<?php
// نظام التوجيه البسيط
$request = $_SERVER['REQUEST_URI'];
$path = parse_url($request, PHP_URL_PATH);



// إزالة مسار المجلد الأساسي
$basePath = '/pos3/';
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

// إزالة الشرطة المائلة
$path = trim($path, '/');



// إذا كان المسار فارغ أو index، عرض صفحة الانطلاق
if (empty($path) || $path === 'index') {
    include 'landing.php';
    exit;
}

// إزالة .php إذا كان موجود
if (substr($path, -4) === '.php') {
    $path = substr($path, 0, -4);
    // إعادة توجيه إلى الرابط النظيف
    header("Location: /pos3/$path", true, 301);
    exit;
}

// قائمة الصفحات المسموحة
$allowedPages = [
    'login', 'logout', 'dashboard', 'pos', 'products', 'categories',
    'units', 'customers', 'suppliers', 'sales', 'purchases',
    'inventory', 'accounting', 'reports', 'settings'
];

// التحقق من وجود الصفحة
if (in_array($path, $allowedPages)) {
    $targetFile = $path . '.php';
    if (file_exists($targetFile)) {
        include $targetFile;
        exit;
    }
}

// صفحة 404
http_response_code(404);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - 404</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            max-width: 500px;
        }
        .error-icon {
            font-size: 5rem;
            color: #e74c3c;
            margin-bottom: 20px;
        }
        .btn-home {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="error-card">
        <i class="bi bi-exclamation-triangle error-icon"></i>
        <h1 class="mb-3">404 - الصفحة غير موجودة</h1>
        <p class="text-muted mb-4">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.
            <br>
            <small>المسار المطلوب: <code><?php echo htmlspecialchars($path); ?></code></small>
        </p>
        <a href="/pos3/" class="btn-home">
            <i class="bi bi-house me-2"></i>
            العودة للصفحة الرئيسية
        </a>
    </div>
</body>
</html>
