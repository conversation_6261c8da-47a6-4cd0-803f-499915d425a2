<?php
require_once 'get_settings.php';

// إعدادات الشركة
$companyName = getSetting('company_name', 'نظام نقاط البيع المتقدم');
$companyLogo = getSetting('company_logo');
$systemVersion = '3.0.0';
$releaseDate = '2024-01-15';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($companyName); ?> - نظام نقاط البيع المتقدم</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.3);
            z-index: 1;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .btn-hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            padding: 15px 40px;
            font-size: 1.1rem;
            border-radius: 50px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-hero:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            color: white;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }
        
        .stats-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 60px 0;
            margin: 60px 0;
        }
        
        .stat-item {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            display: block;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }
        
        .shape {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .tech-badge {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            margin: 5px;
            display: inline-block;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
        }
        
        .footer-section {
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .btn-hero {
                padding: 12px 30px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content" data-aos="fade-right">
                    <?php if (!empty($companyLogo) && file_exists($companyLogo)): ?>
                        <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 120px; max-height: 120px; border-radius: 20px; margin-bottom: 30px;">
                    <?php endif; ?>
                    
                    <h1 class="hero-title">
                        <?php echo htmlspecialchars($companyName); ?>
                    </h1>
                    <p class="hero-subtitle">
                        نظام نقاط البيع المتقدم مع إدارة شاملة للمخزون والمحاسبة والتقارير
                        <br>
                        <strong>الإصدار <?php echo $systemVersion; ?></strong>
                    </p>
                    
                    <div class="mt-4">
                        <a href="login" class="btn-hero">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            دخول النظام
                        </a>
                        <a href="#features" class="btn-hero" style="background: rgba(255,255,255,0.2);">
                            <i class="bi bi-info-circle me-2"></i>
                            اكتشف الميزات
                        </a>
                    </div>
                </div>
                
                <div class="col-lg-6" data-aos="fade-left">
                    <div class="text-center">
                        <i class="bi bi-shop" style="font-size: 15rem; color: rgba(255,255,255,0.3);"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 col-6">
                    <div class="stat-item" data-aos="fade-up" data-aos-delay="100">
                        <span class="stat-number">15+</span>
                        <span class="stat-label">وحدة متكاملة</span>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item" data-aos="fade-up" data-aos-delay="200">
                        <span class="stat-number">100%</span>
                        <span class="stat-label">آمن ومحمي</span>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item" data-aos="fade-up" data-aos-delay="300">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">دعم مستمر</span>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item" data-aos="fade-up" data-aos-delay="400">
                        <span class="stat-number">∞</span>
                        <span class="stat-label">إمكانيات لا محدودة</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">الميزات الرئيسية</h2>

            <div class="row">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                            <i class="bi bi-cart-check"></i>
                        </div>
                        <h4 class="text-center mb-3">نقطة البيع المتقدمة</h4>
                        <p class="text-center text-muted">
                            واجهة سهلة ومتطورة لإدارة المبيعات مع دعم الباركود والطباعة التلقائية للفواتير
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #3498db, #5dade2);">
                            <i class="bi bi-boxes"></i>
                        </div>
                        <h4 class="text-center mb-3">إدارة المخزون الذكية</h4>
                        <p class="text-center text-muted">
                            تتبع دقيق للمخزون مع تنبيهات المخزون المنخفض وإدارة شاملة للمنتجات والفئات
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #e74c3c, #ec7063);">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <h4 class="text-center mb-3">النظام المحاسبي</h4>
                        <p class="text-center text-muted">
                            نظام محاسبي متكامل مع قوائم مالية وتتبع المصروفات والإيرادات بدقة عالية
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #f39c12, #f8c471);">
                            <i class="bi bi-bar-chart"></i>
                        </div>
                        <h4 class="text-center mb-3">التقارير والإحصائيات</h4>
                        <p class="text-center text-muted">
                            تقارير شاملة ومفصلة مع رسوم بيانية تفاعلية لمتابعة الأداء واتخاذ القرارات
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #9b59b6, #bb6bd9);">
                            <i class="bi bi-people"></i>
                        </div>
                        <h4 class="text-center mb-3">إدارة العملاء والموردين</h4>
                        <p class="text-center text-muted">
                            قاعدة بيانات شاملة للعملاء والموردين مع تتبع المعاملات والأرصدة
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #1abc9c, #48c9b0);">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <h4 class="text-center mb-3">الأمان والصلاحيات</h4>
                        <p class="text-center text-muted">
                            نظام صلاحيات متقدم مع حماية شاملة وتشفير البيانات وتسجيل العمليات
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Section -->
    <section class="py-5" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">التقنيات المستخدمة</h2>

            <div class="text-center" data-aos="fade-up" data-aos-delay="200">
                <div class="tech-badge">PHP 7.3+</div>
                <div class="tech-badge">MySQL 8.0</div>
                <div class="tech-badge">Bootstrap 5</div>
                <div class="tech-badge">Chart.js</div>
                <div class="tech-badge">jQuery</div>
                <div class="tech-badge">DataTables</div>
                <div class="tech-badge">SweetAlert2</div>
                <div class="tech-badge">Font Awesome</div>
                <div class="tech-badge">PDO</div>
                <div class="tech-badge">AJAX</div>
                <div class="tech-badge">JSON</div>
                <div class="tech-badge">CSS3</div>
                <div class="tech-badge">HTML5</div>
                <div class="tech-badge">JavaScript ES6</div>
            </div>
        </div>
    </section>

    <!-- Quick Access Section -->
    <section class="py-5">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">الوصول السريع</h2>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="feature-card text-center" data-aos="fade-up" data-aos-delay="200">
                        <h4 class="mb-4">ابدأ الآن مع النظام</h4>
                        <p class="text-muted mb-4">
                            اختر نوع المستخدم للدخول إلى النظام والاستفادة من جميع الميزات المتاحة
                        </p>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="p-3 border rounded">
                                    <i class="bi bi-person-gear fs-1 text-primary mb-2"></i>
                                    <h6>مدير النظام</h6>
                                    <small class="text-muted">صلاحيات كاملة</small>
                                    <br>
                                    <code>admin / admin123</code>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="p-3 border rounded">
                                    <i class="bi bi-person-badge fs-1 text-success mb-2"></i>
                                    <h6>موظف المبيعات</h6>
                                    <small class="text-muted">نقطة البيع والعملاء</small>
                                    <br>
                                    <code>sales_user / sales123</code>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="p-3 border rounded">
                                    <i class="bi bi-person-workspace fs-1 text-warning mb-2"></i>
                                    <h6>أمين المخزن</h6>
                                    <small class="text-muted">إدارة المخزون</small>
                                    <br>
                                    <code>inventory_user / inventory123</code>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <a href="login" class="btn-hero">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                دخول النظام الآن
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer-section">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo htmlspecialchars($companyName); ?></h5>
                    <p class="mb-0">نظام نقاط البيع المتقدم - الإصدار <?php echo $systemVersion; ?></p>
                    <small>تاريخ الإصدار: <?php echo $releaseDate; ?></small>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">جميع الحقوق محفوظة © <?php echo date('Y'); ?></p>
                    <small>تم التطوير بواسطة فريق التطوير المتخصص</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add floating animation to shapes
        const shapes = document.querySelectorAll('.shape');
        shapes.forEach((shape, index) => {
            shape.style.animationDelay = `${index * 2}s`;
        });
    </script>
</body>
</html>
