<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';
require 'check_permissions.php';
require 'apply_permissions.php';
// التحقق من صلاحيات الوصول للصفحة
applyPagePermissions($pdo, $_SESSION['user_id'], 'suppliers.php');


// تحديث جدول الموردين لإضافة الأعمدة المطلوبة
try {
    // التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
    $columns = $pdo->query("SHOW COLUMNS FROM suppliers")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('email', $columns)) {
        $pdo->exec("ALTER TABLE suppliers ADD COLUMN email VARCHAR(255)");
    }
    if (!in_array('contact_person', $columns)) {
        $pdo->exec("ALTER TABLE suppliers ADD COLUMN contact_person VARCHAR(100)");
    }
    if (!in_array('tax_number', $columns)) {
        $pdo->exec("ALTER TABLE suppliers ADD COLUMN tax_number VARCHAR(50)");
    }
    if (!in_array('credit_limit', $columns)) {
        $pdo->exec("ALTER TABLE suppliers ADD COLUMN credit_limit DECIMAL(10,2) DEFAULT 0");
    }
    if (!in_array('payment_terms', $columns)) {
        $pdo->exec("ALTER TABLE suppliers ADD COLUMN payment_terms VARCHAR(100)");
    }
    if (!in_array('notes', $columns)) {
        $pdo->exec("ALTER TABLE suppliers ADD COLUMN notes TEXT");
    }
    if (!in_array('status', $columns)) {
        $pdo->exec("ALTER TABLE suppliers ADD COLUMN status TINYINT(1) DEFAULT 1");
    }
    if (!in_array('created_at', $columns)) {
        $pdo->exec("ALTER TABLE suppliers ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
    }
    if (!in_array('updated_at', $columns)) {
        $pdo->exec("ALTER TABLE suppliers ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
    }
    
    // تحديث الأعمدة الموجودة لتحديد القيم الافتراضية
    $pdo->exec("UPDATE suppliers SET status = 1 WHERE status IS NULL");
    $pdo->exec("UPDATE suppliers SET created_at = NOW() WHERE created_at IS NULL");
    $pdo->exec("UPDATE suppliers SET updated_at = NOW() WHERE updated_at IS NULL");
    
} catch (PDOException $e) {
    // في حالة فشل التحديث، إنشاء الجدول من جديد
    $pdo->exec("CREATE TABLE IF NOT EXISTS suppliers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        contact_person VARCHAR(100),
        phone VARCHAR(20),
        email VARCHAR(255),
        address VARCHAR(255),
        tax_number VARCHAR(50),
        credit_limit DECIMAL(10,2) DEFAULT 0,
        payment_terms VARCHAR(100),
        notes TEXT,
        status TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
}

// معالجة إضافة/تعديل/حذف المورد
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            if ($_POST['action'] === 'add') {
                // التحقق من عدم وجود مورد بنفس الاسم
                $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM suppliers WHERE name = ?");
                $checkStmt->execute([$_POST['name']]);
                if ($checkStmt->fetchColumn() > 0) {
                    $message = 'يوجد مورد بهذا الاسم مسبقاً';
                    $messageType = 'danger';
                } else {
                    $stmt = $pdo->prepare("INSERT INTO suppliers (name, contact_person, phone, email, address, tax_number, credit_limit, payment_terms, notes, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        trim($_POST['name']),
                        trim($_POST['contact_person']),
                        trim($_POST['phone']),
                        trim($_POST['email']),
                        trim($_POST['address']),
                        trim($_POST['tax_number']),
                        floatval($_POST['credit_limit']),
                        trim($_POST['payment_terms']),
                        trim($_POST['notes']),
                        isset($_POST['status']) ? 1 : 0
                    ]);
                    $message = 'تمت إضافة المورد بنجاح';
                    $messageType = 'success';
                }
            } elseif ($_POST['action'] === 'edit') {
                // التحقق من عدم وجود مورد بنفس الاسم (عدا المورد الحالي)
                $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM suppliers WHERE name = ? AND id != ?");
                $checkStmt->execute([$_POST['name'], $_POST['id']]);
                if ($checkStmt->fetchColumn() > 0) {
                    $message = 'يوجد مورد بهذا الاسم مسبقاً';
                    $messageType = 'danger';
                } else {
                    $stmt = $pdo->prepare("UPDATE suppliers SET name = ?, contact_person = ?, phone = ?, email = ?, address = ?, tax_number = ?, credit_limit = ?, payment_terms = ?, notes = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                    $stmt->execute([
                        trim($_POST['name']),
                        trim($_POST['contact_person']),
                        trim($_POST['phone']),
                        trim($_POST['email']),
                        trim($_POST['address']),
                        trim($_POST['tax_number']),
                        floatval($_POST['credit_limit']),
                        trim($_POST['payment_terms']),
                        trim($_POST['notes']),
                        isset($_POST['status']) ? 1 : 0,
                        $_POST['id']
                    ]);
                    $message = 'تم تحديث المورد بنجاح';
                    $messageType = 'success';
                }
            } elseif ($_POST['action'] === 'delete' && isset($_POST['id'])) {
                // التحقق من عدم وجود مشتريات أو منتجات مرتبطة بهذا المورد
                try {
                    $checkPurchases = $pdo->prepare("SELECT COUNT(*) FROM purchases WHERE supplier_id = ?");
                    $checkPurchases->execute([$_POST['id']]);
                    $purchasesCount = $checkPurchases->fetchColumn();
                    
                    $checkProducts = $pdo->prepare("SELECT COUNT(*) FROM products WHERE supplier_id = ?");
                    $checkProducts->execute([$_POST['id']]);
                    $productsCount = $checkProducts->fetchColumn();
                    
                    if ($purchasesCount > 0 || $productsCount > 0) {
                        $message = 'لا يمكن حذف هذا المورد لأنه مرتبط بـ ' . $purchasesCount . ' مشترى و ' . $productsCount . ' منتج';
                        $messageType = 'warning';
                    } else {
                        $stmt = $pdo->prepare("DELETE FROM suppliers WHERE id = ?");
                        $stmt->execute([$_POST['id']]);
                        $message = 'تم حذف المورد بنجاح';
                        $messageType = 'success';
                    }
                } catch (PDOException $e) {
                    // إذا لم تكن الجداول موجودة، السماح بالحذف
                    $stmt = $pdo->prepare("DELETE FROM suppliers WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $message = 'تم حذف المورد بنجاح';
                    $messageType = 'success';
                }
            }
        }
    } catch (PDOException $e) {
        $message = 'حدث خطأ: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// جلب الموردين مع إحصائيات
try {
    $suppliersQuery = "
        SELECT s.*, 
               COUNT(DISTINCT p.id) as products_count,
               COUNT(DISTINCT pu.id) as purchases_count,
               COALESCE(SUM(pu.total), 0) as total_purchases_value
        FROM suppliers s 
        LEFT JOIN products p ON s.id = p.supplier_id 
        LEFT JOIN purchases pu ON s.id = pu.supplier_id AND pu.status = 'completed'
        GROUP BY s.id 
        ORDER BY s.name
    ";
    $suppliers = $pdo->query($suppliersQuery)->fetchAll();
} catch (PDOException $e) {
    // في حالة حدوث خطأ، جلب الموردين بدون إحصائيات
    $suppliers = $pdo->query("SELECT *, 0 as products_count, 0 as purchases_count, 0 as total_purchases_value FROM suppliers ORDER BY name")->fetchAll();
}

// إحصائيات عامة
$totalSuppliers = count($suppliers);
$activeSuppliers = count(array_filter($suppliers, function($supplier) { return $supplier['status'] == 1; }));
$inactiveSuppliers = $totalSuppliers - $activeSuppliers;

// حساب إجمالي قيمة المشتريات
$totalPurchasesValue = 0;
$totalCreditLimit = 0;
foreach ($suppliers as $supplier) {
    $totalPurchasesValue += $supplier['total_purchases_value'];
    $totalCreditLimit += $supplier['credit_limit'];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - نظام نقاط البيع</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-left-color: #fff;
            transform: translateX(-5px);
        }
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .page-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .table-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }
        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 20px 20px 0 0;
        }
        .supplier-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .supplier-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .contact-info {
            font-size: 0.9em;
            color: #6c757d;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            $companyName = getSetting('company_name', 'نظام نقاط البيع');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>
        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link active" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-building me-2"></i>إدارة الموردين</h2>
                    <small class="text-muted">إضافة وتعديل وإدارة معلومات الموردين والشركاء التجاريين</small>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-gradient" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة مورد جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $totalSuppliers; ?></h4>
                            <p class="mb-0 small">إجمالي الموردين</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-building"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #2ecc71);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $activeSuppliers; ?></h4>
                            <p class="mb-0 small">موردين نشطين</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalPurchasesValue, false); ?></h4>
                            <p class="mb-0 small">إجمالي المشتريات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalCreditLimit, false); ?></h4>
                            <p class="mb-0 small">إجمالي حدود الائتمان</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-credit-card"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Suppliers Table -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0"><i class="bi bi-table me-2"></i>قائمة الموردين</h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="exportData()">
                        <i class="bi bi-download me-1"></i>تصدير
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshTable()">
                        <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                    </button>
                </div>
            </div>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="suppliersTable">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="20%">اسم المورد</th>
                                <th width="15%">الشخص المسؤول</th>
                                <th width="12%">الهاتف</th>
                                <th width="15%">البريد الإلكتروني</th>
                                <th width="8%">المنتجات</th>
                                <th width="8%">المشتريات</th>
                                <th width="10%">حد الائتمان</th>
                                <th width="8%">الحالة</th>
                                <th width="15%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($suppliers as $index => $supplier): ?>
                            <tr>
                                <td><?php echo $index + 1; ?></td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($supplier['name']); ?></strong>
                                        <?php if (!empty($supplier['tax_number'])): ?>
                                            <br><small class="text-muted">ض.ب: <?php echo htmlspecialchars($supplier['tax_number']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($supplier['contact_person'])): ?>
                                        <span class="contact-info"><?php echo htmlspecialchars($supplier['contact_person']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($supplier['phone'])): ?>
                                        <a href="tel:<?php echo $supplier['phone']; ?>" class="text-decoration-none">
                                            <i class="bi bi-telephone me-1"></i><?php echo htmlspecialchars($supplier['phone']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($supplier['email'])): ?>
                                        <a href="mailto:<?php echo $supplier['email']; ?>" class="text-decoration-none">
                                            <i class="bi bi-envelope me-1"></i><?php echo htmlspecialchars($supplier['email']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $supplier['products_count']; ?> منتج</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo $supplier['purchases_count']; ?> مشترى</span>
                                </td>
                                <td>
                                    <?php if ($supplier['credit_limit'] > 0): ?>
                                        <strong class="text-warning"><?php echo formatCurrency($supplier['credit_limit']); ?></strong>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge <?php echo $supplier['status'] ? 'bg-success' : 'bg-danger'; ?>">
                                        <?php echo $supplier['status'] ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="viewSupplier(<?php echo htmlspecialchars(json_encode($supplier)); ?>)"
                                                title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success"
                                                onclick="editSupplier(<?php echo htmlspecialchars(json_encode($supplier)); ?>)"
                                                title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="deleteSupplier(<?php echo $supplier['id']; ?>, '<?php echo htmlspecialchars($supplier['name']); ?>')"
                                                title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Add Supplier Modal -->
        <div class="modal fade" id="addSupplierModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-plus-circle me-2"></i>إضافة مورد جديد</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST" id="addSupplierForm">
                        <div class="modal-body">
                            <input type="hidden" name="action" value="add">

                            <!-- Basic Information -->
                            <h6 class="mb-3"><i class="bi bi-info-circle me-2"></i>المعلومات الأساسية</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-building me-1"></i>اسم المورد *</label>
                                    <input type="text" class="form-control" name="name" required
                                           placeholder="أدخل اسم المورد أو الشركة" maxlength="100">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-person me-1"></i>الشخص المسؤول</label>
                                    <input type="text" class="form-control" name="contact_person"
                                           placeholder="اسم الشخص المسؤول" maxlength="100">
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <h6 class="mb-3"><i class="bi bi-telephone me-2"></i>معلومات الاتصال</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-telephone me-1"></i>رقم الهاتف</label>
                                    <input type="text" class="form-control" name="phone"
                                           placeholder="أدخل رقم الهاتف" maxlength="20">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-envelope me-1"></i>البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email"
                                           placeholder="<EMAIL>" maxlength="255">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-geo-alt me-1"></i>العنوان</label>
                                <textarea class="form-control" name="address" rows="2"
                                          placeholder="العنوان الكامل للمورد" maxlength="255"></textarea>
                            </div>

                            <!-- Business Information -->
                            <h6 class="mb-3"><i class="bi bi-briefcase me-2"></i>المعلومات التجارية</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-receipt me-1"></i>الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="tax_number"
                                           placeholder="رقم السجل التجاري أو الضريبي" maxlength="50">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-credit-card me-1"></i>حد الائتمان</label>
                                    <input type="number" class="form-control" name="credit_limit"
                                           placeholder="0.00" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-calendar me-1"></i>شروط الدفع</label>
                                    <input type="text" class="form-control" name="payment_terms"
                                           placeholder="مثال: 30 يوم، نقداً، إلخ" maxlength="100">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-toggles me-1"></i>حالة المورد</label>
                                    <div class="form-check form-switch mt-2">
                                        <input class="form-check-input" type="checkbox" name="status" checked>
                                        <label class="form-check-label">مورد نشط</label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-text-paragraph me-1"></i>ملاحظات</label>
                                <textarea class="form-control" name="notes" rows="3"
                                          placeholder="ملاحظات إضافية عن المورد (اختياري)"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>إلغاء
                            </button>
                            <button type="submit" class="btn btn-gradient">
                                <i class="bi bi-check-circle me-1"></i>حفظ المورد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Edit Supplier Modal -->
        <div class="modal fade" id="editSupplierModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-pencil me-2"></i>تعديل المورد</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST" id="editSupplierForm">
                        <div class="modal-body">
                            <input type="hidden" name="action" value="edit">
                            <input type="hidden" name="id" id="edit_id">

                            <!-- Basic Information -->
                            <h6 class="mb-3"><i class="bi bi-info-circle me-2"></i>المعلومات الأساسية</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-building me-1"></i>اسم المورد *</label>
                                    <input type="text" class="form-control" name="name" id="edit_name" required
                                           placeholder="أدخل اسم المورد أو الشركة" maxlength="100">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-person me-1"></i>الشخص المسؤول</label>
                                    <input type="text" class="form-control" name="contact_person" id="edit_contact_person"
                                           placeholder="اسم الشخص المسؤول" maxlength="100">
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <h6 class="mb-3"><i class="bi bi-telephone me-2"></i>معلومات الاتصال</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-telephone me-1"></i>رقم الهاتف</label>
                                    <input type="text" class="form-control" name="phone" id="edit_phone"
                                           placeholder="أدخل رقم الهاتف" maxlength="20">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-envelope me-1"></i>البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email" id="edit_email"
                                           placeholder="<EMAIL>" maxlength="255">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-geo-alt me-1"></i>العنوان</label>
                                <textarea class="form-control" name="address" id="edit_address" rows="2"
                                          placeholder="العنوان الكامل للمورد" maxlength="255"></textarea>
                            </div>

                            <!-- Business Information -->
                            <h6 class="mb-3"><i class="bi bi-briefcase me-2"></i>المعلومات التجارية</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-receipt me-1"></i>الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="tax_number" id="edit_tax_number"
                                           placeholder="رقم السجل التجاري أو الضريبي" maxlength="50">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-credit-card me-1"></i>حد الائتمان</label>
                                    <input type="number" class="form-control" name="credit_limit" id="edit_credit_limit"
                                           placeholder="0.00" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-calendar me-1"></i>شروط الدفع</label>
                                    <input type="text" class="form-control" name="payment_terms" id="edit_payment_terms"
                                           placeholder="مثال: 30 يوم، نقداً، إلخ" maxlength="100">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-toggles me-1"></i>حالة المورد</label>
                                    <div class="form-check form-switch mt-2">
                                        <input class="form-check-input" type="checkbox" name="status" id="edit_status">
                                        <label class="form-check-label">مورد نشط</label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-text-paragraph me-1"></i>ملاحظات</label>
                                <textarea class="form-control" name="notes" id="edit_notes" rows="3"
                                          placeholder="ملاحظات إضافية عن المورد (اختياري)"></textarea>
                            </div>

                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>معلومة:</strong> تاريخ الإنشاء: <span id="edit_created_at"></span>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>إلغاء
                            </button>
                            <button type="submit" class="btn btn-gradient">
                                <i class="bi bi-check-circle me-1"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Buttons -->
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let suppliersTable;

        $(document).ready(function() {
            // Initialize DataTable
            suppliersTable = $('#suppliersTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[1, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [9] }, // Actions column
                    { searchable: false, targets: [0, 9] } // ID and Actions columns
                ],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="bi bi-file-earmark-excel me-1"></i>Excel',
                        className: 'btn btn-success btn-sm',
                        exportOptions: {
                            columns: [1, 2, 3, 4, 5, 6, 7, 8] // Exclude ID and Actions
                        }
                    }
                ]
            });

            // Form validation
            $('#addSupplierForm').on('submit', function(e) {
                if (!validateSupplierForm(this)) {
                    e.preventDefault();
                    return false;
                }
            });

            $('#editSupplierForm').on('submit', function(e) {
                if (!validateSupplierForm(this)) {
                    e.preventDefault();
                    return false;
                }
            });

            // Clear form when modal is hidden
            $('#addSupplierModal').on('hidden.bs.modal', function() {
                $('#addSupplierForm')[0].reset();
            });

            // Remove automatic phone formatting - let users enter any format they want
            // $('input[type="tel"]').on('input', function() {
            //     // No automatic formatting - users can enter any phone format
            // });
        });

        function validateSupplierForm(form) {
            const name = $(form).find('input[name="name"]').val().trim();
            const phone = $(form).find('input[name="phone"]').val().trim();
            const email = $(form).find('input[name="email"]').val().trim();

            if (name.length < 2) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'يجب أن يكون اسم المورد أكثر من حرفين'
                });
                return false;
            }

            // Remove phone validation - allow any format
            // if (phone && phone.length > 0) {
            //     // No validation - users can enter any phone format they want
            // }

            if (email && !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'البريد الإلكتروني غير صحيح'
                });
                return false;
            }

            return true;
        }

        function viewSupplier(supplier) {
            const purchasesValue = supplier.total_purchases_value || 0;
            const productsCount = supplier.products_count || 0;
            const purchasesCount = supplier.purchases_count || 0;

            Swal.fire({
                title: supplier.name,
                html: `
                    <div class="text-start">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الاتصال</h6>
                                <p><strong>الشخص المسؤول:</strong> ${supplier.contact_person || 'غير محدد'}</p>
                                <p><strong>الهاتف:</strong> ${supplier.phone || 'غير محدد'}</p>
                                <p><strong>البريد الإلكتروني:</strong> ${supplier.email || 'غير محدد'}</p>
                                <p><strong>العنوان:</strong> ${supplier.address || 'غير محدد'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>المعلومات التجارية</h6>
                                <p><strong>الرقم الضريبي:</strong> ${supplier.tax_number || 'غير محدد'}</p>
                                <p><strong>حد الائتمان:</strong> ${supplier.credit_limit > 0 ? supplier.credit_limit : 'غير محدد'}</p>
                                <p><strong>شروط الدفع:</strong> ${supplier.payment_terms || 'غير محدد'}</p>
                                <p><strong>الحالة:</strong> ${supplier.status ? 'نشط' : 'غير نشط'}</p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <h5 class="text-primary">${productsCount}</h5>
                                <small>منتج</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <h5 class="text-success">${purchasesCount}</h5>
                                <small>مشترى</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <h5 class="text-info">${purchasesValue.toFixed(2)}</h5>
                                <small>إجمالي المشتريات</small>
                            </div>
                        </div>
                        ${supplier.notes ? `<hr><p><strong>ملاحظات:</strong> ${supplier.notes}</p>` : ''}
                    </div>
                `,
                width: 700,
                showCloseButton: true,
                showConfirmButton: false
            });
        }

        function editSupplier(supplier) {
            document.getElementById('edit_id').value = supplier.id;
            document.getElementById('edit_name').value = supplier.name || '';
            document.getElementById('edit_contact_person').value = supplier.contact_person || '';
            document.getElementById('edit_phone').value = supplier.phone || '';
            document.getElementById('edit_email').value = supplier.email || '';
            document.getElementById('edit_address').value = supplier.address || '';
            document.getElementById('edit_tax_number').value = supplier.tax_number || '';
            document.getElementById('edit_credit_limit').value = supplier.credit_limit || '';
            document.getElementById('edit_payment_terms').value = supplier.payment_terms || '';
            document.getElementById('edit_notes').value = supplier.notes || '';
            document.getElementById('edit_status').checked = supplier.status == 1;

            const createdAtElement = document.getElementById('edit_created_at');
            if (supplier.created_at && supplier.created_at !== '0000-00-00 00:00:00') {
                createdAtElement.textContent = formatDate(supplier.created_at);
            } else {
                createdAtElement.textContent = 'غير محدد';
            }

            new bootstrap.Modal(document.getElementById('editSupplierModal')).show();
        }

        function deleteSupplier(id, name) {
            Swal.fire({
                title: 'تأكيد الحذف',
                text: `هل أنت متأكد من حذف المورد "${name}"؟`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Create and submit form
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" value="${id}">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

        function refreshTable() {
            location.reload();
        }

        function exportData() {
            suppliersTable.button('.buttons-excel').trigger();
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Add loading animation to buttons
        $('form').on('submit', function() {
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            submitBtn.html('<i class="bi bi-hourglass-split me-1"></i>جاري الحفظ...').prop('disabled', true);

            setTimeout(function() {
                submitBtn.html(originalText).prop('disabled', false);
            }, 3000);
        });
    </script>
</body>
</html>
