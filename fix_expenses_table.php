<?php
// ملف لإصلاح جدول المصروفات وإضافة الأعمدة المفقودة
require 'db.php';

try {
    // التحقق من وجود جدول expenses
    $tableExists = $pdo->query("SHOW TABLES LIKE 'expenses'")->rowCount() > 0;
    
    if (!$tableExists) {
        // إنشاء الجدول كاملاً إذا لم يكن موجود
        $pdo->exec("CREATE TABLE expenses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            expense_number VARCHAR(20) UNIQUE NOT NULL,
            expense_date DATE NOT NULL,
            category ENUM('salary', 'rent', 'utilities', 'supplies', 'maintenance', 'marketing', 'other') DEFAULT 'other',
            description TEXT NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            payment_method ENUM('cash', 'bank', 'check') DEFAULT 'cash',
            account_id INT,
            journal_entry_id INT,
            user_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_date (expense_date),
            INDEX idx_category (category),
            INDEX idx_payment_method (payment_method)
        )");
        echo "تم إنشاء جدول expenses بنجاح!<br>";
    } else {
        // التحقق من وجود الأعمدة المطلوبة وإضافتها إذا لم تكن موجودة
        $columns = $pdo->query("SHOW COLUMNS FROM expenses")->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('category', $columns)) {
            $pdo->exec("ALTER TABLE expenses ADD COLUMN category ENUM('salary', 'rent', 'utilities', 'supplies', 'maintenance', 'marketing', 'other') DEFAULT 'other' AFTER expense_date");
            echo "تم إضافة عمود category إلى جدول expenses<br>";
        }
        
        if (!in_array('expense_number', $columns)) {
            $pdo->exec("ALTER TABLE expenses ADD COLUMN expense_number VARCHAR(20) UNIQUE AFTER id");
            echo "تم إضافة عمود expense_number إلى جدول expenses<br>";
        }
        
        if (!in_array('payment_method', $columns)) {
            $pdo->exec("ALTER TABLE expenses ADD COLUMN payment_method ENUM('cash', 'bank', 'check') DEFAULT 'cash' AFTER amount");
            echo "تم إضافة عمود payment_method إلى جدول expenses<br>";
        }
        
        if (!in_array('account_id', $columns)) {
            $pdo->exec("ALTER TABLE expenses ADD COLUMN account_id INT AFTER payment_method");
            echo "تم إضافة عمود account_id إلى جدول expenses<br>";
        }
        
        if (!in_array('journal_entry_id', $columns)) {
            $pdo->exec("ALTER TABLE expenses ADD COLUMN journal_entry_id INT AFTER account_id");
            echo "تم إضافة عمود journal_entry_id إلى جدول expenses<br>";
        }
        
        if (!in_array('user_id', $columns)) {
            $pdo->exec("ALTER TABLE expenses ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER journal_entry_id");
            echo "تم إضافة عمود user_id إلى جدول expenses<br>";
        }
        
        // إضافة الفهارس إذا لم تكن موجودة
        try {
            $pdo->exec("ALTER TABLE expenses ADD INDEX idx_date (expense_date)");
        } catch (PDOException $e) {
            // الفهرس موجود مسبقاً
        }
        
        try {
            $pdo->exec("ALTER TABLE expenses ADD INDEX idx_category (category)");
        } catch (PDOException $e) {
            // الفهرس موجود مسبقاً
        }
        
        try {
            $pdo->exec("ALTER TABLE expenses ADD INDEX idx_payment_method (payment_method)");
        } catch (PDOException $e) {
            // الفهرس موجود مسبقاً
        }
    }
    
    // التحقق من وجود جدول payroll وإنشاؤه إذا لم يكن موجود
    $payrollExists = $pdo->query("SHOW TABLES LIKE 'payroll'")->rowCount() > 0;
    
    if (!$payrollExists) {
        $pdo->exec("CREATE TABLE payroll (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_name VARCHAR(100) NOT NULL,
            employee_id VARCHAR(20),
            salary_month DATE NOT NULL,
            basic_salary DECIMAL(10,2) NOT NULL,
            allowances DECIMAL(10,2) DEFAULT 0,
            deductions DECIMAL(10,2) DEFAULT 0,
            net_salary DECIMAL(10,2) NOT NULL,
            payment_date DATE,
            payment_method ENUM('cash', 'bank', 'check') DEFAULT 'bank',
            journal_entry_id INT,
            user_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_month (salary_month),
            INDEX idx_employee (employee_id),
            INDEX idx_payment_date (payment_date)
        )");
        echo "تم إنشاء جدول payroll بنجاح!<br>";
    }
    
    // تحديث البيانات الموجودة لتعيين قيم افتراضية للأعمدة الجديدة
    $pdo->exec("UPDATE expenses SET category = 'other' WHERE category IS NULL");
    $pdo->exec("UPDATE expenses SET payment_method = 'cash' WHERE payment_method IS NULL");
    
    // إضافة أرقام للمصروفات التي لا تحتوي على أرقام
    $expensesWithoutNumbers = $pdo->query("SELECT id FROM expenses WHERE expense_number IS NULL OR expense_number = ''")->fetchAll();
    
    if (!empty($expensesWithoutNumbers)) {
        $counter = 1;
        $updateStmt = $pdo->prepare("UPDATE expenses SET expense_number = ? WHERE id = ?");
        
        foreach ($expensesWithoutNumbers as $expense) {
            $expenseNumber = 'EXP' . date('Ymd') . str_pad($counter, 3, '0', STR_PAD_LEFT);
            $updateStmt->execute([$expenseNumber, $expense['id']]);
            $counter++;
        }
        echo "تم تحديث أرقام المصروفات الموجودة<br>";
    }
    
    echo "<br><strong>تم إصلاح جدول المصروفات بنجاح! النظام جاهز للعمل.</strong>";
    
} catch (PDOException $e) {
    echo "خطأ في إصلاح الجدول: " . $e->getMessage();
}
?>
