<?php
// سكريبت لتطبيق الصلاحيات على جميع صفحات النظام
require 'db.php';
require 'check_permissions.php';

// قائمة الصفحات التي تحتاج تطبيق الصلاحيات
$pages = [
    'dashboard.php' => [],
    'products.php' => ['products.view'],
    'categories.php' => ['products.view'],
    'units.php' => ['products.view'],
    'pos.php' => ['pos.access'],
    'sales.php' => ['sales.view'],
    'purchases.php' => ['inventory.view'],
    'inventory.php' => ['inventory.view'],
    'customers.php' => ['customers.view'],
    'suppliers.php' => ['suppliers.view'],
    'accounting.php' => ['accounting.view'],
    'reports.php' => ['reports.view'],
    'settings.php' => ['settings.view']
];

function updatePageWithPermissions($filename, $requiredPermissions) {
    if (!file_exists($filename)) {
        echo "الملف غير موجود: $filename\n";
        return false;
    }
    
    $content = file_get_contents($filename);
    
    // التحقق من وجود require للصلاحيات
    if (strpos($content, "require 'check_permissions.php';") === false) {
        // إضافة require للصلاحيات بعد require 'get_settings.php';
        $content = str_replace(
            "require 'get_settings.php';",
            "require 'get_settings.php';\nrequire 'check_permissions.php';\nrequire 'apply_permissions.php';",
            $content
        );
        
        // إضافة التحقق من الصلاحيات
        if (!empty($requiredPermissions)) {
            $permissionCheck = "\n// التحقق من صلاحيات الوصول للصفحة\napplyPagePermissions(\$pdo, \$_SESSION['user_id'], '$filename');\n";
            $content = str_replace(
                "require 'apply_permissions.php';",
                "require 'apply_permissions.php';" . $permissionCheck,
                $content
            );
        }
    }
    
    // تحديث الشريط الجانبي ليستخدم الملف الموحد
    $sidebarPattern = '/<!-- Sidebar -->.*?<\/div>/s';
    if (preg_match($sidebarPattern, $content)) {
        $newSidebar = "<?php \n    require 'sidebar.php';\n    renderSidebar(\$pdo, \$_SESSION['user_id'], '$filename'); \n    ?>";
        $content = preg_replace($sidebarPattern, $newSidebar, $content);
    }
    
    // إضافة CSS الموحد
    if (strpos($content, 'renderCommonCSS()') === false) {
        $cssPattern = '/<style>.*?<\/style>/s';
        if (preg_match($cssPattern, $content)) {
            $content = preg_replace($cssPattern, "<?php renderCommonCSS(); ?>", $content, 1);
        }
    }
    
    // إضافة JavaScript للصلاحيات قبل </body>
    if (strpos($content, 'renderCommonJS(') === false) {
        $content = str_replace(
            '</body>',
            "    <?php renderCommonJS(\$pdo, \$_SESSION['user_id']); ?>\n</body>",
            $content
        );
    }
    
    // حفظ الملف المحدث
    if (file_put_contents($filename, $content)) {
        echo "تم تحديث الملف: $filename\n";
        return true;
    } else {
        echo "فشل في تحديث الملف: $filename\n";
        return false;
    }
}

// تطبيق التحديثات على جميع الصفحات
echo "بدء تطبيق الصلاحيات على جميع صفحات النظام...\n\n";

foreach ($pages as $page => $permissions) {
    echo "معالجة الصفحة: $page\n";
    updatePageWithPermissions($page, $permissions);
    echo "---\n";
}

// إنشاء ملف تحديث خاص للأزرار والعمليات
function addPermissionChecksToButtons($filename, $buttonPermissions) {
    if (!file_exists($filename)) {
        return false;
    }
    
    $content = file_get_contents($filename);
    
    // تحديث أزرار الإضافة
    foreach ($buttonPermissions as $permission => $buttonText) {
        $oldButton = '<button type="button" class="btn btn-gradient"';
        $newButton = '<?php if (hasPermission($pdo, $_SESSION[\'user_id\'], \'' . $permission . '\') || isAdmin($pdo, $_SESSION[\'user_id\'])): ?>
                    <button type="button" class="btn btn-gradient"';
        
        if (strpos($content, $buttonText) !== false && strpos($content, $oldButton) !== false) {
            $content = str_replace($oldButton, $newButton, $content);
            
            // إضافة نهاية الشرط
            $endButton = '</button>';
            $newEndButton = '</button>
                    <?php else: ?>
                    <button type="button" class="btn btn-secondary" disabled title="ليس لديك صلاحية">
                        <i class="bi bi-lock me-2"></i>غير مصرح
                    </button>
                    <?php endif; ?>';
            
            $pos = strpos($content, $endButton);
            if ($pos !== false) {
                $content = substr_replace($content, $newEndButton, $pos, strlen($endButton));
            }
        }
    }
    
    return file_put_contents($filename, $content);
}

// تحديث أزرار محددة في صفحات معينة
$buttonUpdates = [
    'products.php' => [
        'products.create' => 'إضافة منتج جديد',
        'products.edit' => 'تعديل',
        'products.delete' => 'حذف'
    ],
    'customers.php' => [
        'customers.create' => 'إضافة عميل جديد',
        'customers.edit' => 'تعديل',
        'customers.delete' => 'حذف'
    ],
    'suppliers.php' => [
        'suppliers.create' => 'إضافة مورد جديد',
        'suppliers.edit' => 'تعديل',
        'suppliers.delete' => 'حذف'
    ]
];

echo "\nتحديث أزرار العمليات...\n";
foreach ($buttonUpdates as $page => $buttons) {
    echo "تحديث أزرار الصفحة: $page\n";
    addPermissionChecksToButtons($page, $buttons);
}

// إنشاء ملف اختبار الصلاحيات
$testContent = '<?php
session_start();
if (!isset($_SESSION[\'user_id\'])) {
    header(\'Location: login.php\');
    exit;
}

require \'db.php\';
require \'check_permissions.php\';

$userId = $_SESSION[\'user_id\'];
$permissions = getUserPermissions($pdo, $userId);
$isAdmin = isAdmin($pdo, $userId);
$userRole = getUserRole($pdo, $userId);

echo "<h2>اختبار الصلاحيات للمستخدم: " . $_SESSION[\'username\'] . "</h2>";
echo "<p><strong>الدور:</strong> " . ($userRole[\'display_name\'] ?? \'غير محدد\') . "</p>";
echo "<p><strong>مدير النظام:</strong> " . ($isAdmin ? \'نعم\' : \'لا\') . "</p>";

echo "<h3>الصلاحيات المتاحة:</h3>";
if (!empty($permissions)) {
    echo "<ul>";
    foreach ($permissions as $permission) {
        echo "<li>" . $permission[\'display_name\'] . " (" . $permission[\'name\'] . ")</li>";
    }
    echo "</ul>";
} else {
    echo "<p>لا توجد صلاحيات محددة</p>";
}

echo "<h3>اختبار الصفحات:</h3>";
$testPages = [
    \'products.php\' => \'products.view\',
    \'pos.php\' => \'pos.access\',
    \'sales.php\' => \'sales.view\',
    \'settings.php\' => \'settings.view\'
];

echo "<ul>";
foreach ($testPages as $page => $permission) {
    $hasAccess = hasPermission($pdo, $userId, $permission) || $isAdmin;
    $status = $hasAccess ? \'<span style="color: green;">✓ مسموح</span>\' : \'<span style="color: red;">✗ غير مسموح</span>\';
    echo "<li><a href=\"$page\">$page</a> - $status</li>";
}
echo "</ul>";
?>';

file_put_contents('test_permissions.php', $testContent);

echo "\nتم إنشاء ملف اختبار الصلاحيات: test_permissions.php\n";
echo "\nتم الانتهاء من تطبيق الصلاحيات على جميع صفحات النظام!\n";
echo "\nيمكنك الآن:\n";
echo "1. اختبار الصلاحيات من خلال: test_permissions.php\n";
echo "2. إدارة المستخدمين والأدوار من: settings.php\n";
echo "3. مراجعة سجل العمليات في قاعدة البيانات\n";
?>
