<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';

// إنشاء جداول الإرجاعات إذا لم تكن موجودة
try {
    // جدول الإرجاعات الرئيسي
    $pdo->exec("CREATE TABLE IF NOT EXISTS returns (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type ENUM('sale', 'purchase') DEFAULT 'sale',
        reference_id INT NOT NULL,
        total DECIMAL(10,2) NOT NULL,
        notes TEXT,
        user_id INT,
        return_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_reference (type, reference_id),
        INDEX idx_date (return_date)
    )");

    // جدول عناصر الإرجاع
    $pdo->exec("CREATE TABLE IF NOT EXISTS return_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        return_id INT NOT NULL,
        product_id INT NOT NULL,
        quantity INT NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (return_id) REFERENCES returns(id) ON DELETE CASCADE,
        INDEX idx_return (return_id),
        INDEX idx_product (product_id)
    )");

} catch (PDOException $e) {
    // في حالة فشل إنشاء الجداول، المتابعة بدون إرجاعات
}

// معالجة طلب مراجعة الفاتورة بالباركود
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'find_sale') {
            $barcode = trim($_POST['barcode']);
            
            // استخراج رقم الفاتورة من الباركود
            if (strpos($barcode, 'SALE') === 0) {
                $saleId = intval(substr($barcode, 4));
            } else {
                $saleId = intval($barcode);
            }
            
            if ($saleId <= 0) {
                throw new Exception('رقم فاتورة غير صحيح');
            }
            
            // البحث عن الفاتورة
            $saleStmt = $pdo->prepare("
                SELECT s.*, 
                       c.name as customer_name,
                       c.phone as customer_phone,
                       u.name as user_name
                FROM sales s 
                LEFT JOIN customers c ON s.customer_id = c.id 
                LEFT JOIN users u ON s.user_id = u.id 
                WHERE s.id = ?
            ");
            $saleStmt->execute([$saleId]);
            $sale = $saleStmt->fetch();
            
            if (!$sale) {
                throw new Exception('الفاتورة غير موجودة');
            }
            
            // جلب عناصر الفاتورة
            $itemsStmt = $pdo->prepare("
                SELECT si.*, p.name as product_name, p.code as product_code,
                       p.quantity as current_stock, u.name as unit_name
                FROM sale_items si 
                LEFT JOIN products p ON si.product_id = p.id 
                LEFT JOIN units u ON p.unit_id = u.id
                WHERE si.sale_id = ?
                ORDER BY p.name
            ");
            $itemsStmt->execute([$saleId]);
            $items = $itemsStmt->fetchAll();
            
            echo json_encode([
                'success' => true,
                'sale' => $sale,
                'items' => $items
            ]);
            exit;
            
        } elseif ($_POST['action'] === 'return_sale') {
            $saleId = intval($_POST['sale_id']);
            $returnItems = json_decode($_POST['return_items'], true);
            $returnReason = trim($_POST['return_reason']);
            
            if (empty($returnItems)) {
                throw new Exception('لا توجد منتجات للإرجاع');
            }
            
            $pdo->beginTransaction();
            
            // حساب إجمالي الإرجاع
            $returnTotal = 0;
            foreach ($returnItems as $item) {
                $returnTotal += $item['quantity'] * $item['unit_price'];
            }
            
            // إدراج عملية الإرجاع
            $returnStmt = $pdo->prepare("INSERT INTO returns (type, reference_id, total, notes, user_id, return_date) VALUES ('sale', ?, ?, ?, ?, NOW())");
            $returnStmt->execute([$saleId, $returnTotal, $returnReason, $_SESSION['user_id']]);
            
            $returnId = $pdo->lastInsertId();
            
            // إدراج عناصر الإرجاع وتحديث المخزون
            $returnItemStmt = $pdo->prepare("INSERT INTO return_items (return_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
            $stockStmt = $pdo->prepare("UPDATE products SET quantity = quantity + ? WHERE id = ?");
            $movementStmt = $pdo->prepare("INSERT INTO stock_movements (product_id, type, reference_id, quantity, user_id) VALUES (?, 'return', ?, ?, ?)");
            
            foreach ($returnItems as $item) {
                // إدراج عنصر الإرجاع
                $returnItemStmt->execute([$returnId, $item['product_id'], $item['quantity'], $item['unit_price']]);
                
                // تحديث المخزون
                $stockStmt->execute([$item['quantity'], $item['product_id']]);
                
                // إضافة حركة مخزون
                $movementStmt->execute([$item['product_id'], $returnId, $item['quantity'], $_SESSION['user_id']]);
            }
            
            $pdo->commit();
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إرجاع المنتجات بنجاح',
                'return_id' => $returnId,
                'return_total' => $returnTotal
            ]);
            exit;
            
        } elseif ($_POST['action'] === 'edit_sale') {
            // ميزة تعديل الفاتورة (يمكن تطويرها لاحقاً)
            echo json_encode([
                'success' => false,
                'message' => 'ميزة تعديل الفاتورة قيد التطوير'
            ]);
            exit;
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
        exit;
    }
}

// إعدادات النظام
$currency = getSetting('currency', 'ر.س');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراجعة الفاتورة - نظام نقاط البيع</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .review-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 20px;
        }
        
        .barcode-scanner {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .barcode-input {
            font-size: 1.3rem;
            padding: 15px 20px;
            border: 3px solid #e9ecef;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        
        .barcode-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }
        
        .sale-details {
            display: none;
        }
        
        .item-row {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
        }
        
        .return-checkbox {
            transform: scale(1.5);
            margin-left: 10px;
        }
        
        .quantity-input {
            width: 80px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 5px;
        }
        
        .btn-action {
            padding: 12px 25px;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <div class="review-container">
        <!-- Header -->
        <div class="text-center mb-4">
            <h2 class="text-white mb-3">
                <i class="bi bi-receipt me-2"></i>مراجعة الفاتورة
            </h2>
            <div class="d-flex justify-content-center gap-3">
                <a href="pos.php" class="btn btn-light">
                    <i class="bi bi-cart me-1"></i>نقطة البيع
                </a>
                <a href="sales.php" class="btn btn-light">
                    <i class="bi bi-graph-up me-1"></i>سجل المبيعات
                </a>
                <a href="dashboard.php" class="btn btn-warning">
                    <i class="bi bi-house me-1"></i>الرئيسية
                </a>
            </div>
        </div>

        <!-- Barcode Scanner -->
        <div class="barcode-scanner">
            <div class="text-center mb-4">
                <h4><i class="bi bi-upc-scan me-2"></i>مسح باركود الفاتورة</h4>
                <p class="text-muted">امسح باركود الفاتورة أو أدخل رقم الفاتورة للمراجعة</p>
            </div>

            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-upc-scan"></i>
                        </span>
                        <input type="text" class="form-control barcode-input" id="barcodeInput"
                               placeholder="امسح الباركود أو أدخل رقم الفاتورة..."
                               autocomplete="off" autofocus>
                        <button class="btn btn-primary btn-action" type="button" onclick="findSale()">
                            <i class="bi bi-search me-1"></i>بحث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sale Details -->
        <div class="sale-details" id="saleDetails">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-receipt me-2"></i>تفاصيل الفاتورة
                        </h5>
                        <span class="badge bg-light text-dark" id="saleNumber"></span>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Sale Info -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <strong>التاريخ:</strong><br>
                            <span id="saleDate" class="text-muted"></span>
                        </div>
                        <div class="col-md-3">
                            <strong>الكاشير:</strong><br>
                            <span id="saleUser" class="text-muted"></span>
                        </div>
                        <div class="col-md-3">
                            <strong>العميل:</strong><br>
                            <span id="saleCustomer" class="text-muted"></span>
                        </div>
                        <div class="col-md-3">
                            <strong>طريقة الدفع:</strong><br>
                            <span id="salePayment" class="text-muted"></span>
                        </div>
                    </div>

                    <!-- Items -->
                    <h6 class="mb-3"><i class="bi bi-list me-2"></i>المنتجات</h6>
                    <div id="saleItems"></div>

                    <!-- Totals -->
                    <div class="row mt-4">
                        <div class="col-md-6 ms-auto">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>المجموع الفرعي:</span>
                                        <span id="saleSubtotal"></span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>الخصم:</span>
                                        <span id="saleDiscount" class="text-danger"></span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>الضريبة:</span>
                                        <span id="saleTax"></span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between">
                                        <strong>الإجمالي:</strong>
                                        <strong id="saleTotal" class="text-success"></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="d-flex gap-3 justify-content-center">
                                <button type="button" class="btn btn-primary btn-action" onclick="printSale()">
                                    <i class="bi bi-printer me-1"></i>طباعة الفاتورة
                                </button>
                                <button type="button" class="btn btn-warning btn-action" onclick="showReturnModal()">
                                    <i class="bi bi-arrow-return-left me-1"></i>إرجاع منتجات
                                </button>
                                <button type="button" class="btn btn-info btn-action" onclick="showEditModal()">
                                    <i class="bi bi-pencil me-1"></i>تعديل الفاتورة
                                </button>
                                <button type="button" class="btn btn-secondary btn-action" onclick="clearSale()">
                                    <i class="bi bi-x-circle me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Return Modal -->
    <div class="modal fade" id="returnModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="bi bi-arrow-return-left me-2"></i>إرجاع منتجات
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        اختر المنتجات المراد إرجاعها وحدد الكمية لكل منتج
                    </div>

                    <div class="mb-3">
                        <label class="form-label">سبب الإرجاع</label>
                        <select class="form-select" id="returnReason">
                            <option value="عيب في المنتج">عيب في المنتج</option>
                            <option value="منتج خاطئ">منتج خاطئ</option>
                            <option value="عدم رضا العميل">عدم رضا العميل</option>
                            <option value="انتهاء صلاحية">انتهاء صلاحية</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>

                    <div id="returnItems"></div>

                    <div class="mt-3">
                        <div class="d-flex justify-content-between">
                            <strong>إجمالي الإرجاع:</strong>
                            <strong id="returnTotal" class="text-danger">0.00 <?php echo $currency; ?></strong>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-warning" onclick="processReturn()">
                        <i class="bi bi-check-circle me-1"></i>تأكيد الإرجاع
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-pencil me-2"></i>تعديل الفاتورة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        ميزة تعديل الفاتورة قيد التطوير وستتوفر قريباً
                    </div>
                    <p>الميزات المخططة:</p>
                    <ul>
                        <li>تعديل كميات المنتجات</li>
                        <li>إضافة منتجات جديدة</li>
                        <li>تعديل الخصومات</li>
                        <li>تغيير معلومات العميل</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let currentSale = null;
        let currentItems = [];
        let currency = '<?php echo $currency; ?>';

        $(document).ready(function() {
            // Barcode input handler
            $('#barcodeInput').on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    findSale();
                }
            });

            // Auto-focus barcode input
            $('#barcodeInput').focus();
        });

        function findSale() {
            const barcode = $('#barcodeInput').val().trim();
            if (!barcode) {
                Swal.fire('خطأ', 'يرجى إدخال رقم الفاتورة أو مسح الباركود', 'error');
                return;
            }

            // Show loading
            Swal.fire({
                title: 'جاري البحث...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.post('review_sale.php', {
                action: 'find_sale',
                barcode: barcode
            })
            .done(function(response) {
                const result = JSON.parse(response);
                if (result.success) {
                    currentSale = result.sale;
                    currentItems = result.items;
                    displaySaleDetails();
                    Swal.close();
                } else {
                    Swal.fire('خطأ', result.message, 'error');
                }
            })
            .fail(function() {
                Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
            });
        }

        function displaySaleDetails() {
            // Sale info
            $('#saleNumber').text('#' + String(currentSale.id).padStart(6, '0'));
            $('#saleDate').text(new Date(currentSale.sale_date).toLocaleString('ar-SA'));
            $('#saleUser').text(currentSale.user_name || 'غير محدد');
            $('#saleCustomer').text(currentSale.customer_name || 'عميل عادي');

            // Payment method
            let paymentText = 'نقدي';
            switch (currentSale.payment_method) {
                case 'card': paymentText = 'بطاقة ائتمان'; break;
                case 'transfer': paymentText = 'تحويل بنكي'; break;
            }
            $('#salePayment').text(paymentText);

            // Items
            let itemsHtml = '';
            let subtotal = 0;

            currentItems.forEach((item, index) => {
                const itemTotal = item.quantity * item.unit_price;
                subtotal += itemTotal;

                itemsHtml += `
                    <div class="item-row">
                        <div class="row align-items-center">
                            <div class="col-md-5">
                                <strong>${item.product_name}</strong>
                                ${item.product_code ? `<br><small class="text-muted">كود: ${item.product_code}</small>` : ''}
                            </div>
                            <div class="col-md-2 text-center">
                                <span class="badge bg-primary">${item.quantity} ${item.unit_name || ''}</span>
                            </div>
                            <div class="col-md-2 text-center">
                                ${parseFloat(item.unit_price).toFixed(2)} ${currency}
                            </div>
                            <div class="col-md-2 text-center">
                                <strong>${itemTotal.toFixed(2)} ${currency}</strong>
                            </div>
                            <div class="col-md-1 text-center">
                                <small class="text-muted">المخزون: ${item.current_stock || 0}</small>
                            </div>
                        </div>
                    </div>
                `;
            });

            $('#saleItems').html(itemsHtml);

            // Totals
            $('#saleSubtotal').text(subtotal.toFixed(2) + ' ' + currency);
            $('#saleDiscount').text('-' + parseFloat(currentSale.discount || 0).toFixed(2) + ' ' + currency);
            $('#saleTax').text(parseFloat(currentSale.tax || 0).toFixed(2) + ' ' + currency);
            $('#saleTotal').text(parseFloat(currentSale.total).toFixed(2) + ' ' + currency);

            // Show details
            $('#saleDetails').addClass('fade-in').show();
        }

        function showReturnModal() {
            if (!currentSale) return;

            let returnHtml = '';
            currentItems.forEach((item, index) => {
                returnHtml += `
                    <div class="item-row">
                        <div class="row align-items-center">
                            <div class="col-md-1">
                                <input type="checkbox" class="form-check-input return-checkbox"
                                       id="return_${index}" onchange="updateReturnTotal()">
                            </div>
                            <div class="col-md-5">
                                <label for="return_${index}" class="form-check-label">
                                    <strong>${item.product_name}</strong>
                                    ${item.product_code ? `<br><small class="text-muted">كود: ${item.product_code}</small>` : ''}
                                </label>
                            </div>
                            <div class="col-md-2">
                                <span class="text-muted">الكمية الأصلية: ${item.quantity}</span>
                            </div>
                            <div class="col-md-2">
                                <input type="number" class="form-control quantity-input"
                                       id="quantity_${index}" value="${item.quantity}"
                                       min="1" max="${item.quantity}"
                                       onchange="updateReturnTotal()" disabled>
                            </div>
                            <div class="col-md-2 text-end">
                                <span class="fw-bold" id="itemTotal_${index}">
                                    ${(item.quantity * item.unit_price).toFixed(2)} ${currency}
                                </span>
                            </div>
                        </div>
                    </div>
                `;
            });

            $('#returnItems').html(returnHtml);
            $('#returnTotal').text('0.00 ' + currency);
            $('#returnModal').modal('show');
        }

        function updateReturnTotal() {
            let total = 0;

            currentItems.forEach((item, index) => {
                const checkbox = document.getElementById(`return_${index}`);
                const quantityInput = document.getElementById(`quantity_${index}`);

                if (checkbox.checked) {
                    quantityInput.disabled = false;
                    const quantity = parseInt(quantityInput.value) || 0;
                    const itemTotal = quantity * item.unit_price;
                    total += itemTotal;
                    document.getElementById(`itemTotal_${index}`).textContent = itemTotal.toFixed(2) + ' ' + currency;
                } else {
                    quantityInput.disabled = true;
                    quantityInput.value = item.quantity;
                    document.getElementById(`itemTotal_${index}`).textContent = (item.quantity * item.unit_price).toFixed(2) + ' ' + currency;
                }
            });

            $('#returnTotal').text(total.toFixed(2) + ' ' + currency);
        }

        function processReturn() {
            const returnItems = [];
            let hasSelectedItems = false;

            currentItems.forEach((item, index) => {
                const checkbox = document.getElementById(`return_${index}`);
                if (checkbox.checked) {
                    const quantity = parseInt(document.getElementById(`quantity_${index}`).value) || 0;
                    if (quantity > 0) {
                        returnItems.push({
                            product_id: item.product_id,
                            quantity: quantity,
                            unit_price: item.unit_price
                        });
                        hasSelectedItems = true;
                    }
                }
            });

            if (!hasSelectedItems) {
                Swal.fire('تنبيه', 'يرجى اختيار المنتجات المراد إرجاعها', 'warning');
                return;
            }

            const returnReason = $('#returnReason').val();

            Swal.fire({
                title: 'تأكيد الإرجاع',
                text: 'هل أنت متأكد من إرجاع المنتجات المحددة؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، أرجع',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'جاري معالجة الإرجاع...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    $.post('review_sale.php', {
                        action: 'return_sale',
                        sale_id: currentSale.id,
                        return_items: JSON.stringify(returnItems),
                        return_reason: returnReason
                    })
                    .done(function(response) {
                        const result = JSON.parse(response);
                        if (result.success) {
                            $('#returnModal').modal('hide');
                            Swal.fire({
                                icon: 'success',
                                title: 'تم الإرجاع بنجاح',
                                html: `
                                    <p>${result.message}</p>
                                    <div class="alert alert-info">
                                        <strong>رقم الإرجاع:</strong> #${result.return_id}<br>
                                        <strong>قيمة الإرجاع:</strong> ${result.return_total.toFixed(2)} ${currency}
                                    </div>
                                `,
                                confirmButtonText: 'موافق'
                            }).then(() => {
                                // Refresh sale details
                                findSale();
                            });
                        } else {
                            Swal.fire('خطأ', result.message, 'error');
                        }
                    })
                    .fail(function() {
                        Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                    });
                }
            });
        }

        function showEditModal() {
            $('#editModal').modal('show');
        }

        function printSale() {
            if (!currentSale) return;

            window.open(`get_receipt.php?id=${currentSale.id}`, '_blank');
        }

        function clearSale() {
            currentSale = null;
            currentItems = [];
            $('#saleDetails').hide();
            $('#barcodeInput').val('').focus();
        }

        // Auto-focus barcode input when modals are closed
        $('.modal').on('hidden.bs.modal', function() {
            $('#barcodeInput').focus();
        });
    </script>
</body>
</html>
