<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'generate_cash_flow') {
            $dateFrom = $_POST['date_from'];
            $dateTo = $_POST['date_to'];
            
            // الأنشطة التشغيلية
            $operatingActivities = getOperatingActivities($pdo, $dateFrom, $dateTo);
            
            // الأنشطة الاستثمارية
            $investingActivities = getInvestingActivities($pdo, $dateFrom, $dateTo);
            
            // الأنشطة التمويلية
            $financingActivities = getFinancingActivities($pdo, $dateFrom, $dateTo);
            
            // النقدية في بداية ونهاية الفترة
            $beginningCash = getCashBalance($pdo, $dateFrom, true);
            $endingCash = getCashBalance($pdo, $dateTo, false);
            
            // حساب صافي التغير في النقدية
            $netCashFromOperating = array_sum(array_column($operatingActivities, 'amount'));
            $netCashFromInvesting = array_sum(array_column($investingActivities, 'amount'));
            $netCashFromFinancing = array_sum(array_column($financingActivities, 'amount'));
            
            $netChangeInCash = $netCashFromOperating + $netCashFromInvesting + $netCashFromFinancing;
            $calculatedEndingCash = $beginningCash + $netChangeInCash;
            
            // تحليل إضافي
            $analysis = getCashFlowAnalysis($pdo, $dateFrom, $dateTo);
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'operating_activities' => $operatingActivities,
                    'investing_activities' => $investingActivities,
                    'financing_activities' => $financingActivities,
                    'net_cash_from_operating' => $netCashFromOperating,
                    'net_cash_from_investing' => $netCashFromInvesting,
                    'net_cash_from_financing' => $netCashFromFinancing,
                    'net_change_in_cash' => $netChangeInCash,
                    'beginning_cash' => $beginningCash,
                    'ending_cash' => $endingCash,
                    'calculated_ending_cash' => $calculatedEndingCash,
                    'date_from' => $dateFrom,
                    'date_to' => $dateTo,
                    'analysis' => $analysis
                ]
            ]);
            exit;
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

function getOperatingActivities($pdo, $dateFrom, $dateTo) {
    $activities = [];
    
    // صافي الدخل
    $netIncomeStmt = $pdo->prepare("
        SELECT 
            COALESCE(SUM(CASE WHEN coa.account_type = 'revenue' THEN jed.credit - jed.debit ELSE 0 END), 0) -
            COALESCE(SUM(CASE WHEN coa.account_type = 'expense' THEN jed.debit - jed.credit ELSE 0 END), 0) as net_income
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
        WHERE coa.account_type IN ('revenue', 'expense')
        AND je.entry_date BETWEEN ? AND ?
        AND je.status = 'posted'
    ");
    $netIncomeStmt->execute([$dateFrom, $dateTo]);
    $netIncome = $netIncomeStmt->fetchColumn() ?: 0;
    
    $activities[] = [
        'description' => 'صافي الدخل',
        'amount' => $netIncome,
        'type' => 'income'
    ];
    
    // التغيرات في رأس المال العامل
    
    // تغير في المخزون
    $inventoryChange = getAccountChange($pdo, '1120', $dateFrom, $dateTo);
    if ($inventoryChange != 0) {
        $activities[] = [
            'description' => 'التغير في المخزون',
            'amount' => -$inventoryChange, // زيادة المخزون = تدفق نقدي خارج
            'type' => 'working_capital'
        ];
    }
    
    // تغير في العملاء
    $customersChange = getAccountChange($pdo, '1130', $dateFrom, $dateTo);
    if ($customersChange != 0) {
        $activities[] = [
            'description' => 'التغير في العملاء',
            'amount' => -$customersChange, // زيادة العملاء = تدفق نقدي خارج
            'type' => 'working_capital'
        ];
    }
    
    // تغير في الموردين
    $suppliersChange = getAccountChange($pdo, '2110', $dateFrom, $dateTo);
    if ($suppliersChange != 0) {
        $activities[] = [
            'description' => 'التغير في الموردين',
            'amount' => $suppliersChange, // زيادة الموردين = تدفق نقدي داخل
            'type' => 'working_capital'
        ];
    }
    
    // المبيعات النقدية
    $cashSalesStmt = $pdo->prepare("
        SELECT COALESCE(SUM(total), 0) as cash_sales
        FROM sales 
        WHERE payment_method = 'cash'
        AND DATE(sale_date) BETWEEN ? AND ?
    ");
    $cashSalesStmt->execute([$dateFrom, $dateTo]);
    $cashSales = $cashSalesStmt->fetchColumn() ?: 0;
    
    if ($cashSales > 0) {
        $activities[] = [
            'description' => 'المبيعات النقدية',
            'amount' => $cashSales,
            'type' => 'cash_sales'
        ];
    }
    
    // المشتريات النقدية
    $cashPurchasesStmt = $pdo->prepare("
        SELECT COALESCE(SUM(amount), 0) as cash_expenses
        FROM expenses 
        WHERE payment_method = 'cash'
        AND DATE(expense_date) BETWEEN ? AND ?
    ");
    $cashPurchasesStmt->execute([$dateFrom, $dateTo]);
    $cashExpenses = $cashPurchasesStmt->fetchColumn() ?: 0;
    
    if ($cashExpenses > 0) {
        $activities[] = [
            'description' => 'المصروفات النقدية',
            'amount' => -$cashExpenses,
            'type' => 'cash_expenses'
        ];
    }
    
    // الرواتب المدفوعة
    $payrollStmt = $pdo->prepare("
        SELECT COALESCE(SUM(net_salary), 0) as total_payroll
        FROM payroll 
        WHERE DATE(payment_date) BETWEEN ? AND ?
    ");
    $payrollStmt->execute([$dateFrom, $dateTo]);
    $totalPayroll = $payrollStmt->fetchColumn() ?: 0;
    
    if ($totalPayroll > 0) {
        $activities[] = [
            'description' => 'الرواتب المدفوعة',
            'amount' => -$totalPayroll,
            'type' => 'payroll'
        ];
    }
    
    return $activities;
}

function getInvestingActivities($pdo, $dateFrom, $dateTo) {
    $activities = [];
    
    // شراء الأصول الثابتة (تقدير بناءً على التغير في حسابات الأصول الثابتة)
    $fixedAssetsChange = 0;
    $fixedAssetsStmt = $pdo->prepare("
        SELECT COALESCE(SUM(jed.debit - jed.credit), 0) as change_amount
        FROM chart_of_accounts coa
        JOIN journal_entry_details jed ON coa.id = jed.account_id
        JOIN journal_entries je ON jed.journal_entry_id = je.id
        WHERE coa.account_code LIKE '12%' 
        AND coa.account_type = 'asset'
        AND je.entry_date BETWEEN ? AND ?
        AND je.status = 'posted'
    ");
    $fixedAssetsStmt->execute([$dateFrom, $dateTo]);
    $fixedAssetsChange = $fixedAssetsStmt->fetchColumn() ?: 0;
    
    if ($fixedAssetsChange > 0) {
        $activities[] = [
            'description' => 'شراء أصول ثابتة',
            'amount' => -$fixedAssetsChange,
            'type' => 'fixed_assets'
        ];
    } elseif ($fixedAssetsChange < 0) {
        $activities[] = [
            'description' => 'بيع أصول ثابتة',
            'amount' => -$fixedAssetsChange,
            'type' => 'fixed_assets'
        ];
    }
    
    return $activities;
}

function getFinancingActivities($pdo, $dateFrom, $dateTo) {
    $activities = [];
    
    // التغير في رأس المال
    $capitalChange = getAccountChange($pdo, '3100', $dateFrom, $dateTo);
    if ($capitalChange > 0) {
        $activities[] = [
            'description' => 'زيادة رأس المال',
            'amount' => $capitalChange,
            'type' => 'capital'
        ];
    } elseif ($capitalChange < 0) {
        $activities[] = [
            'description' => 'تخفيض رأس المال',
            'amount' => $capitalChange,
            'type' => 'capital'
        ];
    }
    
    // القروض
    $loansChange = 0;
    $loansStmt = $pdo->prepare("
        SELECT COALESCE(SUM(jed.credit - jed.debit), 0) as loans_change
        FROM chart_of_accounts coa
        JOIN journal_entry_details jed ON coa.id = jed.account_id
        JOIN journal_entries je ON jed.journal_entry_id = je.id
        WHERE coa.account_code LIKE '22%' 
        AND coa.account_type = 'liability'
        AND je.entry_date BETWEEN ? AND ?
        AND je.status = 'posted'
    ");
    $loansStmt->execute([$dateFrom, $dateTo]);
    $loansChange = $loansStmt->fetchColumn() ?: 0;
    
    if ($loansChange > 0) {
        $activities[] = [
            'description' => 'قروض جديدة',
            'amount' => $loansChange,
            'type' => 'loans'
        ];
    } elseif ($loansChange < 0) {
        $activities[] = [
            'description' => 'سداد قروض',
            'amount' => $loansChange,
            'type' => 'loans'
        ];
    }
    
    return $activities;
}

function getAccountChange($pdo, $accountCode, $dateFrom, $dateTo) {
    // حساب التغير في رصيد الحساب خلال الفترة
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(jed.debit - jed.credit), 0) as change_amount
        FROM chart_of_accounts coa
        JOIN journal_entry_details jed ON coa.id = jed.account_id
        JOIN journal_entries je ON jed.journal_entry_id = je.id
        WHERE coa.account_code = ?
        AND je.entry_date BETWEEN ? AND ?
        AND je.status = 'posted'
    ");
    $stmt->execute([$accountCode, $dateFrom, $dateTo]);
    return $stmt->fetchColumn() ?: 0;
}

function getCashBalance($pdo, $date, $isBeginning = false) {
    $operator = $isBeginning ? '<' : '<=';
    
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(jed.debit - jed.credit), 0) as cash_balance
        FROM chart_of_accounts coa
        JOIN journal_entry_details jed ON coa.id = jed.account_id
        JOIN journal_entries je ON jed.journal_entry_id = je.id
        WHERE coa.account_code IN ('1111', '1112') 
        AND je.entry_date $operator ?
        AND je.status = 'posted'
    ");
    $stmt->execute([$date]);
    return $stmt->fetchColumn() ?: 0;
}

function getCashFlowAnalysis($pdo, $dateFrom, $dateTo) {
    $analysis = [];
    
    // تحليل المبيعات حسب طريقة الدفع
    $salesAnalysisStmt = $pdo->prepare("
        SELECT 
            payment_method,
            COUNT(*) as count,
            SUM(total) as amount
        FROM sales 
        WHERE DATE(sale_date) BETWEEN ? AND ?
        GROUP BY payment_method
    ");
    $salesAnalysisStmt->execute([$dateFrom, $dateTo]);
    $analysis['sales_by_payment'] = $salesAnalysisStmt->fetchAll();
    
    // تحليل المصروفات حسب طريقة الدفع
    $expensesAnalysisStmt = $pdo->prepare("
        SELECT 
            payment_method,
            COUNT(*) as count,
            SUM(amount) as amount
        FROM expenses 
        WHERE DATE(expense_date) BETWEEN ? AND ?
        GROUP BY payment_method
    ");
    $expensesAnalysisStmt->execute([$dateFrom, $dateTo]);
    $analysis['expenses_by_payment'] = $expensesAnalysisStmt->fetchAll();
    
    // متوسط التدفق النقدي اليومي
    $daysInPeriod = (strtotime($dateTo) - strtotime($dateFrom)) / (60 * 60 * 24) + 1;
    $analysis['days_in_period'] = $daysInPeriod;
    
    return $analysis;
}

// إعدادات النظام
$currency = getSetting('currency', 'ر.س');
$companyName = getSetting('company_name', 'نظام نقاط البيع');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة التدفقات النقدية - <?php echo $companyName; ?></title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .report-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .report-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            text-align: center;
        }

        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .filter-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .cash-flow-table {
            border: none;
        }

        .cash-flow-table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
        }

        .cash-flow-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .section-header {
            background: #f8f9fa;
            font-weight: bold;
            color: var(--primary-color);
            font-size: 1.1em;
        }

        .subsection-header {
            background: #f1f3f4;
            font-weight: 600;
            color: var(--secondary-color);
            font-size: 0.95em;
        }

        .total-row {
            background: #e3f2fd;
            font-weight: bold;
            border-top: 2px solid var(--primary-color);
        }

        .cash-inflow {
            color: var(--success-color);
            font-weight: 600;
        }

        .cash-outflow {
            color: var(--danger-color);
            font-weight: 600;
        }

        .net-positive {
            background: #d4edda;
            color: #155724;
            font-weight: bold;
        }

        .net-negative {
            background: #f8d7da;
            color: #721c24;
            font-weight: bold;
        }

        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }

        .cash-flow-summary {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid var(--primary-color);
        }

        .activity-section {
            margin-bottom: 30px;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
        }

        .operating-icon {
            background: var(--success-color);
            color: white;
        }

        .investing-icon {
            background: var(--warning-color);
            color: white;
        }

        .financing-icon {
            background: var(--info-color);
            color: white;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            .filter-card, .btn, .chart-container {
                display: none !important;
            }
            .content-card {
                box-shadow: none;
                border: 1px solid #000;
                margin: 0;
                padding: 20px;
            }
            .report-header {
                background: white;
                box-shadow: none;
                border-bottom: 2px solid #000;
            }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <div class="report-container fade-in">
        <!-- Report Header -->
        <div class="report-header">
            <div class="row align-items-center">
                <div class="col-md-2">
                    <?php
                    $companyLogo = getSetting('company_logo');
                    if (!empty($companyLogo) && file_exists($companyLogo)):
                    ?>
                        <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 80px; max-height: 80px; border-radius: 10px;">
                    <?php endif; ?>
                </div>
                <div class="col-md-8">
                    <h2 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h2>
                    <h4 class="text-primary mb-0">قائمة التدفقات النقدية</h4>
                    <p class="text-muted mb-0" id="reportPeriod">للفترة من __ إلى __</p>
                </div>
                <div class="col-md-2 text-end">
                    <a href="accounting.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-right me-1"></i>العودة
                    </a>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card">
            <form id="cashFlowForm">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="dateFrom" name="date_from"
                               value="<?php echo date('Y-m-01'); ?>" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="dateTo" name="date_to"
                               value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-gradient w-100">
                            <i class="bi bi-cash-stack me-1"></i>إنشاء التقرير
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="printReport()">
                            <i class="bi bi-printer me-1"></i>طباعة
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-outline-success w-100" onclick="exportToExcel()">
                            <i class="bi bi-file-earmark-excel me-1"></i>تصدير Excel
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4" id="summaryCards" style="display: none;">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="operatingCashCard">0</h4>
                            <p class="mb-0 small">التدفق من الأنشطة التشغيلية</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-gear"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="investingCashCard">0</h4>
                            <p class="mb-0 small">التدفق من الأنشطة الاستثمارية</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-graph-up"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="financingCashCard">0</h4>
                            <p class="mb-0 small">التدفق من الأنشطة التمويلية</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-bank"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" id="netCashCard">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="netCashAmount">0</h4>
                            <p class="mb-0 small">صافي التغير في النقدية</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-cash-stack" id="netCashIcon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4" id="chartsRow" style="display: none;">
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-pie-chart me-2"></i>توزيع التدفقات النقدية</h5>
                    <div class="chart-container">
                        <canvas id="cashFlowDistributionChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-bar-chart me-2"></i>المبيعات حسب طريقة الدفع</h5>
                    <div class="chart-container">
                        <canvas id="salesPaymentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cash Flow Statement -->
        <div class="content-card" id="cashFlowStatement" style="display: none;">
            <h5 class="mb-4 text-center">
                <i class="bi bi-cash-stack me-2"></i>قائمة التدفقات النقدية التفصيلية
            </h5>

            <div class="table-responsive">
                <table class="table cash-flow-table">
                    <thead>
                        <tr>
                            <th width="70%">البيان</th>
                            <th width="30%" class="text-end">المبلغ</th>
                        </tr>
                    </thead>
                    <tbody id="cashFlowTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Cash Flow Analysis -->
        <div class="content-card" id="cashFlowAnalysis" style="display: none;">
            <h5 class="mb-3"><i class="bi bi-calculator me-2"></i>تحليل التدفقات النقدية</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>تحليل المبيعات</h6>
                    <div id="salesAnalysisContent">
                        <!-- سيتم ملء المحتوى هنا -->
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>تحليل المصروفات</h6>
                    <div id="expensesAnalysisContent">
                        <!-- سيتم ملء المحتوى هنا -->
                    </div>
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-md-12">
                    <h6>مؤشرات الأداء</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>متوسط التدفق النقدي اليومي:</strong></td>
                            <td id="dailyAverageCashFlow">-</td>
                        </tr>
                        <tr>
                            <td><strong>عدد أيام الفترة:</strong></td>
                            <td id="daysInPeriod">-</td>
                        </tr>
                        <tr>
                            <td><strong>نسبة النقدية من المبيعات:</strong></td>
                            <td id="cashSalesRatio">-</td>
                        </tr>
                        <tr>
                            <td><strong>نسبة المصروفات النقدية:</strong></td>
                            <td id="cashExpensesRatio">-</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Initial Message -->
        <div class="content-card text-center" id="initialMessage">
            <i class="bi bi-cash-stack display-1 text-muted"></i>
            <h5 class="text-muted mt-3">قائمة التدفقات النقدية</h5>
            <p class="text-muted">اختر الفترة الزمنية واضغط "إنشاء التقرير" لعرض قائمة التدفقات النقدية التفصيلية</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let currency = '<?php echo $currency; ?>';
        let cashFlowDistributionChart, salesPaymentChart;

        $(document).ready(function() {
            $('#cashFlowForm').on('submit', function(e) {
                e.preventDefault();
                generateCashFlow();
            });
        });

        function generateCashFlow() {
            const dateFrom = $('#dateFrom').val();
            const dateTo = $('#dateTo').val();

            if (!dateFrom || !dateTo) {
                Swal.fire('خطأ', 'يرجى تحديد الفترة الزمنية', 'error');
                return;
            }

            if (dateFrom > dateTo) {
                Swal.fire('خطأ', 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error');
                return;
            }

            // Show loading
            Swal.fire({
                title: 'جاري إنشاء قائمة التدفقات النقدية...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.post('cash_flow.php', {
                action: 'generate_cash_flow',
                date_from: dateFrom,
                date_to: dateTo
            })
            .done(function(response) {
                const result = JSON.parse(response);
                if (result.success) {
                    displayCashFlow(result.data);
                    Swal.close();
                } else {
                    Swal.fire('خطأ', result.message, 'error');
                }
            })
            .fail(function() {
                Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
            });
        }

        function displayCashFlow(data) {
            // Update report period
            const dateFrom = new Date(data.date_from).toLocaleDateString('ar-SA');
            const dateTo = new Date(data.date_to).toLocaleDateString('ar-SA');
            $('#reportPeriod').text(`للفترة من ${dateFrom} إلى ${dateTo}`);

            // Update summary cards
            $('#operatingCashCard').text(formatCurrency(data.net_cash_from_operating));
            $('#investingCashCard').text(formatCurrency(data.net_cash_from_investing));
            $('#financingCashCard').text(formatCurrency(data.net_cash_from_financing));
            $('#netCashAmount').text(formatCurrency(data.net_change_in_cash));

            // Update net cash card style
            const netCashCard = $('#netCashCard');
            const netCashIcon = $('#netCashIcon');

            if (data.net_change_in_cash >= 0) {
                netCashCard.css('background', 'linear-gradient(135deg, var(--success-color), #2ecc71)');
                netCashIcon.removeClass('bi-cash-stack').addClass('bi-arrow-up-circle');
            } else {
                netCashCard.css('background', 'linear-gradient(135deg, var(--danger-color), #c0392b)');
                netCashIcon.removeClass('bi-cash-stack').addClass('bi-arrow-down-circle');
            }

            // Build cash flow statement table
            buildCashFlowTable(data);

            // Update analysis
            updateCashFlowAnalysis(data);

            // Create charts
            createCharts(data);

            // Show all sections
            $('#initialMessage').hide();
            $('#summaryCards, #chartsRow, #cashFlowStatement, #cashFlowAnalysis').show();
        }

        function buildCashFlowTable(data) {
            let tableBody = '';

            // الأنشطة التشغيلية
            tableBody += '<tr class="section-header"><td colspan="2"><span class="activity-icon operating-icon"><i class="bi bi-gear"></i></span><strong>التدفقات النقدية من الأنشطة التشغيلية</strong></td></tr>';

            if (data.operating_activities.length > 0) {
                data.operating_activities.forEach(activity => {
                    const amountClass = activity.amount >= 0 ? 'cash-inflow' : 'cash-outflow';
                    tableBody += `
                        <tr>
                            <td>&nbsp;&nbsp;&nbsp;&nbsp;${activity.description}</td>
                            <td class="text-end ${amountClass}">${formatCurrency(activity.amount)}</td>
                        </tr>
                    `;
                });
            } else {
                tableBody += '<tr><td>&nbsp;&nbsp;&nbsp;&nbsp;لا توجد أنشطة تشغيلية</td><td class="text-end">0.00</td></tr>';
            }

            const operatingClass = data.net_cash_from_operating >= 0 ? 'net-positive' : 'net-negative';
            tableBody += `
                <tr class="${operatingClass}">
                    <td><strong>صافي التدفق النقدي من الأنشطة التشغيلية</strong></td>
                    <td class="text-end"><strong>${formatCurrency(data.net_cash_from_operating)}</strong></td>
                </tr>
            `;

            // الأنشطة الاستثمارية
            tableBody += '<tr class="section-header"><td colspan="2"><span class="activity-icon investing-icon"><i class="bi bi-graph-up"></i></span><strong>التدفقات النقدية من الأنشطة الاستثمارية</strong></td></tr>';

            if (data.investing_activities.length > 0) {
                data.investing_activities.forEach(activity => {
                    const amountClass = activity.amount >= 0 ? 'cash-inflow' : 'cash-outflow';
                    tableBody += `
                        <tr>
                            <td>&nbsp;&nbsp;&nbsp;&nbsp;${activity.description}</td>
                            <td class="text-end ${amountClass}">${formatCurrency(activity.amount)}</td>
                        </tr>
                    `;
                });
            } else {
                tableBody += '<tr><td>&nbsp;&nbsp;&nbsp;&nbsp;لا توجد أنشطة استثمارية</td><td class="text-end">0.00</td></tr>';
            }

            const investingClass = data.net_cash_from_investing >= 0 ? 'net-positive' : 'net-negative';
            tableBody += `
                <tr class="${investingClass}">
                    <td><strong>صافي التدفق النقدي من الأنشطة الاستثمارية</strong></td>
                    <td class="text-end"><strong>${formatCurrency(data.net_cash_from_investing)}</strong></td>
                </tr>
            `;

            // الأنشطة التمويلية
            tableBody += '<tr class="section-header"><td colspan="2"><span class="activity-icon financing-icon"><i class="bi bi-bank"></i></span><strong>التدفقات النقدية من الأنشطة التمويلية</strong></td></tr>';

            if (data.financing_activities.length > 0) {
                data.financing_activities.forEach(activity => {
                    const amountClass = activity.amount >= 0 ? 'cash-inflow' : 'cash-outflow';
                    tableBody += `
                        <tr>
                            <td>&nbsp;&nbsp;&nbsp;&nbsp;${activity.description}</td>
                            <td class="text-end ${amountClass}">${formatCurrency(activity.amount)}</td>
                        </tr>
                    `;
                });
            } else {
                tableBody += '<tr><td>&nbsp;&nbsp;&nbsp;&nbsp;لا توجد أنشطة تمويلية</td><td class="text-end">0.00</td></tr>';
            }

            const financingClass = data.net_cash_from_financing >= 0 ? 'net-positive' : 'net-negative';
            tableBody += `
                <tr class="${financingClass}">
                    <td><strong>صافي التدفق النقدي من الأنشطة التمويلية</strong></td>
                    <td class="text-end"><strong>${formatCurrency(data.net_cash_from_financing)}</strong></td>
                </tr>
            `;

            // ملخص التدفقات النقدية
            tableBody += '<tr class="section-header"><td colspan="2"><strong>ملخص التدفقات النقدية</strong></td></tr>';

            const netChangeClass = data.net_change_in_cash >= 0 ? 'net-positive' : 'net-negative';
            tableBody += `
                <tr class="${netChangeClass}">
                    <td><strong>صافي التغير في النقدية</strong></td>
                    <td class="text-end"><strong>${formatCurrency(data.net_change_in_cash)}</strong></td>
                </tr>
                <tr>
                    <td>النقدية في بداية الفترة</td>
                    <td class="text-end">${formatCurrency(data.beginning_cash)}</td>
                </tr>
                <tr class="total-row">
                    <td><strong>النقدية في نهاية الفترة</strong></td>
                    <td class="text-end"><strong>${formatCurrency(data.calculated_ending_cash)}</strong></td>
                </tr>
            `;

            $('#cashFlowTableBody').html(tableBody);
        }

        function updateCashFlowAnalysis(data) {
            // تحليل المبيعات
            let salesAnalysisHtml = '<table class="table table-sm">';
            let totalSalesAmount = 0;
            let cashSalesAmount = 0;

            data.analysis.sales_by_payment.forEach(payment => {
                const methodName = getPaymentMethodName(payment.payment_method);
                salesAnalysisHtml += `
                    <tr>
                        <td><strong>${methodName}:</strong></td>
                        <td>${payment.count} عملية</td>
                        <td class="text-end">${formatCurrency(payment.amount)}</td>
                    </tr>
                `;
                totalSalesAmount += parseFloat(payment.amount);
                if (payment.payment_method === 'cash') {
                    cashSalesAmount = parseFloat(payment.amount);
                }
            });
            salesAnalysisHtml += '</table>';
            $('#salesAnalysisContent').html(salesAnalysisHtml);

            // تحليل المصروفات
            let expensesAnalysisHtml = '<table class="table table-sm">';
            let totalExpensesAmount = 0;
            let cashExpensesAmount = 0;

            data.analysis.expenses_by_payment.forEach(payment => {
                const methodName = getPaymentMethodName(payment.payment_method);
                expensesAnalysisHtml += `
                    <tr>
                        <td><strong>${methodName}:</strong></td>
                        <td>${payment.count} عملية</td>
                        <td class="text-end">${formatCurrency(payment.amount)}</td>
                    </tr>
                `;
                totalExpensesAmount += parseFloat(payment.amount);
                if (payment.payment_method === 'cash') {
                    cashExpensesAmount = parseFloat(payment.amount);
                }
            });
            expensesAnalysisHtml += '</table>';
            $('#expensesAnalysisContent').html(expensesAnalysisHtml);

            // مؤشرات الأداء
            const dailyAverage = data.analysis.days_in_period > 0 ?
                (data.net_change_in_cash / data.analysis.days_in_period) : 0;
            const cashSalesRatio = totalSalesAmount > 0 ?
                ((cashSalesAmount / totalSalesAmount) * 100).toFixed(1) + '%' : '0%';
            const cashExpensesRatio = totalExpensesAmount > 0 ?
                ((cashExpensesAmount / totalExpensesAmount) * 100).toFixed(1) + '%' : '0%';

            $('#dailyAverageCashFlow').text(formatCurrency(dailyAverage));
            $('#daysInPeriod').text(Math.round(data.analysis.days_in_period) + ' يوم');
            $('#cashSalesRatio').text(cashSalesRatio);
            $('#cashExpensesRatio').text(cashExpensesRatio);
        }

        function createCharts(data) {
            // Cash Flow Distribution Chart
            const cashFlowDistributionCtx = document.getElementById('cashFlowDistributionChart').getContext('2d');

            if (cashFlowDistributionChart) {
                cashFlowDistributionChart.destroy();
            }

            const flowLabels = [];
            const flowData = [];
            const flowColors = ['#27ae60', '#f39c12', '#3498db'];

            if (Math.abs(data.net_cash_from_operating) > 0) {
                flowLabels.push('الأنشطة التشغيلية');
                flowData.push(Math.abs(data.net_cash_from_operating));
            }
            if (Math.abs(data.net_cash_from_investing) > 0) {
                flowLabels.push('الأنشطة الاستثمارية');
                flowData.push(Math.abs(data.net_cash_from_investing));
            }
            if (Math.abs(data.net_cash_from_financing) > 0) {
                flowLabels.push('الأنشطة التمويلية');
                flowData.push(Math.abs(data.net_cash_from_financing));
            }

            cashFlowDistributionChart = new Chart(cashFlowDistributionCtx, {
                type: 'doughnut',
                data: {
                    labels: flowLabels,
                    datasets: [{
                        data: flowData,
                        backgroundColor: flowColors.slice(0, flowLabels.length),
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + formatCurrency(context.parsed);
                                }
                            }
                        }
                    }
                }
            });

            // Sales Payment Methods Chart
            const salesPaymentCtx = document.getElementById('salesPaymentChart').getContext('2d');

            if (salesPaymentChart) {
                salesPaymentChart.destroy();
            }

            const paymentLabels = [];
            const paymentData = [];
            const paymentColors = ['#27ae60', '#3498db', '#f39c12'];

            data.analysis.sales_by_payment.forEach((payment, index) => {
                paymentLabels.push(getPaymentMethodName(payment.payment_method));
                paymentData.push(payment.amount);
            });

            salesPaymentChart = new Chart(salesPaymentCtx, {
                type: 'bar',
                data: {
                    labels: paymentLabels,
                    datasets: [{
                        label: 'المبلغ',
                        data: paymentData,
                        backgroundColor: paymentColors.slice(0, paymentLabels.length),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        function getPaymentMethodName(method) {
            const methods = {
                'cash': 'نقدي',
                'card': 'بطاقة ائتمان',
                'bank': 'تحويل بنكي',
                'check': 'شيك'
            };
            return methods[method] || method;
        }

        function formatCurrency(amount) {
            return parseFloat(amount || 0).toFixed(2) + ' ' + currency;
        }

        function printReport() {
            window.print();
        }

        function exportToExcel() {
            Swal.fire('معلومة', 'ميزة تصدير Excel قيد التطوير', 'info');
        }
    </script>
</body>
</html>
