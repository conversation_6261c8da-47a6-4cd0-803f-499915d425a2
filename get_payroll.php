<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    exit('غير مصرح');
}

require 'db.php';

try {
    $stmt = $pdo->query("
        SELECT p.*, u.name as user_name
        FROM payroll p
        LEFT JOIN users u ON p.user_id = u.id
        ORDER BY p.salary_month DESC, p.id DESC
    ");
    
    $payroll = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    header('Content-Type: application/json');
    echo json_encode($payroll);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في قاعدة البيانات']);
}
?>
