# نظام نقاط البيع (POS System)

نظام متكامل لإدارة نقاط البيع باللغة العربية مع واجهة مستخدم حديثة وجذابة.

## المميزات

### 🏠 لوحة التحكم
- إحصائيات سريعة ومفصلة
- رسوم بيانية تفاعلية لمبيعات آخر 7 أيام
- أفضل 5 منتجات مبيعاً
- ملخص مالي شامل (مبيعات، مشتريات، أرباح)
- تنبيهات المخزون المنخفض

### 📦 إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- تصنيف المنتجات حسب الفئات
- إدارة وحدات القياس
- تتبع المخزون

### 🛒 نقطة البيع
- واجهة سهلة وسريعة للبيع
- حساب المجموع تلقائياً
- طباعة الفواتير

### 📊 التقارير والإحصائيات
- تقارير المبيعات اليومية والشهرية
- تقارير المشتريات
- تقارير المخزون
- التحليل المالي

### 👥 إدارة العملاء والموردين
- قاعدة بيانات العملاء
- معلومات الموردين
- تاريخ التعاملات

### 💰 النظام المحاسبي
- تتبع الإيداعات والمصروفات
- حساب صافي الربح
- التوازن المالي

## متطلبات التشغيل

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- متصفح ويب حديث

## التثبيت

### 1. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE pos3;

-- استخدام قاعدة البيانات
USE pos3;

-- تشغيل ملف إنشاء الجداول
SOURCE pos_schema.sql;

-- إدراج البيانات التجريبية (اختياري)
SOURCE sample_data.sql;
```

### 2. إعداد الاتصال بقاعدة البيانات

تأكد من تحديث إعدادات قاعدة البيانات في ملف `db.php`:

```php
$host = 'localhost';
$db   = 'pos3';
$user = 'root';
$pass = '';
```

### 3. رفع الملفات

ارفع جميع الملفات إلى مجلد الخادم (مثل `htdocs` في XAMPP).

### 4. الوصول للنظام

افتح المتصفح وانتقل إلى:
```
http://localhost/pos3/login.php
```

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## هيكل الملفات

```
pos3/
├── dashboard.php          # لوحة التحكم الرئيسية
├── login.php             # صفحة تسجيل الدخول
├── logout.php            # تسجيل الخروج
├── db.php                # إعدادات قاعدة البيانات
├── pos_schema.sql        # هيكل قاعدة البيانات
├── sample_data.sql       # بيانات تجريبية
└── README.md             # دليل الاستخدام
```

## التقنيات المستخدمة

- **Backend:** PHP 7.4+
- **Database:** MySQL
- **Frontend:** Bootstrap 5 RTL
- **Charts:** Chart.js
- **Icons:** Bootstrap Icons
- **Styling:** CSS3 مع متغيرات مخصصة

## المميزات التقنية

### 🎨 التصميم
- واجهة مستخدم عربية (RTL)
- تصميم متجاوب يعمل على جميع الأجهزة
- ألوان وخطوط حديثة
- تأثيرات بصرية جذابة

### 🔒 الأمان
- حماية الجلسات
- تشفير كلمات المرور
- حماية من SQL Injection
- التحقق من صحة البيانات

### 📊 الأداء
- استعلامات محسنة لقاعدة البيانات
- تحديث تلقائي للصفحة كل 5 دقائق
- تحميل سريع للبيانات

## الدعم والتطوير

هذا النظام قابل للتطوير والتخصيص حسب احتياجاتك. يمكن إضافة المزيد من المميزات مثل:

- نظام الصلاحيات المتقدم
- تقارير أكثر تفصيلاً
- تكامل مع أنظمة الدفع
- تطبيق موبايل
- نظام الباركود
- إدارة الفروع المتعددة

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتطويره بحرية.
