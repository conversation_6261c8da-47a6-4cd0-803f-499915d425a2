<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require 'db.php';
require 'check_permissions.php';
require 'get_settings.php';

$userId = $_SESSION['user_id'];
$permissions = getUserPermissions($pdo, $userId);
$isAdmin = isAdmin($pdo, $userId);
$userRole = getUserRole($pdo, $userId);
$companyName = getSetting('company_name', 'نظام نقاط البيع');

// تجميع الصلاحيات حسب الفئة
$permissionsByCategory = [];
foreach ($permissions as $permission) {
    $permissionsByCategory[$permission['category']][] = $permission;
}

// قائمة الصفحات للاختبار
$testPages = [
    'dashboard.php' => ['name' => 'لوحة التحكم', 'permission' => '', 'icon' => 'bi-house-door'],
    'products.php' => ['name' => 'إدارة المنتجات', 'permission' => 'products.view', 'icon' => 'bi-box'],
    'categories.php' => ['name' => 'إدارة الفئات', 'permission' => 'products.view', 'icon' => 'bi-folder'],
    'units.php' => ['name' => 'إدارة الوحدات', 'permission' => 'products.view', 'icon' => 'bi-rulers'],
    'pos.php' => ['name' => 'نقطة البيع', 'permission' => 'pos.access', 'icon' => 'bi-cart'],
    'sales.php' => ['name' => 'سجل المبيعات', 'permission' => 'sales.view', 'icon' => 'bi-graph-up'],
    'purchases.php' => ['name' => 'سجل المشتريات', 'permission' => 'inventory.view', 'icon' => 'bi-clipboard-data'],
    'inventory.php' => ['name' => 'إدارة المخزون', 'permission' => 'inventory.view', 'icon' => 'bi-boxes'],
    'customers.php' => ['name' => 'إدارة العملاء', 'permission' => 'customers.view', 'icon' => 'bi-people'],
    'suppliers.php' => ['name' => 'إدارة الموردين', 'permission' => 'suppliers.view', 'icon' => 'bi-building'],
    'accounting.php' => ['name' => 'النظام المحاسبي', 'permission' => 'accounting.view', 'icon' => 'bi-currency-dollar'],
    'reports.php' => ['name' => 'التقارير والإحصائيات', 'permission' => 'reports.view', 'icon' => 'bi-bar-chart'],
    'settings.php' => ['name' => 'إعدادات النظام', 'permission' => 'settings.view', 'icon' => 'bi-gear']
];

// إحصائيات الصلاحيات
$totalPermissions = count($permissions);
$allowedPages = 0;
$deniedPages = 0;

foreach ($testPages as $page => $info) {
    if (empty($info['permission']) || hasPermission($pdo, $userId, $info['permission']) || $isAdmin) {
        $allowedPages++;
    } else {
        $deniedPages++;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الصلاحيات - <?php echo $companyName; ?></title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .permission-badge {
            font-size: 0.8em;
            padding: 4px 8px;
        }
        
        .page-test-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .page-test-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .allowed {
            border-left: 4px solid #28a745;
        }
        
        .denied {
            border-left: 4px solid #dc3545;
            opacity: 0.7;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .category-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-card">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-shield-check me-2"></i>اختبار الصلاحيات</h2>
                    <p class="text-muted mb-0">فحص شامل لصلاحيات المستخدم وإمكانية الوصول للصفحات</p>
                </div>
                <div class="col-auto">
                    <a href="settings.php" class="btn btn-primary">
                        <i class="bi bi-gear me-2"></i>إدارة الصلاحيات
                    </a>
                </div>
            </div>
        </div>

        <!-- User Info -->
        <div class="test-card">
            <h5 class="mb-3"><i class="bi bi-person me-2"></i>معلومات المستخدم</h5>
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>اسم المستخدم:</strong></td>
                            <td><?php echo htmlspecialchars($_SESSION['username']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>الدور:</strong></td>
                            <td>
                                <span class="badge bg-info"><?php echo htmlspecialchars($userRole['display_name'] ?? 'غير محدد'); ?></span>
                                <?php if ($isAdmin): ?>
                                <span class="badge bg-danger ms-1">مدير النظام</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>عدد الصلاحيات:</strong></td>
                            <td><?php echo $totalPermissions; ?></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <div class="stats-grid">
                        <div class="stat-card" style="background: linear-gradient(135deg, #28a745, #20c997);">
                            <h4 class="mb-0"><?php echo $allowedPages; ?></h4>
                            <small>صفحات مسموحة</small>
                        </div>
                        <div class="stat-card" style="background: linear-gradient(135deg, #dc3545, #e74c3c);">
                            <h4 class="mb-0"><?php echo $deniedPages; ?></h4>
                            <small>صفحات محظورة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions by Category -->
        <div class="test-card">
            <h5 class="mb-3"><i class="bi bi-list-check me-2"></i>الصلاحيات حسب الفئة</h5>
            <?php if (!empty($permissionsByCategory)): ?>
                <?php 
                $categoryNames = [
                    'users' => 'إدارة المستخدمين',
                    'products' => 'إدارة المنتجات',
                    'sales' => 'المبيعات ونقطة البيع',
                    'inventory' => 'إدارة المخزون',
                    'customers' => 'العملاء والموردين',
                    'suppliers' => 'العملاء والموردين',
                    'reports' => 'التقارير والمحاسبة',
                    'accounting' => 'التقارير والمحاسبة',
                    'settings' => 'الإعدادات'
                ];
                ?>
                <?php foreach ($permissionsByCategory as $category => $categoryPermissions): ?>
                <div class="category-section">
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-shield-check me-2"></i>
                        <?php echo $categoryNames[$category] ?? $category; ?>
                    </h6>
                    <div class="row">
                        <?php foreach ($categoryPermissions as $permission): ?>
                        <div class="col-md-6 mb-2">
                            <span class="badge bg-success permission-badge">
                                <?php echo htmlspecialchars($permission['display_name']); ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    لا توجد صلاحيات محددة لهذا المستخدم
                </div>
            <?php endif; ?>
        </div>

        <!-- Pages Access Test -->
        <div class="test-card">
            <h5 class="mb-3"><i class="bi bi-globe me-2"></i>اختبار الوصول للصفحات</h5>
            <div class="row">
                <?php foreach ($testPages as $page => $info): ?>
                <?php 
                $hasAccess = empty($info['permission']) || hasPermission($pdo, $userId, $info['permission']) || $isAdmin;
                $statusClass = $hasAccess ? 'allowed' : 'denied';
                $statusIcon = $hasAccess ? 'bi-check-circle text-success' : 'bi-x-circle text-danger';
                $statusText = $hasAccess ? 'مسموح' : 'محظور';
                ?>
                <div class="col-md-6 mb-3">
                    <div class="page-test-item <?php echo $statusClass; ?>">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <i class="<?php echo $info['icon']; ?> fs-4 me-3 text-primary"></i>
                                <div>
                                    <h6 class="mb-0"><?php echo $info['name']; ?></h6>
                                    <small class="text-muted"><?php echo $page; ?></small>
                                </div>
                            </div>
                            <div class="text-end">
                                <i class="<?php echo $statusIcon; ?> fs-5"></i>
                                <br>
                                <small class="<?php echo $hasAccess ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo $statusText; ?>
                                </small>
                            </div>
                        </div>
                        <?php if ($hasAccess): ?>
                        <div class="mt-2">
                            <a href="<?php echo $page; ?>" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-arrow-left me-1"></i>زيارة الصفحة
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Actions -->
        <div class="test-card">
            <h5 class="mb-3"><i class="bi bi-tools me-2"></i>إجراءات سريعة</h5>
            <div class="row">
                <div class="col-md-3">
                    <a href="settings.php" class="btn btn-primary w-100 mb-2">
                        <i class="bi bi-gear me-2"></i>إدارة الصلاحيات
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="dashboard.php" class="btn btn-outline-primary w-100 mb-2">
                        <i class="bi bi-house-door me-2"></i>لوحة التحكم
                    </a>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-success w-100 mb-2" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise me-2"></i>تحديث الاختبار
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-info w-100 mb-2" onclick="window.print()">
                        <i class="bi bi-printer me-2"></i>طباعة التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
