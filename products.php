<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';

// إنشاء جدول المنتجات إذا لم يكن موجوداً
$pdo->exec("CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id INT,
    unit_id INT,
    cost_price DECIMAL(10,2),
    selling_price DECIMAL(10,2),
    quantity INT DEFAULT 0,
    min_quantity INT DEFAULT 0,
    image VARCHAR(255),
    status TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)");

// معالجة إضافة/تحديث المنتج
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            if ($_POST['action'] === 'add' || $_POST['action'] === 'edit') {
                $data = [
                    'code' => $_POST['code'],
                    'name' => $_POST['name'],
                    'description' => $_POST['description'],
                    'category_id' => $_POST['category_id'],
                    'unit_id' => $_POST['unit_id'],
                    'cost_price' => $_POST['cost_price'],
                    'selling_price' => $_POST['selling_price'],
                    'quantity' => $_POST['quantity'],
                    'min_quantity' => $_POST['min_quantity'],
                    'status' => isset($_POST['status']) ? 1 : 0
                ];

                // معالجة رفع الصورة
                if (isset($_FILES['image']) && $_FILES['image']['error'] === 0) {
                    $uploadDir = 'uploads/products/';
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0777, true);
                    }

                    $file = $_FILES['image'];
                    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
                    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

                    if (in_array($file['type'], $allowedTypes) && in_array($fileExtension, $allowedExtensions)) {
                        if ($file['size'] < 5000000) { // 5MB max
                            $newFileName = 'product_' . time() . '.' . $fileExtension;
                            $uploadPath = $uploadDir . $newFileName;

                            if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                                $data['image'] = $uploadPath;
                            }
                        }
                    }
                }

                if ($_POST['action'] === 'add') {
                    $sql = "INSERT INTO products (code, name, description, category_id, unit_id, cost_price, selling_price, quantity, min_quantity, image, status) 
                            VALUES (:code, :name, :description, :category_id, :unit_id, :cost_price, :selling_price, :quantity, :min_quantity, :image, :status)";
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($data);
                    $message = '<div class="alert alert-success">تم إضافة المنتج بنجاح</div>';
                } else {
                    $sql = "UPDATE products SET 
                            code = :code,
                            name = :name,
                            description = :description,
                            category_id = :category_id,
                            unit_id = :unit_id,
                            cost_price = :cost_price,
                            selling_price = :selling_price,
                            quantity = :quantity,
                            min_quantity = :min_quantity,
                            image = :image,
                            status = :status
                            WHERE id = :id";
                    $data['id'] = $_POST['id'];
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($data);
                    $message = '<div class="alert alert-success">تم تحديث المنتج بنجاح</div>';
                }
            } elseif ($_POST['action'] === 'delete' && isset($_POST['id'])) {
                $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
                $stmt->execute([$_POST['id']]);
                $message = '<div class="alert alert-success">تم حذف المنتج بنجاح</div>';
            }
        }
    } catch (PDOException $e) {
        $message = '<div class="alert alert-danger">حدث خطأ: ' . $e->getMessage() . '</div>';
    }
}

// جلب المنتجات
$products = $pdo->query("SELECT p.*, c.name as category_name, u.name as unit_name 
                        FROM products p 
                        LEFT JOIN categories c ON p.category_id = c.id 
                        LEFT JOIN units u ON p.unit_id = u.id 
                        ORDER BY p.name")->fetchAll();

// جلب الفئات والوحدات
$categories = $pdo->query("SELECT * FROM categories ORDER BY name")->fetchAll();
$units = $pdo->query("SELECT * FROM units ORDER BY name")->fetchAll();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام نقاط البيع</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f6f9;
            margin: 0;
            padding: 0;
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            border-left-color: #fff;
        }
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        .settings-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 20px;
        }
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            $companyName = getSetting('company_name', 'نظام نقاط البيع');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>

        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link active" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0">إدارة المنتجات</h2>
                    <small class="text-muted">إضافة وتعديل وحذف المنتجات</small>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة منتج جديد
                    </button>
                </div>
            </div>
        </div>

        <?php echo $message; ?>

        <!-- Products Table -->
        <div class="settings-card">
            <div class="table-responsive">
                <table class="table table-striped" id="productsTable">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>الصورة</th>
                            <th>الاسم</th>
                            <th>الفئة</th>
                            <th>الوحدة</th>
                            <th>سعر الشراء</th>
                            <th>سعر البيع</th>
                            <th>الكمية</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($product['code']); ?></td>
                            <td>
                                <?php if (!empty($product['image']) && file_exists($product['image'])): ?>
                                    <img src="<?php echo $product['image']; ?>" alt="صورة المنتج" class="img-thumbnail" style="max-width: 50px;">
                                <?php else: ?>
                                    <i class="bi bi-image text-muted"></i>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($product['name']); ?></td>
                            <td><?php echo htmlspecialchars($product['category_name'] ?? '-'); ?></td>
                            <td><?php echo htmlspecialchars($product['unit_name'] ?? '-'); ?></td>
                            <td><?php echo formatCurrency($product['cost_price']); ?></td>
                            <td><?php echo formatCurrency($product['selling_price']); ?></td>
                            <td>
                                <span class="badge <?php echo $product['quantity'] <= $product['min_quantity'] ? 'bg-danger' : 'bg-success'; ?>">
                                    <?php echo $product['quantity']; ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge <?php echo $product['status'] ? 'bg-success' : 'bg-danger'; ?>">
                                    <?php echo $product['status'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-info" onclick="editProduct(<?php echo htmlspecialchars(json_encode($product)); ?>)">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteProduct(<?php echo $product['id']; ?>)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">كود المنتج</label>
                                <input type="text" class="form-control" name="code" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المنتج</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" rows="3"></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-select" name="category_id">
                                    <option value="">اختر الفئة</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الوحدة</label>
                                <select class="form-select" name="unit_id">
                                    <option value="">اختر الوحدة</option>
                                    <?php foreach ($units as $unit): ?>
                                        <option value="<?php echo $unit['id']; ?>"><?php echo htmlspecialchars($unit['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر الشراء</label>
                                <input type="number" class="form-control" name="cost_price" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر البيع</label>
                                <input type="number" class="form-control" name="selling_price" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" class="form-control" name="quantity" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأدنى للكمية</label>
                                <input type="number" class="form-control" name="min_quantity" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">صورة المنتج</label>
                                <input type="file" class="form-control" name="image" accept="image/*">
                            </div>
                            <div class="col-md-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="status" checked>
                                    <label class="form-check-label">نشط</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Product Modal -->
    <div class="modal fade" id="editProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المنتج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="id" id="edit_id">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">كود المنتج</label>
                                <input type="text" class="form-control" name="code" id="edit_code" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المنتج</label>
                                <input type="text" class="form-control" name="name" id="edit_name" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" id="edit_description" rows="3"></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-select" name="category_id" id="edit_category_id">
                                    <option value="">اختر الفئة</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الوحدة</label>
                                <select class="form-select" name="unit_id" id="edit_unit_id">
                                    <option value="">اختر الوحدة</option>
                                    <?php foreach ($units as $unit): ?>
                                        <option value="<?php echo $unit['id']; ?>"><?php echo htmlspecialchars($unit['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر الشراء</label>
                                <input type="number" class="form-control" name="cost_price" id="edit_cost_price" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر البيع</label>
                                <input type="number" class="form-control" name="selling_price" id="edit_selling_price" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" class="form-control" name="quantity" id="edit_quantity" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأدنى للكمية</label>
                                <input type="number" class="form-control" name="min_quantity" id="edit_min_quantity" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">صورة المنتج</label>
                                <input type="file" class="form-control" name="image" accept="image/*">
                                <div id="current_image" class="mt-2"></div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="status" id="edit_status">
                                    <label class="form-check-label">نشط</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Product Modal -->
    <div class="modal fade" id="deleteProductModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف هذا المنتج؟</p>
                </div>
                <div class="modal-footer">
                    <form method="POST">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" id="delete_id">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // تهيئة DataTable
        $(document).ready(function() {
            $('#productsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                }
            });
        });

        // دالة تعديل المنتج
        function editProduct(product) {
            document.getElementById('edit_id').value = product.id;
            document.getElementById('edit_code').value = product.code;
            document.getElementById('edit_name').value = product.name;
            document.getElementById('edit_description').value = product.description;
            document.getElementById('edit_category_id').value = product.category_id;
            document.getElementById('edit_unit_id').value = product.unit_id;
            document.getElementById('edit_cost_price').value = product.cost_price;
            document.getElementById('edit_selling_price').value = product.selling_price;
            document.getElementById('edit_quantity').value = product.quantity;
            document.getElementById('edit_min_quantity').value = product.min_quantity;
            document.getElementById('edit_status').checked = product.status == 1;

            // عرض الصورة الحالية
            const currentImage = document.getElementById('current_image');
            if (product.image) {
                currentImage.innerHTML = `<img src="${product.image}" alt="صورة المنتج" class="img-thumbnail" style="max-width: 100px;">`;
            } else {
                currentImage.innerHTML = '<p class="text-muted">لا توجد صورة</p>';
            }

            new bootstrap.Modal(document.getElementById('editProductModal')).show();
        }

        // دالة حذف المنتج
        function deleteProduct(id) {
            document.getElementById('delete_id').value = id;
            new bootstrap.Modal(document.getElementById('deleteProductModal')).show();
        }
    </script>
</body>
</html> 