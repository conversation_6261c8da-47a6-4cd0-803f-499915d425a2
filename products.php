<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';
require 'check_permissions.php';
require 'apply_permissions.php';

// التحقق من صلاحيات الوصول للصفحة
applyPagePermissions($pdo, $_SESSION['user_id'], 'products.php');

// تحديث جدول المنتجات لإضافة الأعمدة المطلوبة
try {
    // التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
    $columns = $pdo->query("SHOW COLUMNS FROM products")->fetchAll(PDO::FETCH_COLUMN);

    if (!in_array('code', $columns)) {
        $pdo->exec("ALTER TABLE products ADD COLUMN code VARCHAR(50) UNIQUE");
    }
    if (!in_array('description', $columns)) {
        $pdo->exec("ALTER TABLE products ADD COLUMN description TEXT");
    }
    if (!in_array('cost_price', $columns)) {
        $pdo->exec("ALTER TABLE products ADD COLUMN cost_price DECIMAL(10,2)");
    }
    if (!in_array('selling_price', $columns)) {
        $pdo->exec("ALTER TABLE products ADD COLUMN selling_price DECIMAL(10,2)");
    }
    if (!in_array('quantity', $columns)) {
        $pdo->exec("ALTER TABLE products ADD COLUMN quantity INT DEFAULT 0");
    }
    if (!in_array('min_quantity', $columns)) {
        $pdo->exec("ALTER TABLE products ADD COLUMN min_quantity INT DEFAULT 0");
    }
    if (!in_array('image', $columns)) {
        $pdo->exec("ALTER TABLE products ADD COLUMN image VARCHAR(255)");
    }
    if (!in_array('status', $columns)) {
        $pdo->exec("ALTER TABLE products ADD COLUMN status TINYINT(1) DEFAULT 1");
    }
    if (!in_array('created_at', $columns)) {
        $pdo->exec("ALTER TABLE products ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
    }
    if (!in_array('updated_at', $columns)) {
        $pdo->exec("ALTER TABLE products ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
    }

    // تحديث الأعمدة الموجودة لتحديد القيم الافتراضية
    $pdo->exec("UPDATE products SET status = 1 WHERE status IS NULL");
    $pdo->exec("UPDATE products SET quantity = 0 WHERE quantity IS NULL");
    $pdo->exec("UPDATE products SET min_quantity = 0 WHERE min_quantity IS NULL");
    $pdo->exec("UPDATE products SET created_at = NOW() WHERE created_at IS NULL");
    $pdo->exec("UPDATE products SET updated_at = NOW() WHERE updated_at IS NULL");

} catch (PDOException $e) {
    // في حالة فشل التحديث، إنشاء الجدول من جديد
    $pdo->exec("CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(50) UNIQUE,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category_id INT,
        unit_id INT,
        cost_price DECIMAL(10,2),
        selling_price DECIMAL(10,2),
        quantity INT DEFAULT 0,
        min_quantity INT DEFAULT 0,
        image VARCHAR(255),
        status TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
}

// معالجة إضافة/تعديل/حذف المنتج
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            // التحقق من الصلاحيات أولاً
            $hasAddPermission = hasPermission($pdo, $_SESSION['user_id'], 'products.create') || isAdmin($pdo, $_SESSION['user_id']);
            $hasEditPermission = hasPermission($pdo, $_SESSION['user_id'], 'products.edit') || isAdmin($pdo, $_SESSION['user_id']);
            $hasDeletePermission = hasPermission($pdo, $_SESSION['user_id'], 'products.delete') || isAdmin($pdo, $_SESSION['user_id']);

            if ($_POST['action'] === 'add' && !$hasAddPermission) {
                $message = 'ليس لديك صلاحية لإضافة المنتجات';
                $messageType = 'danger';
            } elseif ($_POST['action'] === 'edit' && !$hasEditPermission) {
                $message = 'ليس لديك صلاحية لتعديل المنتجات';
                $messageType = 'danger';
            } elseif ($_POST['action'] === 'delete' && !$hasDeletePermission) {
                $message = 'ليس لديك صلاحية لحذف المنتجات';
                $messageType = 'danger';
            } elseif ($_POST['action'] === 'add' || $_POST['action'] === 'edit') {
                // تسجيل العملية
                logUserAction($pdo, $_SESSION['user_id'], 'product_' . $_POST['action'], 'Product: ' . ($_POST['name'] ?? 'Unknown'));
                // التحقق من صحة البيانات
                $errors = [];

                if (empty(trim($_POST['name']))) {
                    $errors[] = 'اسم المنتج مطلوب';
                }

                if (empty(trim($_POST['code']))) {
                    $errors[] = 'كود المنتج مطلوب';
                }

                if ($_POST['cost_price'] < 0) {
                    $errors[] = 'سعر الشراء لا يمكن أن يكون سالباً';
                }

                if ($_POST['selling_price'] < 0) {
                    $errors[] = 'سعر البيع لا يمكن أن يكون سالباً';
                }

                if ($_POST['quantity'] < 0) {
                    $errors[] = 'الكمية لا يمكن أن تكون سالبة';
                }

                if (!empty($errors)) {
                    $message = implode('<br>', $errors);
                    $messageType = 'danger';
                } else {
                    // التحقق من تكرار الكود
                    if ($_POST['action'] === 'add') {
                        $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE code = ?");
                        $checkStmt->execute([$_POST['code']]);
                        if ($checkStmt->fetchColumn() > 0) {
                            $message = 'يوجد منتج بهذا الكود مسبقاً';
                            $messageType = 'danger';
                        }
                    } else {
                        $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE code = ? AND id != ?");
                        $checkStmt->execute([$_POST['code'], $_POST['id']]);
                        if ($checkStmt->fetchColumn() > 0) {
                            $message = 'يوجد منتج بهذا الكود مسبقاً';
                            $messageType = 'danger';
                        }
                    }

                    if (empty($message)) {
                        $data = [
                            'code' => trim($_POST['code']),
                            'name' => trim($_POST['name']),
                            'description' => trim($_POST['description']),
                            'category_id' => !empty($_POST['category_id']) ? $_POST['category_id'] : null,
                            'unit_id' => !empty($_POST['unit_id']) ? $_POST['unit_id'] : null,
                            'cost_price' => floatval($_POST['cost_price']),
                            'selling_price' => floatval($_POST['selling_price']),
                            'quantity' => intval($_POST['quantity']),
                            'min_quantity' => intval($_POST['min_quantity']),
                            'status' => isset($_POST['status']) ? 1 : 0,
                            'image' => null
                        ];

                        // معالجة رفع الصورة
                        if (isset($_FILES['image']) && $_FILES['image']['error'] === 0) {
                            $uploadDir = 'uploads/products/';
                            if (!file_exists($uploadDir)) {
                                mkdir($uploadDir, 0777, true);
                            }

                            $file = $_FILES['image'];
                            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                            $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

                            if (in_array($file['type'], $allowedTypes) && in_array($fileExtension, $allowedExtensions)) {
                                if ($file['size'] < 5000000) { // 5MB max
                                    $newFileName = 'product_' . time() . '_' . uniqid() . '.' . $fileExtension;
                                    $uploadPath = $uploadDir . $newFileName;

                                    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                                        $data['image'] = $uploadPath;
                                    }
                                } else {
                                    $message = 'حجم الصورة كبير جداً (الحد الأقصى 5 ميجابايت)';
                                    $messageType = 'warning';
                                }
                            } else {
                                $message = 'نوع الملف غير مدعوم (JPG, PNG, GIF, WEBP فقط)';
                                $messageType = 'warning';
                            }
                        }

                        if (empty($message)) {
                            if ($_POST['action'] === 'add') {
                                $sql = "INSERT INTO products (code, name, description, category_id, unit_id, cost_price, selling_price, quantity, min_quantity, image, status)
                                        VALUES (:code, :name, :description, :category_id, :unit_id, :cost_price, :selling_price, :quantity, :min_quantity, :image, :status)";
                                $stmt = $pdo->prepare($sql);
                                $stmt->execute($data);
                                $message = 'تم إضافة المنتج بنجاح';
                                $messageType = 'success';
                            } else {
                                // في حالة التعديل، الاحتفاظ بالصورة القديمة إذا لم يتم رفع صورة جديدة
                                if ($data['image'] === null) {
                                    $sql = "UPDATE products SET
                                            code = :code,
                                            name = :name,
                                            description = :description,
                                            category_id = :category_id,
                                            unit_id = :unit_id,
                                            cost_price = :cost_price,
                                            selling_price = :selling_price,
                                            quantity = :quantity,
                                            min_quantity = :min_quantity,
                                            status = :status,
                                            updated_at = CURRENT_TIMESTAMP
                                            WHERE id = :id";
                                    unset($data['image']);
                                } else {
                                    $sql = "UPDATE products SET
                                            code = :code,
                                            name = :name,
                                            description = :description,
                                            category_id = :category_id,
                                            unit_id = :unit_id,
                                            cost_price = :cost_price,
                                            selling_price = :selling_price,
                                            quantity = :quantity,
                                            min_quantity = :min_quantity,
                                            image = :image,
                                            status = :status,
                                            updated_at = CURRENT_TIMESTAMP
                                            WHERE id = :id";
                                }
                                $data['id'] = $_POST['id'];
                                $stmt = $pdo->prepare($sql);
                                $stmt->execute($data);
                                $message = 'تم تحديث المنتج بنجاح';
                                $messageType = 'success';
                            }
                        }
                    }
                }
            } elseif ($_POST['action'] === 'delete' && isset($_POST['id']) && $hasDeletePermission) {
                // تسجيل عملية الحذف
                logUserAction($pdo, $_SESSION['user_id'], 'product_delete', 'Product ID: ' . $_POST['id']);

                // التحقق من عدم وجود مبيعات أو مشتريات مرتبطة بهذا المنتج
                try {
                    $checkSales = $pdo->prepare("SELECT COUNT(*) FROM sale_items WHERE product_id = ?");
                    $checkSales->execute([$_POST['id']]);
                    $salesCount = $checkSales->fetchColumn();

                    $checkPurchases = $pdo->prepare("SELECT COUNT(*) FROM purchase_items WHERE product_id = ?");
                    $checkPurchases->execute([$_POST['id']]);
                    $purchasesCount = $checkPurchases->fetchColumn();

                    if ($salesCount > 0 || $purchasesCount > 0) {
                        $message = 'لا يمكن حذف هذا المنتج لأنه مرتبط بمعاملات مبيعات أو مشتريات';
                        $messageType = 'warning';
                    } else {
                        // حذف صورة المنتج إذا كانت موجودة
                        $productStmt = $pdo->prepare("SELECT image FROM products WHERE id = ?");
                        $productStmt->execute([$_POST['id']]);
                        $product = $productStmt->fetch();

                        if ($product && !empty($product['image']) && file_exists($product['image'])) {
                            unlink($product['image']);
                        }

                        $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
                        $stmt->execute([$_POST['id']]);
                        $message = 'تم حذف المنتج بنجاح';
                        $messageType = 'success';
                    }
                } catch (PDOException $e) {
                    // إذا لم تكن الجداول موجودة، السماح بالحذف
                    $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $message = 'تم حذف المنتج بنجاح';
                    $messageType = 'success';
                }
            }
        }
    } catch (PDOException $e) {
        $message = 'حدث خطأ: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// جلب المنتجات مع معلومات إضافية
try {
    $productsQuery = "
        SELECT p.*,
               c.name as category_name,
               u.name as unit_name,
               u.symbol as unit_symbol,
               CASE
                   WHEN p.quantity <= p.min_quantity THEN 'low'
                   WHEN p.quantity = 0 THEN 'out'
                   ELSE 'normal'
               END as stock_status,
               (p.selling_price - p.cost_price) as profit_margin,
               ROUND(((p.selling_price - p.cost_price) / p.cost_price) * 100, 2) as profit_percentage
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN units u ON p.unit_id = u.id
        ORDER BY p.name
    ";
    $products = $pdo->query($productsQuery)->fetchAll();
} catch (PDOException $e) {
    // في حالة حدوث خطأ، جلب المنتجات بدون معلومات إضافية
    $products = $pdo->query("SELECT p.*, c.name as category_name, u.name as unit_name FROM products p LEFT JOIN categories c ON p.category_id = c.id LEFT JOIN units u ON p.unit_id = u.id ORDER BY p.name")->fetchAll();
}

// جلب الفئات والوحدات النشطة فقط
try {
    $categories = $pdo->query("SELECT * FROM categories WHERE status = 1 ORDER BY name")->fetchAll();
} catch (PDOException $e) {
    $categories = $pdo->query("SELECT * FROM categories ORDER BY name")->fetchAll();
}

try {
    $units = $pdo->query("SELECT * FROM units WHERE status = 1 ORDER BY name")->fetchAll();
} catch (PDOException $e) {
    $units = $pdo->query("SELECT * FROM units ORDER BY name")->fetchAll();
}

// إحصائيات المنتجات
$totalProducts = count($products);
$activeProducts = count(array_filter($products, function($product) { return $product['status'] == 1; }));
$inactiveProducts = $totalProducts - $activeProducts;
$lowStockProducts = count(array_filter($products, function($product) {
    return $product['quantity'] <= $product['min_quantity'] && $product['quantity'] > 0;
}));
$outOfStockProducts = count(array_filter($products, function($product) {
    return $product['quantity'] == 0;
}));

// حساب إجمالي قيمة المخزون
$totalInventoryValue = 0;
$totalCostValue = 0;
foreach ($products as $product) {
    $totalInventoryValue += ($product['selling_price'] * $product['quantity']);
    $totalCostValue += ($product['cost_price'] * $product['quantity']);
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام نقاط البيع</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-left-color: #fff;
            transform: translateX(-5px);
        }
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .page-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .table-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }
        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 20px 20px 0 0;
        }
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: transform 0.3s ease;
        }
        .product-image:hover {
            transform: scale(1.1);
        }
        .stock-badge {
            font-size: 0.8em;
            padding: 4px 8px;
            border-radius: 10px;
        }
        .profit-indicator {
            font-size: 0.75em;
            padding: 2px 6px;
            border-radius: 8px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            $companyName = getSetting('company_name', 'نظام نقاط البيع');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>

        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link active" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-box me-2"></i>إدارة المنتجات</h2>
                    <small class="text-muted">إضافة وتعديل وحذف المنتجات ومتابعة المخزون</small>
                </div>
                <div class="col-auto">
                    <?php if (hasPermission($pdo, $_SESSION['user_id'], 'products.create') || isAdmin($pdo, $_SESSION['user_id'])): ?>
                    <button type="button" class="btn btn-gradient" data-bs-toggle="modal" data-bs-target="#addProductModal">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة منتج جديد
                    </button>
                    <?php else: ?>
                    <button type="button" class="btn btn-secondary" disabled title="ليس لديك صلاحية لإضافة المنتجات">
                        <i class="bi bi-lock me-2"></i>
                        غير مصرح
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $totalProducts; ?></h4>
                            <p class="mb-0 small">إجمالي المنتجات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-box"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #2ecc71);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $activeProducts; ?></h4>
                            <p class="mb-0 small">منتجات نشطة</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $lowStockProducts; ?></h4>
                            <p class="mb-0 small">مخزون منخفض</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--danger-color), #c0392b);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $outOfStockProducts; ?></h4>
                            <p class="mb-0 small">نفد المخزون</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-x-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalInventoryValue, false); ?></h4>
                            <p class="mb-0 small">قيمة المخزون</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalInventoryValue - $totalCostValue, false); ?></h4>
                            <p class="mb-0 small">الربح المتوقع</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-graph-up"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Table -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0"><i class="bi bi-table me-2"></i>قائمة المنتجات</h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="exportData()">
                        <i class="bi bi-download me-1"></i>تصدير
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshTable()">
                        <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                    </button>
                </div>
            </div>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="productsTable">
                        <thead>
                            <tr>
                                <th width="8%">الكود</th>
                                <th width="8%">الصورة</th>
                                <th width="20%">اسم المنتج</th>
                                <th width="12%">الفئة</th>
                                <th width="10%">الوحدة</th>
                                <th width="10%">سعر الشراء</th>
                                <th width="10%">سعر البيع</th>
                                <th width="8%">الكمية</th>
                                <th width="8%">الربح</th>
                                <th width="8%">الحالة</th>
                                <th width="12%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($products as $product): ?>
                            <tr>
                                <td>
                                    <code class="bg-light px-2 py-1 rounded"><?php echo htmlspecialchars($product['code']); ?></code>
                                </td>
                                <td>
                                    <?php if (!empty($product['image']) && file_exists($product['image'])): ?>
                                        <img src="<?php echo $product['image']; ?>" alt="صورة المنتج" class="product-image"
                                             data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($product['name']); ?>">
                                    <?php else: ?>
                                        <div class="d-flex align-items-center justify-content-center bg-light rounded" style="width: 60px; height: 60px;">
                                            <i class="bi bi-image text-muted fs-4"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                        <?php if (!empty($product['description'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars(substr($product['description'], 0, 50)) . (strlen($product['description']) > 50 ? '...' : ''); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($product['category_name'])): ?>
                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($product['category_name']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($product['unit_name'])): ?>
                                        <span class="badge bg-info">
                                            <?php echo htmlspecialchars($product['unit_name']); ?>
                                            <?php if (!empty($product['unit_symbol'])): ?>
                                                (<?php echo htmlspecialchars($product['unit_symbol']); ?>)
                                            <?php endif; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong class="text-primary"><?php echo formatCurrency($product['cost_price']); ?></strong>
                                </td>
                                <td>
                                    <strong class="text-success"><?php echo formatCurrency($product['selling_price']); ?></strong>
                                </td>
                                <td>
                                    <?php
                                    $stockClass = 'bg-success';
                                    $stockIcon = 'check-circle';
                                    if ($product['quantity'] == 0) {
                                        $stockClass = 'bg-danger';
                                        $stockIcon = 'x-circle';
                                    } elseif ($product['quantity'] <= $product['min_quantity']) {
                                        $stockClass = 'bg-warning';
                                        $stockIcon = 'exclamation-triangle';
                                    }
                                    ?>
                                    <span class="stock-badge badge <?php echo $stockClass; ?>">
                                        <i class="bi bi-<?php echo $stockIcon; ?> me-1"></i>
                                        <?php echo $product['quantity']; ?>
                                    </span>
                                    <br><small class="text-muted">الحد الأدنى: <?php echo $product['min_quantity']; ?></small>
                                </td>
                                <td>
                                    <?php if (isset($product['profit_margin']) && $product['profit_margin'] > 0): ?>
                                        <span class="profit-indicator">
                                            <?php echo formatCurrency($product['profit_margin'], false); ?>
                                            <br><small>(<?php echo $product['profit_percentage']; ?>%)</small>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge <?php echo $product['status'] ? 'bg-success' : 'bg-danger'; ?>">
                                        <?php echo $product['status'] ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <!-- زر العرض متاح للجميع -->
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="viewProduct(<?php echo htmlspecialchars(json_encode($product)); ?>)"
                                                title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </button>

                                        <!-- زر التعديل حسب الصلاحية -->
                                        <?php if (hasPermission($pdo, $_SESSION['user_id'], 'products.edit') || isAdmin($pdo, $_SESSION['user_id'])): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success"
                                                onclick="editProduct(<?php echo htmlspecialchars(json_encode($product)); ?>)"
                                                title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <?php else: ?>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" disabled
                                                title="ليس لديك صلاحية للتعديل">
                                            <i class="bi bi-lock"></i>
                                        </button>
                                        <?php endif; ?>

                                        <!-- زر الحذف حسب الصلاحية -->
                                        <?php if (hasPermission($pdo, $_SESSION['user_id'], 'products.delete') || isAdmin($pdo, $_SESSION['user_id'])): ?>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['name']); ?>')"
                                                title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                        <?php else: ?>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" disabled
                                                title="ليس لديك صلاحية للحذف">
                                            <i class="bi bi-lock"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">كود المنتج</label>
                                <input type="text" class="form-control" name="code" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المنتج</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" rows="3"></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-select" name="category_id">
                                    <option value="">اختر الفئة</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الوحدة</label>
                                <select class="form-select" name="unit_id">
                                    <option value="">اختر الوحدة</option>
                                    <?php foreach ($units as $unit): ?>
                                        <option value="<?php echo $unit['id']; ?>"><?php echo htmlspecialchars($unit['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر الشراء</label>
                                <input type="number" class="form-control" name="cost_price" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر البيع</label>
                                <input type="number" class="form-control" name="selling_price" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" class="form-control" name="quantity" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأدنى للكمية</label>
                                <input type="number" class="form-control" name="min_quantity" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">صورة المنتج</label>
                                <input type="file" class="form-control" name="image" accept="image/*">
                            </div>
                            <div class="col-md-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="status" checked>
                                    <label class="form-check-label">نشط</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Product Modal -->
    <div class="modal fade" id="editProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المنتج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="id" id="edit_id">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">كود المنتج</label>
                                <input type="text" class="form-control" name="code" id="edit_code" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المنتج</label>
                                <input type="text" class="form-control" name="name" id="edit_name" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" id="edit_description" rows="3"></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-select" name="category_id" id="edit_category_id">
                                    <option value="">اختر الفئة</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الوحدة</label>
                                <select class="form-select" name="unit_id" id="edit_unit_id">
                                    <option value="">اختر الوحدة</option>
                                    <?php foreach ($units as $unit): ?>
                                        <option value="<?php echo $unit['id']; ?>"><?php echo htmlspecialchars($unit['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر الشراء</label>
                                <input type="number" class="form-control" name="cost_price" id="edit_cost_price" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر البيع</label>
                                <input type="number" class="form-control" name="selling_price" id="edit_selling_price" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" class="form-control" name="quantity" id="edit_quantity" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأدنى للكمية</label>
                                <input type="number" class="form-control" name="min_quantity" id="edit_min_quantity" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label class="form-label">صورة المنتج</label>
                                <input type="file" class="form-control" name="image" accept="image/*">
                                <div id="current_image" class="mt-2"></div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="status" id="edit_status">
                                    <label class="form-check-label">نشط</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Product Modal -->
    <div class="modal fade" id="deleteProductModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف هذا المنتج؟</p>
                </div>
                <div class="modal-footer">
                    <form method="POST">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" id="delete_id">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Buttons -->
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let productsTable;

        $(document).ready(function() {
            // Initialize DataTable
            productsTable = $('#productsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[2, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [1, 10] }, // Image and Actions columns
                    { searchable: false, targets: [1, 10] } // Image and Actions columns
                ],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="bi bi-file-earmark-excel me-1"></i>Excel',
                        className: 'btn btn-success btn-sm',
                        exportOptions: {
                            columns: [0, 2, 3, 4, 5, 6, 7, 9] // Exclude Image, Profit, and Actions
                        }
                    }
                ]
            });

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Form validation
            $('#addProductForm').on('submit', function(e) {
                if (!validateProductForm(this)) {
                    e.preventDefault();
                    return false;
                }
            });

            $('#editProductForm').on('submit', function(e) {
                if (!validateProductForm(this)) {
                    e.preventDefault();
                    return false;
                }
            });

            // Auto-generate product code
            $('#generateCode').on('click', function() {
                const code = 'PRD' + Date.now().toString().slice(-6);
                $('input[name="code"]').val(code);
            });

            // Calculate profit margin
            $('input[name="cost_price"], input[name="selling_price"]').on('input', function() {
                calculateProfitMargin();
            });

            // Clear form when modal is hidden
            $('#addProductModal').on('hidden.bs.modal', function() {
                $('#addProductForm')[0].reset();
                $('#profit-preview').html('');
            });
        });

        function validateProductForm(form) {
            const name = $(form).find('input[name="name"]').val().trim();
            const code = $(form).find('input[name="code"]').val().trim();
            const costPrice = parseFloat($(form).find('input[name="cost_price"]').val());
            const sellingPrice = parseFloat($(form).find('input[name="selling_price"]').val());
            const quantity = parseInt($(form).find('input[name="quantity"]').val());

            if (name.length < 2) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'يجب أن يكون اسم المنتج أكثر من حرفين'
                });
                return false;
            }

            if (code.length < 2) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'يجب أن يكون كود المنتج أكثر من حرفين'
                });
                return false;
            }

            if (costPrice < 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'سعر الشراء لا يمكن أن يكون سالباً'
                });
                return false;
            }

            if (sellingPrice < 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'سعر البيع لا يمكن أن يكون سالباً'
                });
                return false;
            }

            if (sellingPrice < costPrice) {
                const result = confirm('سعر البيع أقل من سعر الشراء. هل تريد المتابعة؟');
                if (!result) return false;
            }

            if (quantity < 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'الكمية لا يمكن أن تكون سالبة'
                });
                return false;
            }

            return true;
        }

        function calculateProfitMargin() {
            const costPrice = parseFloat($('input[name="cost_price"]').val()) || 0;
            const sellingPrice = parseFloat($('input[name="selling_price"]').val()) || 0;

            if (costPrice > 0 && sellingPrice > 0) {
                const profit = sellingPrice - costPrice;
                const profitPercentage = ((profit / costPrice) * 100).toFixed(2);

                let profitClass = 'text-success';
                if (profit < 0) profitClass = 'text-danger';
                else if (profitPercentage < 10) profitClass = 'text-warning';

                $('#profit-preview').html(`
                    <div class="alert alert-info">
                        <strong>الربح المتوقع:</strong>
                        <span class="${profitClass}">${profit.toFixed(2)} (${profitPercentage}%)</span>
                    </div>
                `);
            } else {
                $('#profit-preview').html('');
            }
        }

        function viewProduct(product) {
            Swal.fire({
                title: product.name,
                html: `
                    <div class="text-start">
                        ${product.image ? `<img src="${product.image}" class="img-fluid mb-3" style="max-height: 200px;">` : ''}
                        <p><strong>الكود:</strong> ${product.code}</p>
                        <p><strong>الوصف:</strong> ${product.description || 'لا يوجد وصف'}</p>
                        <p><strong>الفئة:</strong> ${product.category_name || '-'}</p>
                        <p><strong>الوحدة:</strong> ${product.unit_name || '-'}</p>
                        <p><strong>سعر الشراء:</strong> ${product.cost_price}</p>
                        <p><strong>سعر البيع:</strong> ${product.selling_price}</p>
                        <p><strong>الكمية:</strong> ${product.quantity}</p>
                        <p><strong>الحد الأدنى:</strong> ${product.min_quantity}</p>
                        <p><strong>الحالة:</strong> ${product.status ? 'نشط' : 'غير نشط'}</p>
                    </div>
                `,
                width: 600,
                showCloseButton: true,
                showConfirmButton: false
            });
        }

        function editProduct(product) {
            document.getElementById('edit_id').value = product.id;
            document.getElementById('edit_code').value = product.code || '';
            document.getElementById('edit_name').value = product.name || '';
            document.getElementById('edit_description').value = product.description || '';
            document.getElementById('edit_category_id').value = product.category_id || '';
            document.getElementById('edit_unit_id').value = product.unit_id || '';
            document.getElementById('edit_cost_price').value = product.cost_price || '';
            document.getElementById('edit_selling_price').value = product.selling_price || '';
            document.getElementById('edit_quantity').value = product.quantity || '';
            document.getElementById('edit_min_quantity').value = product.min_quantity || '';
            document.getElementById('edit_status').checked = product.status == 1;

            // عرض الصورة الحالية
            const currentImage = document.getElementById('current_image');
            if (product.image) {
                currentImage.innerHTML = `<img src="${product.image}" alt="صورة المنتج" class="img-thumbnail" style="max-width: 100px;">`;
            } else {
                currentImage.innerHTML = '<p class="text-muted">لا توجد صورة</p>';
            }

            new bootstrap.Modal(document.getElementById('editProductModal')).show();
        }

        function deleteProduct(id, name) {
            Swal.fire({
                title: 'تأكيد الحذف',
                text: `هل أنت متأكد من حذف المنتج "${name}"؟`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Create and submit form
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" value="${id}">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

        function refreshTable() {
            location.reload();
        }

        function exportData() {
            productsTable.button('.buttons-excel').trigger();
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Add loading animation to buttons
        $('form').on('submit', function() {
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            submitBtn.html('<i class="bi bi-hourglass-split me-1"></i>جاري الحفظ...').prop('disabled', true);

            setTimeout(function() {
                submitBtn.html(originalText).prop('disabled', false);
            }, 3000);
        });
    </script>

    <?php
    // إضافة JavaScript للصلاحيات
    generatePermissionJS($pdo, $_SESSION['user_id']);
    ?>
</body>
</html>