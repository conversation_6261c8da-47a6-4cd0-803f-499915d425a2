# 🚀 نظام نقاط البيع المتقدم - الإصدار 3.0.0

## 📋 نظرة عامة

نظام نقاط البيع المتقدم هو حل شامل ومتكامل لإدارة المبيعات والمخزون والمحاسبة. تم تطويره باستخدام أحدث التقنيات لضمان الأداء العالي والأمان الشامل.

## ✨ الميزات الرئيسية

### 🛒 نقطة البيع المتقدمة
- واجهة سهلة ومتطورة للمبيعات
- دعم الباركود والبحث السريع
- طباعة تلقائية للفواتير
- دعم طرق دفع متعددة
- حساب الضرائب التلقائي

### 📦 إدارة المخزون الذكية
- تتبع دقيق للمخزون
- تنبيهات المخزون المنخفض
- إدارة شاملة للمنتجات والفئات
- تتبع تواريخ الانتهاء
- تقارير حركة المخزون

### 💰 النظام المحاسبي المتكامل
- قوائم مالية شاملة (قائمة الدخل، الميزانية العمومية)
- تتبع المصروفات والإيرادات
- إدارة الحسابات والأرصدة
- تقارير مالية مفصلة
- نظام القيد المزدوج

### 📊 التقارير والإحصائيات
- تقارير مبيعات تفصيلية
- تحليل الأداء المالي
- رسوم بيانية تفاعلية
- تصدير التقارير (PDF, Excel)
- إحصائيات في الوقت الفعلي

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة
- تتبع المعاملات والأرصدة
- سجل تفصيلي للعمليات
- إدارة معلومات الاتصال
- تقارير العملاء والموردين

### 🔐 الأمان والصلاحيات
- نظام صلاحيات متقدم
- تشفير البيانات الحساسة
- حماية من الهجمات السيبرانية
- تسجيل شامل للعمليات
- جلسات آمنة ومحمية

## 🛠️ التقنيات المستخدمة

### Backend
- **PHP 7.3+** - لغة البرمجة الأساسية
- **MySQL 8.0** - قاعدة البيانات
- **PDO** - للتفاعل الآمن مع قاعدة البيانات

### Frontend
- **Bootstrap 5** - إطار العمل للتصميم
- **jQuery** - مكتبة JavaScript
- **Chart.js** - الرسوم البيانية التفاعلية
- **DataTables** - جداول البيانات المتقدمة
- **SweetAlert2** - النوافذ المنبثقة الأنيقة

### الأمان
- **HTTPS** - تشفير الاتصالات
- **CSRF Protection** - حماية من هجمات CSRF
- **SQL Injection Prevention** - حماية من حقن SQL
- **XSS Protection** - حماية من هجمات XSS
- **Session Security** - أمان الجلسات

## 📁 هيكل المشروع

```
pos3/
├── index.php                 # صفحة الانطلاق الرئيسية
├── dashboard.php             # لوحة التحكم
├── pos.php                   # نقطة البيع
├── products.php              # إدارة المنتجات
├── categories.php            # إدارة الفئات
├── units.php                 # إدارة الوحدات
├── inventory.php             # إدارة المخزون
├── customers.php             # إدارة العملاء
├── suppliers.php             # إدارة الموردين
├── sales.php                 # سجل المبيعات
├── purchases.php             # سجل المشتريات
├── accounting.php            # النظام المحاسبي
├── reports.php               # التقارير والإحصائيات
├── settings.php              # إعدادات النظام
├── login.php                 # تسجيل الدخول
├── logout.php                # تسجيل الخروج
├── db.php                    # اتصال قاعدة البيانات
├── sidebar.php               # القائمة الجانبية الموحدة
├── check_permissions.php     # نظام الصلاحيات
├── session_security.php      # أمان الجلسات
├── security_check.php        # فحص الأمان الشامل
├── get_settings.php          # إعدادات النظام
├── .htaccess                 # إعدادات الخادم والحماية
├── assets/                   # الملفات الثابتة
│   └── style.css            # ملف الأنماط الرئيسي
└── uploads/                  # ملفات الرفع
    ├── products/            # صور المنتجات
    └── default_logo.svg     # الشعار الافتراضي
```

## 🚀 التثبيت والإعداد

### المتطلبات
- **PHP 7.3** أو أحدث
- **MySQL 8.0** أو أحدث
- **Apache/Nginx** مع mod_rewrite
- **مساحة تخزين** 100MB على الأقل

### خطوات التثبيت

1. **تحميل الملفات**
   ```bash
   git clone [repository-url]
   cd pos3
   ```

2. **إعداد قاعدة البيانات**
   - إنشاء قاعدة بيانات جديدة
   - تشغيل ملف `pos_schema.sql`
   - تحديث بيانات الاتصال في `db.php`

3. **إعداد الصلاحيات**
   ```bash
   chmod 755 uploads/
   chmod 755 uploads/products/
   ```

4. **إعداد الخادم**
   - تأكد من تفعيل mod_rewrite
   - رفع الملفات إلى مجلد الويب

5. **الوصول للنظام**
   - افتح المتصفح واذهب إلى رابط الموقع
   - استخدم بيانات الدخول الافتراضية

## 👤 بيانات الدخول الافتراضية

### مدير النظام
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- **الصلاحيات:** كاملة

### موظف المبيعات
- **اسم المستخدم:** `sales_user`
- **كلمة المرور:** `sales123`
- **الصلاحيات:** نقطة البيع، العملاء، التقارير

### أمين المخزن
- **اسم المستخدم:** `inventory_user`
- **كلمة المرور:** `inventory123`
- **الصلاحيات:** المخزون، المنتجات، المشتريات

## 🔧 الإعدادات المتقدمة

### إعدادات الأمان
- تغيير كلمات المرور الافتراضية
- تحديث مفاتيح التشفير
- تفعيل HTTPS في الإنتاج
- مراجعة صلاحيات قاعدة البيانات

### إعدادات الأداء
- تفعيل ضغط الملفات
- إعداد التخزين المؤقت
- تحسين استعلامات قاعدة البيانات
- مراقبة استهلاك الموارد

### النسخ الاحتياطي
- إعداد نسخ احتياطية دورية
- تصدير قاعدة البيانات
- نسخ احتياطي للملفات المرفوعة
- اختبار استعادة البيانات

## 📞 الدعم والمساعدة

### الوثائق
- دليل المستخدم الشامل
- دليل المطور التقني
- أسئلة شائعة (FAQ)
- فيديوهات تعليمية

### التواصل
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966-XX-XXX-XXXX
- **الدعم الفني:** متاح 24/7

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للمزيد من التفاصيل.

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة دليل المساهمة قبل تقديم أي تحديثات.

## 📈 خارطة الطريق

### الإصدار القادم (3.1.0)
- [ ] تطبيق موبايل
- [ ] API متكامل
- [ ] تقارير متقدمة
- [ ] دعم متعدد اللغات

### المستقبل
- [ ] ذكاء اصطناعي للتنبؤات
- [ ] تكامل مع منصات التجارة الإلكترونية
- [ ] نظام CRM متقدم
- [ ] تحليلات متقدمة

---

**تم التطوير بواسطة فريق التطوير المتخصص**  
**الإصدار:** 3.0.0  
**تاريخ الإصدار:** 2024-01-15
