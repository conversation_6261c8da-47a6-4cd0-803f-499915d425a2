<?php
// سكريپت لتحديث جميع الصفحات لتستخدم الشريط الجانبي الموحد
echo "تحديث الشريط الجانبي في جميع الصفحات...\n\n";

$pages = [
    'dashboard.php',
    'products.php',
    'categories.php', 
    'units.php',
    'pos.php',
    'sales.php',
    'purchases.php',
    'inventory.php',
    'customers.php',
    'suppliers.php',
    'accounting.php',
    'reports.php',
    'settings.php'
];

foreach ($pages as $page) {
    if (file_exists($page)) {
        echo "معالجة الصفحة: $page\n";
        
        $content = file_get_contents($page);
        
        // البحث عن الشريط الجانبي القديم وتحديثه
        $sidebarPattern = '/<!-- Sidebar -->.*?<\/div>/s';
        
        if (preg_match($sidebarPattern, $content)) {
            // استبدال الشريط الجانبي القديم بالجديد
            $newSidebar = "<?php \n    require 'sidebar.php';\n    renderSidebar(\$pdo, \$_SESSION['user_id'], '$page'); \n    ?>";
            $content = preg_replace($sidebarPattern, $newSidebar, $content);
            
            // حفظ الملف المحدث
            if (file_put_contents($page, $content)) {
                echo "تم تحديث الشريط الجانبي في: $page\n";
            } else {
                echo "فشل في تحديث: $page\n";
            }
        } else {
            echo "لم يتم العثور على شريط جانبي في: $page\n";
        }
    } else {
        echo "الملف غير موجود: $page\n";
    }
    echo "---\n";
}

echo "\nتم الانتهاء من تحديث الشريط الجانبي!\n";
echo "الآن جميع الصفحات تستخدم الشريط الجانبي الموحد مع الصلاحيات.\n";
?>
