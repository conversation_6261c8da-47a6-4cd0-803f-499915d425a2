# دليل نظام الصلاحيات الشامل

## 🎯 نظرة عامة

تم تطبيق نظام صلاحيات متقدم وشامل على جميع صفحات النظام، يوفر تحكم دقيق في الوصول والعمليات حسب دور كل مستخدم.

## 👥 الأدوار المتاحة

### 1. مدير النظام (admin)
- **الوصف**: صلاحيات كاملة لجميع أجزاء النظام
- **الصلاحيات**: جميع الصلاحيات بدون استثناء
- **المسؤوليات**: إدارة المستخدمين، الإعدادات، النسخ الاحتياطي

### 2. مدير (manager)
- **الوصف**: صلاحيات إدارية محدودة
- **الصلاحيات**: معظم العمليات عدا إدارة المستخدمين
- **المسؤوليات**: الإشراف العام على العمليات

### 3. كاشير (cashier)
- **الوصف**: متخصص في نقطة البيع والمبيعات
- **الصلاحيات**:
  - الوصول لنقطة البيع
  - تطبيق خصومات
  - عرض وإضافة العملاء
  - عرض المنتجات
  - عرض سجل المبيعات

### 4. مدير المخزون (inventory_manager)
- **الوصف**: متخصص في إدارة المنتجات والمخزون
- **الصلاحيات**:
  - إدارة المنتجات (إضافة، تعديل، عرض)
  - إدارة المخزون وتعديل الكميات
  - إدارة المشتريات
  - إدارة الموردين

### 5. محاسب (accountant)
- **الوصف**: متخصص في التقارير المالية والمحاسبة
- **الصلاحيات**:
  - عرض وتصدير التقارير
  - الوصول للنظام المحاسبي
  - إنشاء القيود المحاسبية
  - عرض سجل المبيعات

### 6. مستخدم عادي (user)
- **الوصف**: صلاحيات محدودة للعرض فقط
- **الصلاحيات**:
  - عرض المنتجات
  - عرض العملاء
  - عرض التقارير
  - عرض سجل المبيعات

## 🔐 الصلاحيات المفصلة

### إدارة المستخدمين
- `users.view` - عرض المستخدمين
- `users.create` - إضافة مستخدمين جدد
- `users.edit` - تعديل بيانات المستخدمين
- `users.delete` - حذف المستخدمين
- `users.permissions` - إدارة صلاحيات المستخدمين

### إدارة المنتجات
- `products.view` - عرض المنتجات
- `products.create` - إضافة منتجات جديدة
- `products.edit` - تعديل بيانات المنتجات
- `products.delete` - حذف المنتجات

### نقطة البيع والمبيعات
- `pos.access` - الوصول لنقطة البيع
- `pos.discount` - تطبيق خصومات
- `pos.refund` - معالجة المرتجعات
- `sales.view` - عرض سجل المبيعات
- `sales.edit` - تعديل المبيعات
- `sales.delete` - حذف المبيعات

### إدارة المخزون
- `inventory.view` - عرض تقارير المخزون
- `inventory.adjust` - تعديل كميات المخزون
- `purchases.view` - عرض سجل المشتريات
- `purchases.create` - إضافة مشتريات جديدة

### العملاء والموردين
- `customers.view` - عرض العملاء
- `customers.create` - إضافة عملاء جدد
- `customers.edit` - تعديل بيانات العملاء
- `suppliers.view` - عرض الموردين
- `suppliers.create` - إضافة موردين جدد

### التقارير والمحاسبة
- `reports.view` - عرض التقارير والإحصائيات
- `reports.export` - تصدير التقارير
- `accounting.view` - الوصول للنظام المحاسبي
- `accounting.entries` - إنشاء وتعديل القيود المحاسبية

### الإعدادات
- `settings.view` - عرض إعدادات النظام
- `settings.edit` - تعديل إعدادات النظام
- `settings.backup` - إنشاء واستعادة النسخ الاحتياطية

## 🧪 المستخدمين التجريبيين

تم إنشاء مستخدمين تجريبيين لاختبار النظام:

| المستخدم | كلمة المرور | الدور | الوصف |
|----------|-------------|-------|--------|
| admin | admin123 | مدير النظام | جميع الصلاحيات |
| cashier | cashier123 | كاشير | نقطة البيع والمبيعات |
| inventory_manager | inventory123 | مدير المخزون | المنتجات والمخزون |
| accountant | accountant123 | محاسب | التقارير والمحاسبة |
| normal_user | user123 | مستخدم عادي | عرض فقط |

## 📋 كيفية اختبار النظام

### 1. اختبار الصلاحيات
- افتح `test_permissions.php` لعرض تقرير شامل عن صلاحيات المستخدم الحالي
- جرب تسجيل الدخول بمستخدمين مختلفين
- لاحظ الاختلاف في الصفحات المتاحة والأزرار الظاهرة

### 2. اختبار الوصول للصفحات
- جرب الوصول لصفحات مختلفة بمستخدمين مختلفين
- ستتم إعادة التوجيه لصفحة "غير مصرح" عند عدم وجود صلاحية
- لاحظ الشريط الجانبي يظهر فقط الصفحات المسموحة

### 3. اختبار العمليات
- جرب إضافة/تعديل/حذف البيانات بمستخدمين مختلفين
- ستظهر رسائل خطأ عند عدم وجود صلاحية
- الأزرار ستكون معطلة أو مخفية حسب الصلاحيات

## 🔧 إدارة الصلاحيات

### إضافة مستخدم جديد
1. اذهب إلى `settings.php`
2. انقر على تبويب "إدارة المستخدمين"
3. انقر "إضافة مستخدم جديد"
4. املأ البيانات واختر الدور المناسب

### تعديل صلاحيات دور
1. اذهب إلى `settings.php`
2. انقر على تبويب "الأدوار والصلاحيات"
3. اختر الدور من القائمة
4. حدد/ألغ تحديد الصلاحيات المطلوبة
5. انقر "حفظ الصلاحيات"

### مراقبة النشاط
- جميع العمليات يتم تسجيلها في جدول `user_logs`
- يمكن مراجعة سجل النشاط من قاعدة البيانات
- يتم تسجيل: المستخدم، العملية، التفاصيل، التوقيت، عنوان IP

## 🛡️ الأمان

### مستويات الحماية
1. **قاعدة البيانات**: استعلامات محضرة، تشفير كلمات المرور
2. **الخادم**: فحص الجلسة، التحقق من الصلاحيات
3. **الواجهة**: إخفاء العناصر، تعطيل الأزرار

### تسجيل العمليات
- جميع العمليات الحساسة يتم تسجيلها
- تتبع محاولات الوصول غير المصرح
- مراجعة دورية لسجل النشاط

## 📁 الملفات المهمة

### ملفات الصلاحيات
- `check_permissions.php` - دوال التحقق من الصلاحيات
- `apply_permissions.php` - تطبيق الصلاحيات على الصفحات
- `sidebar.php` - الشريط الجانبي الموحد
- `no_permission.php` - صفحة عدم وجود صلاحية

### ملفات الاختبار
- `test_permissions.php` - اختبار شامل للصلاحيات
- `create_test_users.php` - إنشاء مستخدمين تجريبيين

### قاعدة البيانات
- `roles` - الأدوار
- `permissions` - الصلاحيات
- `role_permissions` - ربط الأدوار بالصلاحيات
- `user_roles` - ربط المستخدمين بالأدوار
- `user_logs` - سجل عمليات المستخدمين

## 🚀 التطوير المستقبلي

### ميزات مقترحة
- صلاحيات على مستوى البيانات (عرض بيانات محددة فقط)
- صلاحيات مؤقتة مع تاريخ انتهاء
- تنبيهات عند محاولة الوصول غير المصرح
- تقارير مفصلة عن استخدام النظام

### تحسينات الأداء
- تخزين مؤقت للصلاحيات
- تحسين استعلامات قاعدة البيانات
- ضغط البيانات المنقولة

---

**ملاحظة**: هذا النظام يوفر حماية شاملة ومرونة عالية في إدارة الصلاحيات. يمكن تخصيصه وتوسيعه حسب احتياجات المؤسسة.
