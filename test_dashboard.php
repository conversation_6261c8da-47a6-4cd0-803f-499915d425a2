<?php
// اختبار سريع لدوال لوحة التحكم
session_start();
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // admin للاختبار
    $_SESSION['username'] = 'admin';
}

require 'db.php';
require 'get_settings.php';
require 'check_permissions.php';
require 'apply_permissions.php';
require 'sidebar.php';

echo "🧪 اختبار دوال لوحة التحكم...\n\n";

// اختبار الدوال المطلوبة
$functions_to_test = [
    'renderCommonCSS',
    'renderCommonJS', 
    'renderSidebar',
    'hasPermission',
    'isAdmin',
    'formatCurrency',
    'getLowStockAlert'
];

foreach ($functions_to_test as $function) {
    if (function_exists($function)) {
        echo "✅ الدالة $function موجودة\n";
    } else {
        echo "❌ الدالة $function غير موجودة\n";
    }
}

echo "\n📊 اختبار البيانات:\n";

try {
    // اختبار الاستعلامات
    $total_sales_today = $pdo->query("SELECT IFNULL(SUM(total),0) FROM sales WHERE DATE(sale_date) = CURDATE()")->fetchColumn();
    echo "✅ مبيعات اليوم: " . formatCurrency($total_sales_today) . "\n";
    
    $product_count = $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn();
    echo "✅ عدد المنتجات: " . number_format($product_count) . "\n";
    
    $customer_count = $pdo->query("SELECT COUNT(*) FROM customers")->fetchColumn();
    echo "✅ عدد العملاء: " . number_format($customer_count) . "\n";
    
    $supplier_count = $pdo->query("SELECT COUNT(*) FROM suppliers")->fetchColumn();
    echo "✅ عدد الموردين: " . number_format($supplier_count) . "\n";
    
    // اختبار حد المخزون المنخفض
    $lowStockLimit = getLowStockAlert();
    echo "✅ حد المخزون المنخفض: $lowStockLimit\n";
    
    $low_stock_count = $pdo->query("SELECT COUNT(*) FROM products WHERE stock < $lowStockLimit")->fetchColumn();
    echo "✅ منتجات بمخزون منخفض: " . number_format($low_stock_count) . "\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في البيانات: " . $e->getMessage() . "\n";
}

echo "\n🔐 اختبار الصلاحيات:\n";

try {
    $isAdmin = isAdmin($pdo, $_SESSION['user_id']);
    echo "✅ هل المستخدم مدير؟ " . ($isAdmin ? 'نعم' : 'لا') . "\n";
    
    $permissions_to_test = ['pos.access', 'products.create', 'sales.view', 'settings.edit'];
    foreach ($permissions_to_test as $permission) {
        $hasPermission = hasPermission($pdo, $_SESSION['user_id'], $permission);
        echo "✅ صلاحية $permission: " . ($hasPermission ? 'متاحة' : 'غير متاحة') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في الصلاحيات: " . $e->getMessage() . "\n";
}

echo "\n🎨 اختبار دوال العرض:\n";

try {
    echo "✅ اختبار renderCommonCSS:\n";
    ob_start();
    renderCommonCSS();
    $css_output = ob_get_clean();
    echo "   - طول CSS: " . strlen($css_output) . " حرف\n";
    echo "   - يحتوي على :root؟ " . (strpos($css_output, ':root') !== false ? 'نعم' : 'لا') . "\n";
    
    echo "✅ اختبار renderCommonJS:\n";
    ob_start();
    renderCommonJS($pdo, $_SESSION['user_id']);
    $js_output = ob_get_clean();
    echo "   - طول JavaScript: " . strlen($js_output) . " حرف\n";
    echo "   - يحتوي على hasPermission؟ " . (strpos($js_output, 'hasPermission') !== false ? 'نعم' : 'لا') . "\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في دوال العرض: " . $e->getMessage() . "\n";
}

echo "\n🌐 اختبار الصفحات المتاحة:\n";

try {
    $availablePages = getAvailablePages($pdo, $_SESSION['user_id']);
    echo "✅ عدد الصفحات المتاحة: " . count($availablePages) . "\n";
    
    foreach ($availablePages as $page => $info) {
        echo "   - $page: " . $info['title'] . "\n";
    }
    
    $firstPage = getFirstAvailablePage($pdo, $_SESSION['user_id']);
    echo "✅ أول صفحة متاحة: $firstPage\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في الصفحات المتاحة: " . $e->getMessage() . "\n";
}

echo "\n🎉 انتهى الاختبار!\n";
echo "إذا ظهرت جميع العلامات ✅ فإن النظام يعمل بشكل صحيح.\n";
?>
