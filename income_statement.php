<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'generate_income_statement') {
            $dateFrom = $_POST['date_from'];
            $dateTo = $_POST['date_to'];
            
            // جلب الإيرادات من المبيعات الفعلية
            $revenueQuery = "
                SELECT
                    '4100' as account_code,
                    'إيرادات المبيعات' as account_name,
                    COALESCE(SUM(s.total - s.tax), 0) as amount
                FROM sales s
                WHERE DATE(s.sale_date) BETWEEN ? AND ?
                UNION ALL
                SELECT
                    '4200' as account_code,
                    'الضرائب المحصلة' as account_name,
                    COALESCE(SUM(s.tax), 0) as amount
                FROM sales s
                WHERE DATE(s.sale_date) BETWEEN ? AND ?
                AND s.tax > 0
                ORDER BY account_code
            ";
            
            $revenueStmt = $pdo->prepare($revenueQuery);
            $revenueStmt->execute([$dateFrom, $dateTo, $dateFrom, $dateTo]);
            $revenues = $revenueStmt->fetchAll();
            
            // جلب المصروفات من البيانات الفعلية
            $expenseQuery = "
                SELECT
                    '5100' as account_code,
                    'تكلفة البضاعة المباعة' as account_name,
                    COALESCE(SUM(si.quantity * p.cost_price), 0) as amount
                FROM sales s
                JOIN sale_items si ON s.id = si.sale_id
                JOIN products p ON si.product_id = p.id
                WHERE DATE(s.sale_date) BETWEEN ? AND ?
                AND p.cost_price > 0
                UNION ALL
                SELECT
                    CASE COALESCE(e.category, 'other')
                        WHEN 'salary' THEN '5210'
                        WHEN 'rent' THEN '5220'
                        WHEN 'utilities' THEN '5230'
                        ELSE '5240'
                    END as account_code,
                    CASE COALESCE(e.category, 'other')
                        WHEN 'salary' THEN 'مصروفات الرواتب'
                        WHEN 'rent' THEN 'مصروفات الإيجار'
                        WHEN 'utilities' THEN 'مصروفات المرافق'
                        ELSE 'مصروفات أخرى'
                    END as account_name,
                    COALESCE(SUM(e.amount), 0) as amount
                FROM expenses e
                WHERE DATE(e.expense_date) BETWEEN ? AND ?
                GROUP BY COALESCE(e.category, 'other')
                UNION ALL
                SELECT
                    '5210' as account_code,
                    'رواتب الموظفين' as account_name,
                    COALESCE(SUM(pr.net_salary), 0) as amount
                FROM payroll pr
                WHERE DATE(pr.payment_date) BETWEEN ? AND ?
                ORDER BY account_code
            ";
            
            $expenseStmt = $pdo->prepare($expenseQuery);
            $expenseStmt->execute([$dateFrom, $dateTo, $dateFrom, $dateTo, $dateFrom, $dateTo]);
            $expenses = $expenseStmt->fetchAll();
            
            // حساب المجاميع
            $totalRevenue = array_sum(array_column($revenues, 'amount'));
            $totalExpenses = array_sum(array_column($expenses, 'amount'));
            $netIncome = $totalRevenue - $totalExpenses;
            
            // جلب تفاصيل إضافية
            $salesData = getSalesData($pdo, $dateFrom, $dateTo);
            $expensesByCategory = getExpensesByCategory($pdo, $dateFrom, $dateTo);
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'revenues' => $revenues,
                    'expenses' => $expenses,
                    'total_revenue' => $totalRevenue,
                    'total_expenses' => $totalExpenses,
                    'net_income' => $netIncome,
                    'sales_data' => $salesData,
                    'expenses_by_category' => $expensesByCategory,
                    'date_from' => $dateFrom,
                    'date_to' => $dateTo
                ]
            ]);
            exit;
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

function getSalesData($pdo, $dateFrom, $dateTo) {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_sales,
            SUM(total) as total_amount,
            SUM(discount) as total_discount,
            SUM(tax) as total_tax,
            AVG(total) as average_sale
        FROM sales 
        WHERE DATE(sale_date) BETWEEN ? AND ?
    ");
    $stmt->execute([$dateFrom, $dateTo]);
    return $stmt->fetch();
}

function getExpensesByCategory($pdo, $dateFrom, $dateTo) {
    $categories = [];

    try {
        // تكلفة البضاعة المباعة من المبيعات الفعلية
        $cogsStmt = $pdo->prepare("
            SELECT
                'cost_of_goods' as category,
                COUNT(DISTINCT s.id) as count,
                COALESCE(SUM(si.quantity * p.cost_price), 0) as total_amount
            FROM sales s
            JOIN sale_items si ON s.id = si.sale_id
            JOIN products p ON si.product_id = p.id
            WHERE DATE(s.sale_date) BETWEEN ? AND ?
            AND p.cost_price > 0
        ");
        $cogsStmt->execute([$dateFrom, $dateTo]);
        $cogs = $cogsStmt->fetch();
        if ($cogs && $cogs['total_amount'] > 0) {
            $categories[] = $cogs;
        }

        // المصروفات من جدول expenses
        $expensesStmt = $pdo->prepare("
            SELECT
                category,
                COUNT(*) as count,
                SUM(amount) as total_amount
            FROM expenses
            WHERE DATE(expense_date) BETWEEN ? AND ?
            GROUP BY category
            ORDER BY total_amount DESC
        ");
        $expensesStmt->execute([$dateFrom, $dateTo]);
        $expenseCategories = $expensesStmt->fetchAll();
        $categories = array_merge($categories, $expenseCategories);

        // الرواتب من جدول payroll
        $payrollStmt = $pdo->prepare("
            SELECT
                'payroll' as category,
                COUNT(*) as count,
                COALESCE(SUM(net_salary), 0) as total_amount
            FROM payroll
            WHERE DATE(payment_date) BETWEEN ? AND ?
        ");
        $payrollStmt->execute([$dateFrom, $dateTo]);
        $payroll = $payrollStmt->fetch();
        if ($payroll && $payroll['total_amount'] > 0) {
            $categories[] = $payroll;
        }

        // ترتيب حسب المبلغ
        usort($categories, function($a, $b) {
            return $b['total_amount'] <=> $a['total_amount'];
        });

        return $categories;

    } catch (PDOException $e) {
        return [];
    }
}

// إعدادات النظام
$currency = getSetting('currency', 'ر.س');
$companyName = getSetting('company_name', 'نظام نقاط البيع');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الدخل - <?php echo $companyName; ?></title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .report-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .report-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            text-align: center;
        }
        
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .filter-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .income-statement-table {
            border: none;
        }
        
        .income-statement-table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
        }
        
        .income-statement-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .section-header {
            background: #f8f9fa;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .total-row {
            background: #e3f2fd;
            font-weight: bold;
            border-top: 2px solid var(--primary-color);
        }
        
        .net-income-positive {
            background: #e8f5e8;
            color: var(--success-color);
            font-weight: bold;
        }
        
        .net-income-negative {
            background: #ffebee;
            color: var(--danger-color);
            font-weight: bold;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            .filter-card, .btn, .chart-container {
                display: none !important;
            }
            .content-card {
                box-shadow: none;
                border: 1px solid #000;
                margin: 0;
                padding: 20px;
            }
            .report-header {
                background: white;
                box-shadow: none;
                border-bottom: 2px solid #000;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <div class="report-container fade-in">
        <!-- Report Header -->
        <div class="report-header">
            <div class="row align-items-center">
                <div class="col-md-2">
                    <?php
                    $companyLogo = getSetting('company_logo');
                    if (!empty($companyLogo) && file_exists($companyLogo)):
                    ?>
                        <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 80px; max-height: 80px; border-radius: 10px;">
                    <?php endif; ?>
                </div>
                <div class="col-md-8">
                    <h2 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h2>
                    <h4 class="text-primary mb-0">قائمة الدخل</h4>
                    <p class="text-muted mb-0" id="reportPeriod">للفترة من __ إلى __</p>
                </div>
                <div class="col-md-2 text-end">
                    <a href="accounting.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-right me-1"></i>العودة
                    </a>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card">
            <form id="incomeStatementForm">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="dateFrom" name="date_from"
                               value="<?php echo date('Y-m-01'); ?>" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="dateTo" name="date_to"
                               value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-gradient w-100">
                            <i class="bi bi-calculator me-1"></i>إنشاء التقرير
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="printReport()">
                            <i class="bi bi-printer me-1"></i>طباعة
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4" id="summaryCards" style="display: none;">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="totalRevenueCard">0</h4>
                            <p class="mb-0 small">إجمالي الإيرادات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-arrow-up-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--danger-color), #c0392b);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="totalExpensesCard">0</h4>
                            <p class="mb-0 small">إجمالي المصروفات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-arrow-down-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" id="netIncomeCard">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="netIncomeAmount">0</h4>
                            <p class="mb-0 small">صافي الدخل</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-graph-up" id="netIncomeIcon"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="profitMargin">0%</h4>
                            <p class="mb-0 small">هامش الربح</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-percent"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4" id="chartsRow" style="display: none;">
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-pie-chart me-2"></i>توزيع الإيرادات والمصروفات</h5>
                    <div class="chart-container">
                        <canvas id="revenueExpenseChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-bar-chart me-2"></i>المصروفات حسب الفئة</h5>
                    <div class="chart-container">
                        <canvas id="expensesCategoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Income Statement Table -->
        <div class="content-card" id="incomeStatementTable" style="display: none;">
            <h5 class="mb-4 text-center">
                <i class="bi bi-file-earmark-text me-2"></i>قائمة الدخل التفصيلية
            </h5>

            <div class="table-responsive">
                <table class="table income-statement-table">
                    <thead>
                        <tr>
                            <th width="60%">البيان</th>
                            <th width="20%" class="text-center">كود الحساب</th>
                            <th width="20%" class="text-end">المبلغ</th>
                        </tr>
                    </thead>
                    <tbody id="incomeStatementBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Sales Analysis -->
        <div class="content-card" id="salesAnalysis" style="display: none;">
            <h5 class="mb-3"><i class="bi bi-graph-up me-2"></i>تحليل المبيعات</h5>
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>عدد المبيعات:</strong></td>
                            <td id="totalSalesCount">-</td>
                        </tr>
                        <tr>
                            <td><strong>إجمالي المبيعات:</strong></td>
                            <td id="totalSalesAmount">-</td>
                        </tr>
                        <tr>
                            <td><strong>متوسط البيع:</strong></td>
                            <td id="averageSale">-</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>إجمالي الخصومات:</strong></td>
                            <td id="totalDiscounts">-</td>
                        </tr>
                        <tr>
                            <td><strong>إجمالي الضرائب:</strong></td>
                            <td id="totalTaxes">-</td>
                        </tr>
                        <tr>
                            <td><strong>صافي المبيعات:</strong></td>
                            <td id="netSales">-</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Initial Message -->
        <div class="content-card text-center" id="initialMessage">
            <i class="bi bi-file-earmark-text display-1 text-muted"></i>
            <h5 class="text-muted mt-3">قائمة الدخل</h5>
            <p class="text-muted">اختر الفترة الزمنية واضغط "إنشاء التقرير" لعرض قائمة الدخل التفصيلية</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let currency = '<?php echo $currency; ?>';
        let revenueExpenseChart, expensesCategoryChart;

        $(document).ready(function() {
            $('#incomeStatementForm').on('submit', function(e) {
                e.preventDefault();
                generateIncomeStatement();
            });
        });

        function generateIncomeStatement() {
            const dateFrom = $('#dateFrom').val();
            const dateTo = $('#dateTo').val();

            if (!dateFrom || !dateTo) {
                Swal.fire('خطأ', 'يرجى تحديد الفترة الزمنية', 'error');
                return;
            }

            if (dateFrom > dateTo) {
                Swal.fire('خطأ', 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error');
                return;
            }

            // Show loading
            Swal.fire({
                title: 'جاري إنشاء التقرير...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.post('income_statement.php', {
                action: 'generate_income_statement',
                date_from: dateFrom,
                date_to: dateTo
            })
            .done(function(response) {
                const result = JSON.parse(response);
                if (result.success) {
                    displayIncomeStatement(result.data);
                    Swal.close();
                } else {
                    Swal.fire('خطأ', result.message, 'error');
                }
            })
            .fail(function() {
                Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
            });
        }

        function displayIncomeStatement(data) {
            // Update report period
            const dateFrom = new Date(data.date_from).toLocaleDateString('ar-SA');
            const dateTo = new Date(data.date_to).toLocaleDateString('ar-SA');
            $('#reportPeriod').text(`للفترة من ${dateFrom} إلى ${dateTo}`);

            // Update summary cards
            $('#totalRevenueCard').text(formatCurrency(data.total_revenue));
            $('#totalExpensesCard').text(formatCurrency(data.total_expenses));
            $('#netIncomeAmount').text(formatCurrency(data.net_income));

            // Calculate profit margin
            const profitMargin = data.total_revenue > 0 ? ((data.net_income / data.total_revenue) * 100).toFixed(1) : 0;
            $('#profitMargin').text(profitMargin + '%');

            // Update net income card style
            const netIncomeCard = $('#netIncomeCard');
            const netIncomeIcon = $('#netIncomeIcon');

            if (data.net_income >= 0) {
                netIncomeCard.css('background', 'linear-gradient(135deg, var(--success-color), #2ecc71)');
                netIncomeIcon.removeClass('bi-graph-down').addClass('bi-graph-up');
            } else {
                netIncomeCard.css('background', 'linear-gradient(135deg, var(--danger-color), #c0392b)');
                netIncomeIcon.removeClass('bi-graph-up').addClass('bi-graph-down');
            }

            // Build income statement table
            buildIncomeStatementTable(data);

            // Update sales analysis
            updateSalesAnalysis(data.sales_data);

            // Create charts
            createCharts(data);

            // Show all sections
            $('#initialMessage').hide();
            $('#summaryCards, #chartsRow, #incomeStatementTable, #salesAnalysis').show();
        }

        function buildIncomeStatementTable(data) {
            let tableBody = '';

            // الإيرادات
            tableBody += '<tr class="section-header"><td colspan="3"><strong>الإيرادات</strong></td></tr>';

            if (data.revenues.length > 0) {
                data.revenues.forEach(revenue => {
                    if (revenue.amount > 0) {
                        tableBody += `
                            <tr>
                                <td>&nbsp;&nbsp;&nbsp;&nbsp;${revenue.account_name}</td>
                                <td class="text-center">${revenue.account_code}</td>
                                <td class="text-end">${formatCurrency(revenue.amount)}</td>
                            </tr>
                        `;
                    }
                });
            } else {
                tableBody += '<tr><td>&nbsp;&nbsp;&nbsp;&nbsp;لا توجد إيرادات</td><td class="text-center">-</td><td class="text-end">0.00</td></tr>';
            }

            tableBody += `
                <tr class="total-row">
                    <td><strong>إجمالي الإيرادات</strong></td>
                    <td class="text-center">-</td>
                    <td class="text-end"><strong>${formatCurrency(data.total_revenue)}</strong></td>
                </tr>
            `;

            // المصروفات
            tableBody += '<tr class="section-header"><td colspan="3"><strong>المصروفات</strong></td></tr>';

            if (data.expenses.length > 0) {
                data.expenses.forEach(expense => {
                    if (expense.amount > 0) {
                        tableBody += `
                            <tr>
                                <td>&nbsp;&nbsp;&nbsp;&nbsp;${expense.account_name}</td>
                                <td class="text-center">${expense.account_code}</td>
                                <td class="text-end">${formatCurrency(expense.amount)}</td>
                            </tr>
                        `;
                    }
                });
            } else {
                tableBody += '<tr><td>&nbsp;&nbsp;&nbsp;&nbsp;لا توجد مصروفات</td><td class="text-center">-</td><td class="text-end">0.00</td></tr>';
            }

            tableBody += `
                <tr class="total-row">
                    <td><strong>إجمالي المصروفات</strong></td>
                    <td class="text-center">-</td>
                    <td class="text-end"><strong>${formatCurrency(data.total_expenses)}</strong></td>
                </tr>
            `;

            // صافي الدخل
            const netIncomeClass = data.net_income >= 0 ? 'net-income-positive' : 'net-income-negative';
            tableBody += `
                <tr class="${netIncomeClass}">
                    <td><strong>صافي الدخل ${data.net_income >= 0 ? '(ربح)' : '(خسارة)'}</strong></td>
                    <td class="text-center">-</td>
                    <td class="text-end"><strong>${formatCurrency(data.net_income)}</strong></td>
                </tr>
            `;

            $('#incomeStatementBody').html(tableBody);
        }

        function updateSalesAnalysis(salesData) {
            $('#totalSalesCount').text(salesData.total_sales || 0);
            $('#totalSalesAmount').text(formatCurrency(salesData.total_amount || 0));
            $('#averageSale').text(formatCurrency(salesData.average_sale || 0));
            $('#totalDiscounts').text(formatCurrency(salesData.total_discount || 0));
            $('#totalTaxes').text(formatCurrency(salesData.total_tax || 0));

            const netSales = (salesData.total_amount || 0) - (salesData.total_discount || 0);
            $('#netSales').text(formatCurrency(netSales));
        }

        function createCharts(data) {
            // Revenue vs Expense Chart
            const revenueExpenseCtx = document.getElementById('revenueExpenseChart').getContext('2d');

            if (revenueExpenseChart) {
                revenueExpenseChart.destroy();
            }

            revenueExpenseChart = new Chart(revenueExpenseCtx, {
                type: 'doughnut',
                data: {
                    labels: ['الإيرادات', 'المصروفات'],
                    datasets: [{
                        data: [data.total_revenue, data.total_expenses],
                        backgroundColor: ['#27ae60', '#e74c3c'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + formatCurrency(context.parsed);
                                }
                            }
                        }
                    }
                }
            });

            // Expenses by Category Chart
            const expensesCategoryCtx = document.getElementById('expensesCategoryChart').getContext('2d');

            if (expensesCategoryChart) {
                expensesCategoryChart.destroy();
            }

            const categoryLabels = [];
            const categoryData = [];
            const categoryColors = ['#3498db', '#e74c3c', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#95a5a6'];

            data.expenses_by_category.forEach((category, index) => {
                const categoryNames = {
                    'salary': 'رواتب',
                    'rent': 'إيجار',
                    'utilities': 'مرافق',
                    'supplies': 'مستلزمات',
                    'maintenance': 'صيانة',
                    'marketing': 'تسويق',
                    'other': 'أخرى'
                };

                categoryLabels.push(categoryNames[category.category] || category.category);
                categoryData.push(category.total_amount);
            });

            expensesCategoryChart = new Chart(expensesCategoryCtx, {
                type: 'bar',
                data: {
                    labels: categoryLabels,
                    datasets: [{
                        label: 'المبلغ',
                        data: categoryData,
                        backgroundColor: categoryColors.slice(0, categoryLabels.length),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        function formatCurrency(amount) {
            return parseFloat(amount || 0).toFixed(2) + ' ' + currency;
        }

        function printReport() {
            window.print();
        }
    </script>
</body>
</html>
