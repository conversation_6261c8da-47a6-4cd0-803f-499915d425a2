<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';

// إنشاء جدول الإعدادات إذا لم يكن موجوداً
$pdo->exec("CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type VARCHAR(50) DEFAULT 'text',
    category VARCHAR(50) DEFAULT 'general',
    description VARCHAR(255),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)");

// إدراج الإعدادات الافتراضية
$defaultSettings = [
    ['company_name', 'شركة نقاط البيع', 'text', 'company', 'اسم الشركة'],
    ['company_phone', '0501234567', 'text', 'company', 'هاتف الشركة'],
    ['company_email', '<EMAIL>', 'email', 'company', 'بريد الشركة الإلكتروني'],
    ['company_address', 'الرياض، المملكة العربية السعودية', 'textarea', 'company', 'عنوان الشركة'],
    ['company_logo', '', 'file', 'company', 'شعار الشركة'],
    ['currency', 'ر.س', 'text', 'general', 'العملة المستخدمة'],
    ['tax_rate', '15', 'number', 'general', 'نسبة الضريبة (%)'],
    ['low_stock_alert', '10', 'number', 'inventory', 'تنبيه المخزون المنخفض'],
    ['receipt_footer', 'شكراً لزيارتكم', 'textarea', 'receipt', 'تذييل الفاتورة'],
    ['auto_backup', '1', 'checkbox', 'system', 'النسخ الاحتياطي التلقائي'],
    ['backup_frequency', 'daily', 'select', 'system', 'تكرار النسخ الاحتياطي'],
    ['theme_color', '#3498db', 'color', 'appearance', 'لون النظام الأساسي'],
    ['items_per_page', '20', 'number', 'general', 'عدد العناصر في الصفحة'],
    ['date_format', 'Y-m-d', 'select', 'general', 'تنسيق التاريخ'],
    ['time_format', 'H:i:s', 'select', 'general', 'تنسيق الوقت']
];

foreach ($defaultSettings as $setting) {
    $stmt = $pdo->prepare("INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, category, description) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute($setting);
}

// معالجة تحديث الإعدادات
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // معالجة رفع الشعار
        if (isset($_FILES['company_logo']) && $_FILES['company_logo']['error'] === 0) {
            $uploadDir = 'uploads/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $file = $_FILES['company_logo'];
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
            $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

            if (in_array($file['type'], $allowedTypes) && in_array($fileExtension, $allowedExtensions)) {
                if ($file['size'] < 5000000) { // 5MB max
                    $newFileName = 'logo_' . time() . '.' . $fileExtension;
                    $uploadPath = $uploadDir . $newFileName;

                    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                        // حذف الشعار القديم
                        $oldLogo = $pdo->query("SELECT setting_value FROM system_settings WHERE setting_key = 'company_logo'")->fetchColumn();
                        if ($oldLogo && file_exists($oldLogo)) {
                            unlink($oldLogo);
                        }

                        // تحديث مسار الشعار
                        $stmt = $pdo->prepare("UPDATE system_settings SET setting_value = ? WHERE setting_key = 'company_logo'");
                        $stmt->execute([$uploadPath]);
                    }
                }
            }
        }

        // معالجة باقي الإعدادات
        foreach ($_POST as $key => $value) {
            if ($key !== 'submit') {
                $stmt = $pdo->prepare("UPDATE system_settings SET setting_value = ? WHERE setting_key = ?");
                $stmt->execute([$value, $key]);
            }
        }
        $message = '<div class="alert alert-success"><i class="bi bi-check-circle me-2"></i>تم حفظ الإعدادات بنجاح!</div>';
    } catch (PDOException $e) {
        $message = '<div class="alert alert-danger"><i class="bi bi-exclamation-triangle me-2"></i>خطأ في حفظ الإعدادات: ' . $e->getMessage() . '</div>';
    }
}

// جلب الإعدادات الحالية
$settings = [];
$stmt = $pdo->query("SELECT * FROM system_settings ORDER BY category, setting_key");
while ($row = $stmt->fetch()) {
    $settings[$row['category']][] = $row;
}

// إحصائيات النظام
$stats = [
    'total_users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'total_products' => $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn(),
    'total_sales' => $pdo->query("SELECT COUNT(*) FROM sales")->fetchColumn(),
    'total_customers' => $pdo->query("SELECT COUNT(*) FROM customers")->fetchColumn(),
    'database_size' => $pdo->query("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb FROM information_schema.tables WHERE table_schema = 'pos3'")->fetchColumn()
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - نظام نقاط البيع</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f6f9;
            margin: 0;
            padding: 0;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            border-left-color: #fff;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        
        .settings-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 20px;
        }
        
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            margin: 0 auto 15px;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            $companyName = getSetting('company_name', 'نظام نقاط البيع');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>

        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link active" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0">إعدادات النظام</h2>
                    <small class="text-muted">إدارة وتخصيص إعدادات نظام نقاط البيع</small>
                </div>
                <div class="col-auto">
                    <span class="badge bg-primary fs-6">
                        <i class="bi bi-gear me-1"></i>
                        الإعدادات
                    </span>
                </div>
            </div>
        </div>

        <?php echo $message; ?>

        <!-- System Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="bi bi-people"></i>
                </div>
                <h6 class="text-muted mb-1">المستخدمين</h6>
                <h4 class="mb-0"><?php echo number_format($stats['total_users']); ?></h4>
            </div>

            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="bi bi-box"></i>
                </div>
                <h6 class="text-muted mb-1">المنتجات</h6>
                <h4 class="mb-0"><?php echo number_format($stats['total_products']); ?></h4>
            </div>

            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="bi bi-graph-up"></i>
                </div>
                <h6 class="text-muted mb-1">المبيعات</h6>
                <h4 class="mb-0"><?php echo number_format($stats['total_sales']); ?></h4>
            </div>

            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="bi bi-database"></i>
                </div>
                <h6 class="text-muted mb-1">حجم قاعدة البيانات</h6>
                <h4 class="mb-0"><?php echo $stats['database_size']; ?> MB</h4>
            </div>
        </div>

        <!-- Settings Form -->
        <form method="POST" class="settings-form" enctype="multipart/form-data">
            <?php
            $categoryTitles = [
                'company' => ['title' => 'معلومات الشركة', 'icon' => 'bi-building'],
                'general' => ['title' => 'الإعدادات العامة', 'icon' => 'bi-gear'],
                'inventory' => ['title' => 'إعدادات المخزون', 'icon' => 'bi-boxes'],
                'receipt' => ['title' => 'إعدادات الفاتورة', 'icon' => 'bi-receipt'],
                'system' => ['title' => 'إعدادات النظام', 'icon' => 'bi-cpu'],
                'appearance' => ['title' => 'المظهر والألوان', 'icon' => 'bi-palette']
            ];

            foreach ($settings as $category => $categorySettings):
                if (isset($categoryTitles[$category])):
            ?>
            <div class="settings-card">
                <h5 class="mb-4">
                    <i class="<?php echo $categoryTitles[$category]['icon']; ?> me-2 text-primary"></i>
                    <?php echo $categoryTitles[$category]['title']; ?>
                </h5>

                <div class="row">
                    <?php foreach ($categorySettings as $setting): ?>
                    <div class="col-md-6 mb-3">
                        <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                            <?php echo $setting['description']; ?>
                        </label>

                        <?php
                        switch ($setting['setting_type']) {
                            case 'textarea':
                                echo '<textarea class="form-control" id="' . $setting['setting_key'] . '" name="' . $setting['setting_key'] . '" rows="3">' . htmlspecialchars($setting['setting_value']) . '</textarea>';
                                break;
                            case 'select':
                                $options = [];
                                if ($setting['setting_key'] === 'backup_frequency') {
                                    $options = ['daily' => 'يومي', 'weekly' => 'أسبوعي', 'monthly' => 'شهري'];
                                } elseif ($setting['setting_key'] === 'date_format') {
                                    $options = ['Y-m-d' => '2024-01-15', 'd/m/Y' => '15/01/2024', 'd-m-Y' => '15-01-2024'];
                                } elseif ($setting['setting_key'] === 'time_format') {
                                    $options = ['H:i:s' => '14:30:45', 'h:i A' => '2:30 PM', 'H:i' => '14:30'];
                                }
                                echo '<select class="form-select" id="' . $setting['setting_key'] . '" name="' . $setting['setting_key'] . '">';
                                foreach ($options as $value => $label) {
                                    $selected = ($setting['setting_value'] === $value) ? 'selected' : '';
                                    echo '<option value="' . $value . '" ' . $selected . '>' . $label . '</option>';
                                }
                                echo '</select>';
                                break;
                            case 'checkbox':
                                $checked = ($setting['setting_value'] === '1') ? 'checked' : '';
                                echo '<div class="form-check form-switch">';
                                echo '<input class="form-check-input" type="checkbox" id="' . $setting['setting_key'] . '" name="' . $setting['setting_key'] . '" value="1" ' . $checked . '>';
                                echo '<label class="form-check-label" for="' . $setting['setting_key'] . '">تفعيل</label>';
                                echo '</div>';
                                break;
                            case 'color':
                                echo '<input type="color" class="form-control form-control-color" id="' . $setting['setting_key'] . '" name="' . $setting['setting_key'] . '" value="' . htmlspecialchars($setting['setting_value']) . '">';
                                break;
                            case 'number':
                                echo '<input type="number" class="form-control" id="' . $setting['setting_key'] . '" name="' . $setting['setting_key'] . '" value="' . htmlspecialchars($setting['setting_value']) . '">';
                                break;
                            case 'email':
                                echo '<input type="email" class="form-control" id="' . $setting['setting_key'] . '" name="' . $setting['setting_key'] . '" value="' . htmlspecialchars($setting['setting_value']) . '">';
                                break;
                            case 'file':
                                echo '<input type="file" class="form-control" id="' . $setting['setting_key'] . '" name="' . $setting['setting_key'] . '" accept="image/*">';
                                if (!empty($setting['setting_value']) && file_exists($setting['setting_value'])) {
                                    echo '<div class="mt-2">';
                                    echo '<img src="' . $setting['setting_value'] . '" alt="شعار الشركة" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">';
                                    echo '<br><small class="text-muted">الشعار الحالي</small>';
                                    echo '</div>';
                                } else {
                                    echo '<small class="text-muted">لم يتم رفع شعار بعد</small>';
                                }
                                break;
                            default:
                                echo '<input type="text" class="form-control" id="' . $setting['setting_key'] . '" name="' . $setting['setting_key'] . '" value="' . htmlspecialchars($setting['setting_value']) . '">';
                        }
                        ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php
                endif;
            endforeach;
            ?>

            <!-- Save Button -->
            <div class="settings-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">حفظ الإعدادات</h6>
                        <small class="text-muted">تأكد من مراجعة جميع الإعدادات قبل الحفظ</small>
                    </div>
                    <div>
                        <button type="submit" name="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </form>

        <!-- Advanced Tools -->
        <div class="settings-card">
            <h5 class="mb-4">
                <i class="bi bi-tools me-2 text-danger"></i>
                أدوات متقدمة
            </h5>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <i class="bi bi-download fs-1 text-warning mb-3"></i>
                            <h6>نسخة احتياطية</h6>
                            <p class="text-muted small">إنشاء نسخة احتياطية من قاعدة البيانات</p>
                            <button class="btn btn-warning btn-sm" onclick="createBackup()">
                                <i class="bi bi-download me-1"></i>
                                إنشاء نسخة احتياطية
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <i class="bi bi-arrow-clockwise fs-1 text-info mb-3"></i>
                            <h6>تحديث النظام</h6>
                            <p class="text-muted small">فحص وتحديث النظام إلى أحدث إصدار</p>
                            <button class="btn btn-info btn-sm" onclick="checkUpdates()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                فحص التحديثات
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card border-danger">
                        <div class="card-body text-center">
                            <i class="bi bi-trash fs-1 text-danger mb-3"></i>
                            <h6>مسح البيانات</h6>
                            <p class="text-muted small">مسح جميع البيانات (احذر!)</p>
                            <button class="btn btn-danger btn-sm" onclick="confirmClearData()">
                                <i class="bi bi-trash me-1"></i>
                                مسح البيانات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="settings-card">
            <h5 class="mb-4">
                <i class="bi bi-info-circle me-2 text-info"></i>
                معلومات النظام
            </h5>

            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>إصدار PHP:</strong></td>
                            <td><?php echo phpversion(); ?></td>
                        </tr>
                        <tr>
                            <td><strong>إصدار MySQL:</strong></td>
                            <td><?php echo $pdo->query('SELECT VERSION()')->fetchColumn(); ?></td>
                        </tr>
                        <tr>
                            <td><strong>نظام التشغيل:</strong></td>
                            <td><?php echo php_uname('s') . ' ' . php_uname('r'); ?></td>
                        </tr>
                        <tr>
                            <td><strong>ذاكرة PHP:</strong></td>
                            <td><?php echo ini_get('memory_limit'); ?></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>إصدار النظام:</strong></td>
                            <td>1.0.0</td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ التثبيت:</strong></td>
                            <td><?php echo date('Y-m-d H:i:s'); ?></td>
                        </tr>
                        <tr>
                            <td><strong>المنطقة الزمنية:</strong></td>
                            <td><?php echo date_default_timezone_get(); ?></td>
                        </tr>
                        <tr>
                            <td><strong>حالة النظام:</strong></td>
                            <td><span class="badge bg-success">يعمل بشكل طبيعي</span></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function createBackup() {
            if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
                // يمكن إضافة وظيفة النسخ الاحتياطي هنا
                alert('تم إنشاء النسخة الاحتياطية بنجاح!');
            }
        }

        function checkUpdates() {
            alert('النظام محدث إلى أحدث إصدار!');
        }

        function confirmClearData() {
            if (confirm('تحذير: هذا الإجراء سيمسح جميع البيانات نهائياً!\nهل أنت متأكد من المتابعة؟')) {
                if (confirm('تأكيد أخير: سيتم مسح جميع البيانات ولا يمكن التراجع عن هذا الإجراء!')) {
                    // يمكن إضافة وظيفة مسح البيانات هنا
                    alert('تم مسح البيانات!');
                }
            }
        }

        // تأثيرات بصرية للنموذج
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.settings-form');
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                input.addEventListener('change', function() {
                    this.style.borderColor = '#28a745';
                    setTimeout(() => {
                        this.style.borderColor = '';
                    }, 1000);
                });
            });

            // معاينة الشعار قبل الرفع
            const logoInput = document.getElementById('company_logo');
            if (logoInput) {
                logoInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            // إنشاء معاينة للصورة
                            let preview = document.getElementById('logo-preview');
                            if (!preview) {
                                preview = document.createElement('div');
                                preview.id = 'logo-preview';
                                preview.className = 'mt-2';
                                logoInput.parentNode.appendChild(preview);
                            }
                            preview.innerHTML = `
                                <img src="${e.target.result}" alt="معاينة الشعار" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                <br><small class="text-success">معاينة الشعار الجديد</small>
                            `;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        });
    </script>
</body>
</html>
