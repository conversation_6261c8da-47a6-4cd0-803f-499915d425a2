<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';

// إنشاء جدول الأدوار والصلاحيات
$pdo->exec("CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

$pdo->exec("CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

$pdo->exec("CREATE TABLE IF NOT EXISTS role_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_permission (role_id, permission_id)
)");

$pdo->exec("CREATE TABLE IF NOT EXISTS user_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    assigned_by INT,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id),
    UNIQUE KEY unique_user_role (user_id, role_id)
)");

// إضافة أعمدة إضافية لجدول users
try {
    $pdo->exec("ALTER TABLE users ADD COLUMN role VARCHAR(50) DEFAULT 'user'");
    $pdo->exec("ALTER TABLE users ADD COLUMN is_active TINYINT(1) DEFAULT 1");
    $pdo->exec("ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL");
    $pdo->exec("ALTER TABLE users ADD COLUMN created_by INT NULL");
    $pdo->exec("ALTER TABLE users ADD COLUMN phone VARCHAR(20) NULL");
    $pdo->exec("ALTER TABLE users ADD COLUMN email VARCHAR(100) NULL");
} catch (PDOException $e) {
    // الأعمدة موجودة مسبقاً
}

// إدراج الأدوار الافتراضية
$defaultRoles = [
    ['admin', 'مدير النظام', 'صلاحيات كاملة لإدارة النظام'],
    ['manager', 'مدير', 'صلاحيات إدارية محدودة'],
    ['cashier', 'كاشير', 'صلاحيات نقطة البيع والمبيعات'],
    ['inventory_manager', 'مدير المخزون', 'إدارة المنتجات والمخزون'],
    ['accountant', 'محاسب', 'الوصول للتقارير المالية والمحاسبة'],
    ['user', 'مستخدم', 'صلاحيات محدودة للعرض فقط']
];

foreach ($defaultRoles as $role) {
    $stmt = $pdo->prepare("INSERT IGNORE INTO roles (name, display_name, description) VALUES (?, ?, ?)");
    $stmt->execute($role);
}

// إدراج الصلاحيات الافتراضية
$defaultPermissions = [
    // إدارة المستخدمين
    ['users.view', 'عرض المستخدمين', 'users', 'عرض قائمة المستخدمين'],
    ['users.create', 'إضافة مستخدمين', 'users', 'إضافة مستخدمين جدد'],
    ['users.edit', 'تعديل المستخدمين', 'users', 'تعديل بيانات المستخدمين'],
    ['users.delete', 'حذف المستخدمين', 'users', 'حذف المستخدمين'],
    ['users.permissions', 'إدارة الصلاحيات', 'users', 'تعديل صلاحيات المستخدمين'],
    
    // إدارة المنتجات
    ['products.view', 'عرض المنتجات', 'products', 'عرض قائمة المنتجات'],
    ['products.create', 'إضافة منتجات', 'products', 'إضافة منتجات جديدة'],
    ['products.edit', 'تعديل المنتجات', 'products', 'تعديل بيانات المنتجات'],
    ['products.delete', 'حذف المنتجات', 'products', 'حذف المنتجات'],
    
    // نقطة البيع
    ['pos.access', 'الوصول لنقطة البيع', 'sales', 'استخدام نقطة البيع'],
    ['pos.discount', 'تطبيق خصومات', 'sales', 'تطبيق خصومات على المبيعات'],
    ['pos.refund', 'المرتجعات', 'sales', 'معالجة المرتجعات'],
    ['sales.view', 'عرض المبيعات', 'sales', 'عرض سجل المبيعات'],
    
    // إدارة المخزون
    ['inventory.view', 'عرض المخزون', 'inventory', 'عرض تقارير المخزون'],
    ['inventory.adjust', 'تعديل المخزون', 'inventory', 'تعديل كميات المخزون'],
    
    // التقارير والمحاسبة
    ['reports.view', 'عرض التقارير', 'reports', 'عرض التقارير والإحصائيات'],
    ['accounting.view', 'عرض المحاسبة', 'accounting', 'الوصول للنظام المحاسبي'],
    
    // الإعدادات
    ['settings.view', 'عرض الإعدادات', 'settings', 'عرض إعدادات النظام'],
    ['settings.edit', 'تعديل الإعدادات', 'settings', 'تعديل إعدادات النظام']
];

foreach ($defaultPermissions as $permission) {
    $stmt = $pdo->prepare("INSERT IGNORE INTO permissions (name, display_name, category, description) VALUES (?, ?, ?, ?)");
    $stmt->execute($permission);
}

// ربط الصلاحيات بالأدوار
$adminRoleId = $pdo->query("SELECT id FROM roles WHERE name = 'admin'")->fetchColumn();
if ($adminRoleId) {
    // إعطاء المدير جميع الصلاحيات
    $allPermissions = $pdo->query("SELECT id FROM permissions")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($allPermissions as $permissionId) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
        $stmt->execute([$adminRoleId, $permissionId]);
    }
}

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'get_users':
                $stmt = $pdo->query("
                    SELECT u.*, r.display_name as role_name, r.id as role_id
                    FROM users u 
                    LEFT JOIN user_roles ur ON u.id = ur.user_id 
                    LEFT JOIN roles r ON ur.role_id = r.id 
                    ORDER BY u.created_at DESC
                ");
                $users = $stmt->fetchAll();
                echo json_encode(['success' => true, 'users' => $users]);
                exit;
                
            case 'create_user':
                $username = $_POST['username'];
                $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
                $email = $_POST['email'] ?? null;
                $phone = $_POST['phone'] ?? null;
                $role_id = $_POST['role_id'];
                
                $stmt = $pdo->prepare("INSERT INTO users (username, password, email, phone, created_by) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$username, $password, $email, $phone, $_SESSION['user_id']]);
                $userId = $pdo->lastInsertId();
                
                // ربط المستخدم بالدور
                $stmt = $pdo->prepare("INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES (?, ?, ?)");
                $stmt->execute([$userId, $role_id, $_SESSION['user_id']]);
                
                echo json_encode(['success' => true, 'message' => 'تم إنشاء المستخدم بنجاح']);
                exit;
                
            case 'update_user':
                $userId = $_POST['user_id'];
                $username = $_POST['username'];
                $email = $_POST['email'] ?? null;
                $phone = $_POST['phone'] ?? null;
                $role_id = $_POST['role_id'];
                $is_active = $_POST['is_active'] ?? 0;
                
                $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, phone = ?, is_active = ? WHERE id = ?");
                $stmt->execute([$username, $email, $phone, $is_active, $userId]);
                
                // تحديث الدور
                $stmt = $pdo->prepare("DELETE FROM user_roles WHERE user_id = ?");
                $stmt->execute([$userId]);
                
                $stmt = $pdo->prepare("INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES (?, ?, ?)");
                $stmt->execute([$userId, $role_id, $_SESSION['user_id']]);
                
                echo json_encode(['success' => true, 'message' => 'تم تحديث المستخدم بنجاح']);
                exit;
                
            case 'delete_user':
                $userId = $_POST['user_id'];
                if ($userId == $_SESSION['user_id']) {
                    echo json_encode(['success' => false, 'message' => 'لا يمكن حذف حسابك الخاص']);
                    exit;
                }
                
                $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                
                echo json_encode(['success' => true, 'message' => 'تم حذف المستخدم بنجاح']);
                exit;
                
            case 'get_roles':
                $stmt = $pdo->query("SELECT * FROM roles WHERE is_active = 1 ORDER BY name");
                $roles = $stmt->fetchAll();
                echo json_encode(['success' => true, 'roles' => $roles]);
                exit;
                
            case 'get_permissions':
                $stmt = $pdo->query("SELECT * FROM permissions ORDER BY category, name");
                $permissions = $stmt->fetchAll();
                echo json_encode(['success' => true, 'permissions' => $permissions]);
                exit;

            case 'get_role_permissions':
                $roleId = $_POST['role_id'];
                $stmt = $pdo->prepare("
                    SELECT p.*,
                           CASE WHEN rp.permission_id IS NOT NULL THEN 1 ELSE 0 END as has_permission
                    FROM permissions p
                    LEFT JOIN role_permissions rp ON p.id = rp.permission_id AND rp.role_id = ?
                    ORDER BY p.category, p.name
                ");
                $stmt->execute([$roleId]);
                $permissions = $stmt->fetchAll();
                echo json_encode(['success' => true, 'permissions' => $permissions]);
                exit;
                
            case 'update_role_permissions':
                $roleId = $_POST['role_id'];
                $permissions = $_POST['permissions'] ?? [];
                
                // حذف الصلاحيات الحالية
                $stmt = $pdo->prepare("DELETE FROM role_permissions WHERE role_id = ?");
                $stmt->execute([$roleId]);
                
                // إضافة الصلاحيات الجديدة
                foreach ($permissions as $permissionId) {
                    $stmt = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
                    $stmt->execute([$roleId, $permissionId]);
                }
                
                echo json_encode(['success' => true, 'message' => 'تم تحديث صلاحيات الدور بنجاح']);
                exit;
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

// جلب البيانات
$users = $pdo->query("
    SELECT u.*, r.display_name as role_name, r.id as role_id
    FROM users u 
    LEFT JOIN user_roles ur ON u.id = ur.user_id 
    LEFT JOIN roles r ON ur.role_id = r.id 
    ORDER BY u.created_at DESC
")->fetchAll();

$roles = $pdo->query("SELECT * FROM roles WHERE is_active = 1 ORDER BY name")->fetchAll();
$permissions = $pdo->query("SELECT * FROM permissions ORDER BY category, name")->fetchAll();

// تجميع الصلاحيات حسب الفئة
$permissionsByCategory = [];
foreach ($permissions as $permission) {
    $permissionsByCategory[$permission['category']][] = $permission;
}

// إحصائيات النظام
$stats = [
    'total_users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'total_products' => $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn(),
    'total_sales' => $pdo->query("SELECT COUNT(*) FROM sales")->fetchColumn(),
    'total_customers' => $pdo->query("SELECT COUNT(*) FROM customers")->fetchColumn()
];

$currency = getSetting('currency', 'ر.س');
$companyName = getSetting('company_name', 'نظام نقاط البيع');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - <?php echo $companyName; ?></title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }

        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 0;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-left-color: #fff;
            transform: translateX(-5px);
        }

        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }

        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .page-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .nav-tabs .nav-link {
            border-radius: 15px 15px 0 0;
            border: none;
            background: rgba(255,255,255,0.7);
            color: var(--primary-color);
            margin-left: 5px;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link.active {
            background: white;
            color: var(--primary-color);
            font-weight: 600;
        }

        .tab-content {
            background: white;
            border-radius: 0 20px 20px 20px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }

        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }

        .permission-group {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }

        .permission-item {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>
        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link active" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-gear me-2"></i>إعدادات النظام</h2>
                    <small class="text-muted">إدارة المستخدمين والصلاحيات وإعدادات النظام</small>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-gradient" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise me-2"></i>تحديث البيانات
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo number_format($stats['total_users']); ?></h4>
                            <p class="mb-0 small">إجمالي المستخدمين</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #2ecc71);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo number_format($stats['total_products']); ?></h4>
                            <p class="mb-0 small">إجمالي المنتجات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-box"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo number_format($stats['total_sales']); ?></h4>
                            <p class="mb-0 small">إجمالي المبيعات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-graph-up"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo number_format($stats['total_customers']); ?></h4>
                            <p class="mb-0 small">إجمالي العملاء</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-people-fill"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tabs -->
        <div class="content-card">
            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                        <i class="bi bi-people me-2"></i>إدارة المستخدمين
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="roles-tab" data-bs-toggle="tab" data-bs-target="#roles" type="button" role="tab">
                        <i class="bi bi-shield-check me-2"></i>الأدوار والصلاحيات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                        <i class="bi bi-gear me-2"></i>إعدادات النظام
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab">
                        <i class="bi bi-cloud-download me-2"></i>النسخ الاحتياطي
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="settingsTabContent">
                <!-- Users Management Tab -->
                <div class="tab-pane fade show active" id="users" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0"><i class="bi bi-people me-2"></i>إدارة المستخدمين</h5>
                        <button type="button" class="btn btn-gradient" onclick="showAddUserModal()">
                            <i class="bi bi-person-plus me-2"></i>إضافة مستخدم جديد
                        </button>
                    </div>

                    <div class="table-container">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="usersTable">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-primary text-white me-2" style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                    <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                                </div>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                                    <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                        <span class="badge bg-success ms-1">أنت</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['email'] ?? 'غير محدد'); ?></td>
                                        <td><?php echo htmlspecialchars($user['phone'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo htmlspecialchars($user['role_name'] ?? 'غير محدد'); ?></span>
                                        </td>
                                        <td>
                                            <?php if ($user['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($user['created_at'] ?? 'now')); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="editUser(<?php echo $user['id']; ?>)">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteUser(<?php echo $user['id']; ?>)">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Roles and Permissions Tab -->
                <div class="tab-pane fade" id="roles" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>الأدوار والصلاحيات</h5>
                        <button type="button" class="btn btn-gradient" onclick="showAddRoleModal()">
                            <i class="bi bi-plus-circle me-2"></i>إضافة دور جديد
                        </button>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <h6>الأدوار المتاحة</h6>
                            <div class="list-group" id="rolesList">
                                <?php foreach ($roles as $role): ?>
                                <a href="#" class="list-group-item list-group-item-action" data-role-id="<?php echo $role['id']; ?>" onclick="selectRole(<?php echo $role['id']; ?>)">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <i class="bi bi-shield-check me-2 text-primary"></i>
                                            <?php echo htmlspecialchars($role['display_name']); ?>
                                        </h6>
                                        <small class="text-muted"><?php echo $role['name']; ?></small>
                                    </div>
                                    <p class="mb-1 text-muted"><?php echo htmlspecialchars($role['description']); ?></p>
                                    <small class="text-info">
                                        <i class="bi bi-people me-1"></i>
                                        <?php
                                        $userCount = $pdo->prepare("SELECT COUNT(*) FROM user_roles WHERE role_id = ?");
                                        $userCount->execute([$role['id']]);
                                        echo $userCount->fetchColumn();
                                        ?> مستخدم
                                    </small>
                                </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h6>صلاحيات الدور المحدد</h6>
                            <div id="rolePermissions">
                                <div class="text-center text-muted py-5">
                                    <i class="bi bi-shield-check display-4"></i>
                                    <p class="mt-3">اختر دوراً من القائمة لعرض وتعديل صلاحياته</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Settings Tab -->
                <div class="tab-pane fade" id="system" role="tabpanel">
                    <h5 class="mb-4"><i class="bi bi-gear me-2"></i>إعدادات النظام</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">الإعدادات العامة</h6>
                                </div>
                                <div class="card-body">
                                    <form id="systemSettingsForm">
                                        <div class="mb-3">
                                            <label class="form-label">اسم الشركة</label>
                                            <input type="text" class="form-control" name="company_name" value="<?php echo htmlspecialchars($companyName); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">العملة</label>
                                            <input type="text" class="form-control" name="currency" value="<?php echo htmlspecialchars($currency); ?>">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">نسبة الضريبة (%)</label>
                                            <input type="number" class="form-control" name="tax_rate" value="15" min="0" max="100">
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-circle me-2"></i>حفظ الإعدادات
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">معلومات النظام</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>إصدار PHP:</strong></td>
                                            <td><?php echo phpversion(); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>إصدار MySQL:</strong></td>
                                            <td><?php echo $pdo->query('SELECT VERSION()')->fetchColumn(); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>نظام التشغيل:</strong></td>
                                            <td><?php echo php_uname('s'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ذاكرة PHP:</strong></td>
                                            <td><?php echo ini_get('memory_limit'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Backup Tab -->
                <div class="tab-pane fade" id="backup" role="tabpanel">
                    <h5 class="mb-4"><i class="bi bi-cloud-download me-2"></i>النسخ الاحتياطي</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="bi bi-download display-4 text-primary mb-3"></i>
                                    <h6>إنشاء نسخة احتياطية</h6>
                                    <p class="text-muted small">إنشاء نسخة احتياطية من قاعدة البيانات</p>
                                    <button class="btn btn-primary" onclick="createBackup()">
                                        <i class="bi bi-download me-2"></i>إنشاء نسخة احتياطية
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="bi bi-upload display-4 text-success mb-3"></i>
                                    <h6>استعادة نسخة احتياطية</h6>
                                    <p class="text-muted small">استعادة البيانات من نسخة احتياطية</p>
                                    <input type="file" class="form-control mb-2" accept=".sql">
                                    <button class="btn btn-success" onclick="restoreBackup()">
                                        <i class="bi bi-upload me-2"></i>استعادة النسخة
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="bi bi-gear display-4 text-warning mb-3"></i>
                                    <h6>إعدادات النسخ الاحتياطي</h6>
                                    <p class="text-muted small">تكوين النسخ الاحتياطي التلقائي</p>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="autoBackup">
                                        <label class="form-check-label" for="autoBackup">نسخ تلقائي</label>
                                    </div>
                                    <button class="btn btn-warning" onclick="saveBackupSettings()">
                                        <i class="bi bi-gear me-2"></i>حفظ الإعدادات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الدور</label>
                            <select class="form-select" name="role_id" required>
                                <option value="">اختر الدور</option>
                                <?php foreach ($roles as $role): ?>
                                <option value="<?php echo $role['id']; ?>"><?php echo htmlspecialchars($role['display_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">حفظ المستخدم</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المستخدم</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" name="user_id">
                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الدور</label>
                            <select class="form-select" name="role_id" required>
                                <option value="">اختر الدور</option>
                                <?php foreach ($roles as $role): ?>
                                <option value="<?php echo $role['id']; ?>"><?php echo htmlspecialchars($role['display_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="is_active" value="1">
                                <label class="form-check-label">المستخدم نشط</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="updateUser()">تحديث المستخدم</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let usersTable;
        let selectedRoleId = null;

        $(document).ready(function() {
            // Initialize DataTables
            usersTable = $('#usersTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 10,
                order: [[5, 'desc']] // Sort by creation date
            });
        });

        function showAddUserModal() {
            $('#addUserForm')[0].reset();
            $('#addUserModal').modal('show');
        }

        function saveUser() {
            const formData = new FormData($('#addUserForm')[0]);
            formData.append('action', 'create_user');

            $.ajax({
                url: '',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم بنجاح',
                            text: response.message,
                            timer: 2000,
                            showConfirmButton: false
                        });
                        $('#addUserModal').modal('hide');
                        refreshUsers();
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: response.message
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ في الاتصال'
                    });
                }
            });
        }

        function editUser(userId) {
            // Get user data and populate edit form
            const row = usersTable.row($(`button[onclick="editUser(${userId})"]`).closest('tr'));
            const data = row.data();

            // You would typically fetch user data via AJAX here
            // For now, we'll show the modal
            $('#editUserModal').modal('show');
        }

        function updateUser() {
            const formData = new FormData($('#editUserForm')[0]);
            formData.append('action', 'update_user');

            $.ajax({
                url: '',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم بنجاح',
                            text: response.message,
                            timer: 2000,
                            showConfirmButton: false
                        });
                        $('#editUserModal').modal('hide');
                        refreshUsers();
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: response.message
                        });
                    }
                }
            });
        }

        function deleteUser(userId) {
            Swal.fire({
                title: 'تأكيد الحذف',
                text: 'هل أنت متأكد من حذف هذا المستخدم؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#d33'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '',
                        method: 'POST',
                        data: {
                            action: 'delete_user',
                            user_id: userId
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'تم الحذف',
                                    text: response.message,
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                                refreshUsers();
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'خطأ',
                                    text: response.message
                                });
                            }
                        }
                    });
                }
            });
        }

        function selectRole(roleId) {
            selectedRoleId = roleId;

            // Highlight selected role
            $('#rolesList .list-group-item').removeClass('active');
            $(`#rolesList .list-group-item[data-role-id="${roleId}"]`).addClass('active');

            // Load role permissions
            loadRolePermissions(roleId);
        }

        function loadRolePermissions(roleId) {
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'get_role_permissions',
                    role_id: roleId
                },
                success: function(response) {
                    if (response.success) {
                        displayPermissions(response.permissions, roleId);
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ في تحميل الصلاحيات'
                    });
                }
            });
        }

        function displayPermissions(permissions, roleId) {
            let html = '<form id="rolePermissionsForm">';
            html += '<input type="hidden" name="role_id" value="' + roleId + '">';

            const categories = {};
            permissions.forEach(permission => {
                if (!categories[permission.category]) {
                    categories[permission.category] = [];
                }
                categories[permission.category].push(permission);
            });

            Object.keys(categories).forEach(category => {
                html += '<div class="permission-group">';
                html += '<h6 class="text-primary mb-3">';
                html += '<i class="bi bi-shield-check me-2"></i>' + getCategoryName(category);
                html += '</h6>';

                categories[category].forEach(permission => {
                    const isChecked = permission.has_permission == 1 ? 'checked' : '';
                    html += '<div class="permission-item">';
                    html += '<div class="form-check">';
                    html += '<input class="form-check-input" type="checkbox" name="permissions[]" value="' + permission.id + '" id="perm_' + permission.id + '" ' + isChecked + '>';
                    html += '<label class="form-check-label" for="perm_' + permission.id + '">';
                    html += '<strong>' + permission.display_name + '</strong>';
                    html += '<br><small class="text-muted">' + permission.description + '</small>';
                    html += '</label>';
                    html += '</div>';
                    html += '</div>';
                });

                html += '</div>';
            });

            html += '<div class="mt-4">';
            html += '<button type="button" class="btn btn-primary me-2" onclick="saveRolePermissions()">';
            html += '<i class="bi bi-check-circle me-2"></i>حفظ الصلاحيات';
            html += '</button>';
            html += '<button type="button" class="btn btn-outline-secondary me-2" onclick="selectAllPermissions()">';
            html += '<i class="bi bi-check-all me-2"></i>تحديد الكل';
            html += '</button>';
            html += '<button type="button" class="btn btn-outline-secondary" onclick="deselectAllPermissions()">';
            html += '<i class="bi bi-x-circle me-2"></i>إلغاء تحديد الكل';
            html += '</button>';
            html += '</div>';
            html += '</form>';

            $('#rolePermissions').html(html);
        }

        function getCategoryName(category) {
            const names = {
                'users': 'إدارة المستخدمين',
                'products': 'إدارة المنتجات',
                'sales': 'المبيعات ونقطة البيع',
                'inventory': 'إدارة المخزون',
                'customers': 'العملاء والموردين',
                'reports': 'التقارير والمحاسبة',
                'settings': 'الإعدادات'
            };
            return names[category] || category;
        }

        function saveRolePermissions() {
            const formData = new FormData($('#rolePermissionsForm')[0]);
            formData.append('action', 'update_role_permissions');

            // إظهار مؤشر التحميل
            Swal.fire({
                title: 'جاري الحفظ...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم بنجاح',
                            text: response.message,
                            timer: 2000,
                            showConfirmButton: false
                        });
                        // إعادة تحميل الصلاحيات لإظهار التحديث
                        if (selectedRoleId) {
                            loadRolePermissions(selectedRoleId);
                        }
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: response.message
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ في الاتصال'
                    });
                }
            });
        }

        function selectAllPermissions() {
            $('#rolePermissionsForm input[type="checkbox"]').prop('checked', true);
        }

        function deselectAllPermissions() {
            $('#rolePermissionsForm input[type="checkbox"]').prop('checked', false);
        }

        function refreshUsers() {
            location.reload();
        }

        function refreshData() {
            location.reload();
        }

        function createBackup() {
            Swal.fire({
                title: 'إنشاء نسخة احتياطية',
                text: 'هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، أنشئ',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم بنجاح',
                        text: 'تم إنشاء النسخة الاحتياطية بنجاح',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }
            });
        }

        function restoreBackup() {
            Swal.fire({
                title: 'استعادة نسخة احتياطية',
                text: 'تحذير: سيتم استبدال البيانات الحالية!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، استعد',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#d33'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم بنجاح',
                        text: 'تم استعادة النسخة الاحتياطية بنجاح',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }
            });
        }

        function saveBackupSettings() {
            Swal.fire({
                icon: 'success',
                title: 'تم بنجاح',
                text: 'تم حفظ إعدادات النسخ الاحتياطي',
                timer: 2000,
                showConfirmButton: false
            });
        }
    </script>
</body>
</html>
