<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require 'db.php';
require 'get_settings.php';
require 'check_permissions.php';

$requestedPage = $_GET['page'] ?? 'غير محدد';
$companyName = getSetting('company_name', 'نظام نقاط البيع');
$userRole = getUserRole($pdo, $_SESSION['user_id']);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير مصرح - <?php echo $companyName; ?></title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .permission-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .permission-icon {
            font-size: 5rem;
            color: #e74c3c;
            margin-bottom: 20px;
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .user-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .fade-in {
            animation: fadeIn 0.8s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="permission-card fade-in">
        <i class="bi bi-shield-exclamation permission-icon"></i>
        
        <h2 class="text-danger mb-3">غير مصرح بالوصول</h2>
        
        <p class="text-muted mb-4">
            عذراً، ليس لديك صلاحية للوصول إلى الصفحة المطلوبة.
        </p>
        
        <div class="user-info">
            <h6 class="mb-2">معلومات المستخدم:</h6>
            <p class="mb-1"><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($_SESSION['username']); ?></p>
            <p class="mb-1"><strong>الدور:</strong> 
                <span class="badge bg-info"><?php echo htmlspecialchars($userRole['display_name'] ?? 'غير محدد'); ?></span>
            </p>
            <p class="mb-0"><strong>الصفحة المطلوبة:</strong> <?php echo htmlspecialchars($requestedPage); ?></p>
        </div>
        
        <div class="alert alert-warning">
            <i class="bi bi-info-circle me-2"></i>
            <strong>تنبيه:</strong> إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع مدير النظام.
        </div>
        
        <div class="d-grid gap-2">
            <a href="dashboard.php" class="btn btn-gradient">
                <i class="bi bi-house-door me-2"></i>العودة للوحة التحكم
            </a>
            
            <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                <i class="bi bi-arrow-left me-2"></i>العودة للصفحة السابقة
            </button>
        </div>
        
        <hr class="my-4">
        
        <div class="text-center">
            <h6 class="text-muted mb-3">الصلاحيات المتاحة لك:</h6>
            <div class="row">
                <?php 
                $userPermissions = getUserPermissions($pdo, $_SESSION['user_id']);
                if (!empty($userPermissions)): 
                    $permissionsByCategory = [];
                    foreach ($userPermissions as $permission) {
                        $permissionsByCategory[$permission['category']][] = $permission;
                    }
                ?>
                    <?php foreach ($permissionsByCategory as $category => $permissions): ?>
                    <div class="col-12 mb-3">
                        <h6 class="text-primary mb-2">
                            <?php 
                            $categoryNames = [
                                'users' => 'إدارة المستخدمين',
                                'products' => 'إدارة المنتجات',
                                'sales' => 'المبيعات ونقطة البيع',
                                'inventory' => 'إدارة المخزون',
                                'customers' => 'العملاء والموردين',
                                'reports' => 'التقارير والمحاسبة',
                                'settings' => 'الإعدادات'
                            ];
                            echo $categoryNames[$category] ?? $category;
                            ?>
                        </h6>
                        <?php foreach ($permissions as $permission): ?>
                        <span class="badge bg-light text-dark me-1 mb-1">
                            <?php echo htmlspecialchars($permission['display_name']); ?>
                        </span>
                        <?php endforeach; ?>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12">
                        <p class="text-muted">لا توجد صلاحيات محددة لحسابك.</p>
                        <p class="text-info">يرجى التواصل مع مدير النظام لتعيين الصلاحيات المناسبة.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="mt-4 text-center">
            <small class="text-muted">
                <i class="bi bi-clock me-1"></i>
                تم الوصول في: <?php echo date('Y-m-d H:i:s'); ?>
            </small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تسجيل محاولة الوصول غير المصرح
        console.log('محاولة وصول غير مصرح للصفحة: <?php echo $requestedPage; ?>');
        
        // إضافة تأثير بصري
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الاهتزاز للأيقونة
            const icon = document.querySelector('.permission-icon');
            setInterval(() => {
                icon.style.animation = 'none';
                setTimeout(() => {
                    icon.style.animation = 'shake 0.5s ease-in-out';
                }, 10);
            }, 5000);
        });
        
        // منع الرجوع للصفحة المحظورة
        if (document.referrer && document.referrer.includes('<?php echo $requestedPage; ?>')) {
            history.replaceState(null, null, 'no_permission.php');
        }
    </script>
</body>
</html>
