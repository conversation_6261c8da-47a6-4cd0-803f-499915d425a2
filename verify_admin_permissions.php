<?php
// سكريپت للتحقق من صلاحيات admin وعرض تقرير مفصل
require 'db.php';
require 'check_permissions.php';
require 'apply_permissions.php';

try {
    echo "🔍 التحقق من صلاحيات مستخدم admin...\n\n";
    
    // البحث عن مستخدم admin
    $adminStmt = $pdo->prepare("SELECT id, username FROM users WHERE username = 'admin'");
    $adminStmt->execute();
    $admin = $adminStmt->fetch();
    
    if (!$admin) {
        echo "❌ لم يتم العثور على مستخدم admin!\n";
        exit;
    }
    
    $adminId = $admin['id'];
    echo "✅ مستخدم admin موجود - المعرف: $adminId\n\n";
    
    // التحقق من كونه مدير
    $isAdmin = isAdmin($pdo, $adminId);
    echo "🔐 هل admin مدير النظام؟ " . ($isAdmin ? "✅ نعم" : "❌ لا") . "\n";
    
    // جلب دور المستخدم
    $userRole = getUserRole($pdo, $adminId);
    echo "👤 دور المستخدم: " . ($userRole['display_name'] ?? 'غير محدد') . "\n\n";
    
    // جلب جميع صلاحيات admin
    $adminPermissions = getUserPermissions($pdo, $adminId);
    echo "📊 إجمالي صلاحيات admin: " . count($adminPermissions) . " صلاحية\n\n";
    
    // جلب جميع الصلاحيات المتاحة في النظام
    $allPermissions = $pdo->query("SELECT COUNT(*) as total FROM permissions")->fetch();
    echo "📋 إجمالي الصلاحيات في النظام: " . $allPermissions['total'] . " صلاحية\n\n";
    
    // مقارنة العدد
    if (count($adminPermissions) == $allPermissions['total']) {
        echo "🎉 ممتاز! admin يملك جميع الصلاحيات المتاحة\n\n";
    } else {
        echo "⚠️ تحذير! admin لا يملك جميع الصلاحيات\n";
        echo "   الصلاحيات الحالية: " . count($adminPermissions) . "\n";
        echo "   الصلاحيات المطلوبة: " . $allPermissions['total'] . "\n\n";
    }
    
    // تجميع الصلاحيات حسب الفئة
    $permissionsByCategory = [];
    foreach ($adminPermissions as $permission) {
        $permissionsByCategory[$permission['category']][] = $permission;
    }
    
    // عرض الصلاحيات مجمعة
    echo "📂 الصلاحيات مجمعة حسب الفئة:\n\n";
    
    $categoryNames = [
        'users' => 'إدارة المستخدمين',
        'products' => 'إدارة المنتجات',
        'sales' => 'المبيعات ونقطة البيع',
        'inventory' => 'إدارة المخزون',
        'customers' => 'إدارة العملاء',
        'suppliers' => 'إدارة الموردين',
        'reports' => 'التقارير',
        'accounting' => 'المحاسبة',
        'settings' => 'الإعدادات'
    ];
    
    foreach ($permissionsByCategory as $category => $permissions) {
        $categoryDisplayName = $categoryNames[$category] ?? $category;
        echo "🔹 $categoryDisplayName (" . count($permissions) . " صلاحيات):\n";
        
        foreach ($permissions as $permission) {
            echo "   ✅ " . $permission['display_name'] . " (" . $permission['name'] . ")\n";
        }
        echo "\n";
    }
    
    // اختبار بعض الصلاحيات المهمة
    echo "🧪 اختبار الصلاحيات المهمة:\n\n";
    
    $importantPermissions = [
        'users.create' => 'إضافة مستخدمين',
        'users.permissions' => 'إدارة الصلاحيات',
        'products.create' => 'إضافة منتجات',
        'pos.access' => 'الوصول لنقطة البيع',
        'sales.view' => 'عرض المبيعات',
        'settings.edit' => 'تعديل الإعدادات',
        'reports.view' => 'عرض التقارير'
    ];
    
    foreach ($importantPermissions as $permissionName => $displayName) {
        $hasPermission = hasPermission($pdo, $adminId, $permissionName);
        $status = $hasPermission ? "✅ يملك" : "❌ لا يملك";
        echo "   $status $displayName ($permissionName)\n";
    }
    
    echo "\n";
    
    // اختبار الوصول للصفحات
    echo "🌐 اختبار الوصول للصفحات:\n\n";
    
    $testPages = [
        'dashboard.php' => 'لوحة التحكم',
        'products.php' => 'إدارة المنتجات',
        'pos.php' => 'نقطة البيع',
        'sales.php' => 'سجل المبيعات',
        'customers.php' => 'إدارة العملاء',
        'settings.php' => 'إعدادات النظام',
        'reports.php' => 'التقارير'
    ];
    
    foreach ($testPages as $page => $title) {
        $canAccess = canAccessPage($pdo, $adminId, $page);
        $status = $canAccess ? "✅ يمكن الوصول" : "❌ لا يمكن الوصول";
        echo "   $status $title ($page)\n";
    }
    
    echo "\n";
    
    // إحصائيات إضافية
    echo "📈 إحصائيات إضافية:\n\n";
    
    // عدد الصلاحيات لكل فئة
    foreach ($permissionsByCategory as $category => $permissions) {
        $categoryDisplayName = $categoryNames[$category] ?? $category;
        echo "   📊 $categoryDisplayName: " . count($permissions) . " صلاحيات\n";
    }
    
    echo "\n";
    
    // التحقق من قاعدة البيانات مباشرة
    echo "🔍 التحقق المباشر من قاعدة البيانات:\n\n";
    
    $directCheckStmt = $pdo->prepare("
        SELECT 
            COUNT(DISTINCT p.id) as permissions_count,
            COUNT(DISTINCT rp.permission_id) as granted_permissions
        FROM permissions p
        LEFT JOIN role_permissions rp ON p.id = rp.permission_id
        LEFT JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = ? OR ur.user_id IS NULL
    ");
    $directCheckStmt->execute([$adminId]);
    $directCheck = $directCheckStmt->fetch();
    
    echo "   📋 إجمالي الصلاحيات في النظام: " . $directCheck['permissions_count'] . "\n";
    echo "   ✅ الصلاحيات الممنوحة لـ admin: " . $directCheck['granted_permissions'] . "\n";
    
    // النتيجة النهائية
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "🎯 النتيجة النهائية:\n\n";
    
    if ($isAdmin && count($adminPermissions) == $allPermissions['total']) {
        echo "🎉 ممتاز! مستخدم admin مُعد بشكل صحيح:\n";
        echo "   ✅ يملك صلاحيات مدير النظام\n";
        echo "   ✅ يملك جميع الصلاحيات المتاحة (" . count($adminPermissions) . " صلاحية)\n";
        echo "   ✅ يمكنه الوصول لجميع صفحات النظام\n";
        echo "   ✅ يمكنه تنفيذ جميع العمليات\n\n";
        echo "🚀 النظام جاهز للاستخدام مع صلاحيات admin كاملة!\n";
    } else {
        echo "⚠️ هناك مشكلة في إعداد admin:\n";
        if (!$isAdmin) echo "   ❌ لا يملك صلاحيات مدير النظام\n";
        if (count($adminPermissions) != $allPermissions['total']) {
            echo "   ❌ لا يملك جميع الصلاحيات المطلوبة\n";
        }
        echo "\n🔧 يرجى تشغيل سكريپت grant_admin_all_permissions.php مرة أخرى\n";
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
}
?>
