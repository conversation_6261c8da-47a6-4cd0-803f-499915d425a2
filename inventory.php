<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';
require 'check_permissions.php';
require 'apply_permissions.php';
// التحقق من صلاحيات الوصول للصفحة
applyPagePermissions($pdo, $_SESSION['user_id'], 'inventory.php');


// تحديث جدول حركات المخزون لإضافة الأعمدة المطلوبة
try {
    // التحقق من وجود جدول حركات المخزون
    $tableExists = $pdo->query("SHOW TABLES LIKE 'stock_movements'")->rowCount() > 0;
    
    if (!$tableExists) {
        $pdo->exec("CREATE TABLE stock_movements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT,
            type VARCHAR(50),
            reference_id INT,
            quantity INT,
            notes TEXT,
            user_id INT,
            movement_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id),
            FOREIGN KEY (user_id) REFERENCES users(id)
        )");
    } else {
        // التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
        $columns = $pdo->query("SHOW COLUMNS FROM stock_movements")->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('notes', $columns)) {
            $pdo->exec("ALTER TABLE stock_movements ADD COLUMN notes TEXT");
        }
        if (!in_array('user_id', $columns)) {
            $pdo->exec("ALTER TABLE stock_movements ADD COLUMN user_id INT");
        }
    }
    
} catch (PDOException $e) {
    // في حالة فشل التحديث، إنشاء الجدول من جديد
    $pdo->exec("CREATE TABLE IF NOT EXISTS stock_movements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT,
        type VARCHAR(50),
        reference_id INT,
        quantity INT,
        notes TEXT,
        user_id INT,
        movement_date DATETIME DEFAULT CURRENT_TIMESTAMP
    )");
}

// معالجة تعديل المخزون
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            if ($_POST['action'] === 'adjust_stock') {
                $productId = intval($_POST['product_id']);
                $adjustmentType = $_POST['adjustment_type']; // 'add', 'subtract', 'set'
                $quantity = intval($_POST['quantity']);
                $notes = trim($_POST['notes']);
                
                $pdo->beginTransaction();
                
                // الحصول على الكمية الحالية
                $currentStockStmt = $pdo->prepare("SELECT quantity, name FROM products WHERE id = ?");
                $currentStockStmt->execute([$productId]);
                $product = $currentStockStmt->fetch();
                
                if (!$product) {
                    throw new Exception('المنتج غير موجود');
                }
                
                $currentStock = $product['quantity'];
                $newStock = $currentStock;
                $movementQuantity = 0;
                
                switch ($adjustmentType) {
                    case 'add':
                        $newStock = $currentStock + $quantity;
                        $movementQuantity = $quantity;
                        break;
                    case 'subtract':
                        $newStock = max(0, $currentStock - $quantity);
                        $movementQuantity = -$quantity;
                        break;
                    case 'set':
                        $newStock = $quantity;
                        $movementQuantity = $quantity - $currentStock;
                        break;
                }
                
                // تحديث المخزون
                $updateStmt = $pdo->prepare("UPDATE products SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $updateStmt->execute([$newStock, $productId]);
                
                // إضافة حركة المخزون
                $movementStmt = $pdo->prepare("INSERT INTO stock_movements (product_id, type, quantity, notes, user_id) VALUES (?, 'manual_adjustment', ?, ?, ?)");
                $movementStmt->execute([$productId, $movementQuantity, $notes, $_SESSION['user_id']]);
                
                $pdo->commit();
                $message = 'تم تعديل المخزون بنجاح';
                $messageType = 'success';
                
            } elseif ($_POST['action'] === 'bulk_adjust') {
                $adjustments = $_POST['adjustments'];
                $notes = trim($_POST['bulk_notes']);
                
                $pdo->beginTransaction();
                
                foreach ($adjustments as $adjustment) {
                    if (empty($adjustment['product_id']) || empty($adjustment['quantity'])) {
                        continue;
                    }
                    
                    $productId = intval($adjustment['product_id']);
                    $quantity = intval($adjustment['quantity']);
                    $type = $adjustment['type'] ?? 'add';
                    
                    // الحصول على الكمية الحالية
                    $currentStockStmt = $pdo->prepare("SELECT quantity FROM products WHERE id = ?");
                    $currentStockStmt->execute([$productId]);
                    $currentStock = $currentStockStmt->fetchColumn();
                    
                    if ($currentStock === false) continue;
                    
                    $newStock = $currentStock;
                    $movementQuantity = 0;
                    
                    switch ($type) {
                        case 'add':
                            $newStock = $currentStock + $quantity;
                            $movementQuantity = $quantity;
                            break;
                        case 'subtract':
                            $newStock = max(0, $currentStock - $quantity);
                            $movementQuantity = -$quantity;
                            break;
                        case 'set':
                            $newStock = $quantity;
                            $movementQuantity = $quantity - $currentStock;
                            break;
                    }
                    
                    // تحديث المخزون
                    $updateStmt = $pdo->prepare("UPDATE products SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                    $updateStmt->execute([$newStock, $productId]);
                    
                    // إضافة حركة المخزون
                    $movementStmt = $pdo->prepare("INSERT INTO stock_movements (product_id, type, quantity, notes, user_id) VALUES (?, 'bulk_adjustment', ?, ?, ?)");
                    $movementStmt->execute([$productId, $movementQuantity, $notes, $_SESSION['user_id']]);
                }
                
                $pdo->commit();
                $message = 'تم تعديل المخزون بالجملة بنجاح';
                $messageType = 'success';
            }
        }
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $message = 'حدث خطأ: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// جلب المنتجات مع معلومات المخزون
try {
    $inventoryQuery = "
        SELECT p.*,
               c.name as category_name,
               u.name as unit_name,
               s.name as supplier_name,
               CASE
                   WHEN p.quantity <= 0 THEN 'out_of_stock'
                   WHEN p.quantity <= p.min_quantity THEN 'low_stock'
                   ELSE 'normal'
               END as stock_status,
               (p.quantity * p.cost_price) as stock_value,
               (p.quantity * p.selling_price) as retail_value
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN units u ON p.unit_id = u.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        WHERE p.status = 1
        ORDER BY 
            CASE 
                WHEN p.quantity <= 0 THEN 1
                WHEN p.quantity <= p.min_quantity THEN 2
                ELSE 3
            END,
            p.name
    ";
    $inventory = $pdo->query($inventoryQuery)->fetchAll();
} catch (PDOException $e) {
    $inventory = [];
}

// إحصائيات المخزون
$totalProducts = count($inventory);
$outOfStock = count(array_filter($inventory, function($item) { return $item['stock_status'] === 'out_of_stock'; }));
$lowStock = count(array_filter($inventory, function($item) { return $item['stock_status'] === 'low_stock'; }));
$normalStock = $totalProducts - $outOfStock - $lowStock;

// قيمة المخزون
$totalStockValue = array_sum(array_column($inventory, 'stock_value'));
$totalRetailValue = array_sum(array_column($inventory, 'retail_value'));

// جلب المنتجات للقوائم المنسدلة
try {
    $products = $pdo->query("SELECT id, name, code, quantity FROM products WHERE status = 1 ORDER BY name")->fetchAll();
} catch (PDOException $e) {
    $products = [];
}

// جلب آخر حركات المخزون
try {
    $recentMovementsQuery = "
        SELECT sm.*, 
               p.name as product_name,
               p.code as product_code,
               u.name as user_name
        FROM stock_movements sm
        LEFT JOIN products p ON sm.product_id = p.id
        LEFT JOIN users u ON sm.user_id = u.id
        ORDER BY sm.movement_date DESC
        LIMIT 20
    ";
    $recentMovements = $pdo->query($recentMovementsQuery)->fetchAll();
} catch (PDOException $e) {
    $recentMovements = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخزون - نظام نقاط البيع</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <?php
    require_once 'sidebar.php';
    renderCommonCSS();
    ?>
    <style>
        .stock-status-out {
            background-color: #dc3545;
            color: white;
        }
        .stock-status-low {
            background-color: #ffc107;
            color: #000;
        }
        .stock-status-normal {
            background-color: #28a745;
            color: white;
        }
        .movement-positive {
            color: #28a745;
            font-weight: bold;
        }
        .movement-negative {
            color: #dc3545;
            font-weight: bold;
        }
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <?php
    renderSidebar($pdo, $_SESSION['user_id'], 'inventory.php');
    ?>

    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-boxes me-2"></i>إدارة المخزون</h2>
                    <small class="text-muted">مراقبة وتعديل مستويات المخزون وحركات المنتجات</small>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-gradient" data-bs-toggle="modal" data-bs-target="#adjustStockModal">
                            <i class="bi bi-plus-minus me-2"></i>
                            تعديل مخزون
                        </button>
                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#bulkAdjustModal">
                            <i class="bi bi-list-check me-2"></i>
                            تعديل بالجملة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $totalProducts; ?></h4>
                            <p class="mb-0 small">إجمالي المنتجات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-box"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--danger-color), #c0392b);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $outOfStock; ?></h4>
                            <p class="mb-0 small">نفد المخزون</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $lowStock; ?></h4>
                            <p class="mb-0 small">مخزون منخفض</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-exclamation-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalStockValue, false); ?></h4>
                            <p class="mb-0 small">قيمة المخزون</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Table -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0"><i class="bi bi-table me-2"></i>حالة المخزون</h5>
                <div class="d-flex gap-2">
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="stockFilter" id="all" value="all" checked>
                        <label class="btn btn-outline-primary btn-sm" for="all">الكل</label>

                        <input type="radio" class="btn-check" name="stockFilter" id="outOfStock" value="out_of_stock">
                        <label class="btn btn-outline-danger btn-sm" for="outOfStock">نفد المخزون</label>

                        <input type="radio" class="btn-check" name="stockFilter" id="lowStock" value="low_stock">
                        <label class="btn btn-outline-warning btn-sm" for="lowStock">مخزون منخفض</label>

                        <input type="radio" class="btn-check" name="stockFilter" id="normalStock" value="normal">
                        <label class="btn btn-outline-success btn-sm" for="normalStock">مخزون طبيعي</label>
                    </div>
                    <button class="btn btn-outline-primary btn-sm" onclick="exportInventory()">
                        <i class="bi bi-download me-1"></i>تصدير
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshTable()">
                        <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                    </button>
                </div>
            </div>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="inventoryTable">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="8%">الكود</th>
                                <th width="20%">اسم المنتج</th>
                                <th width="12%">الفئة</th>
                                <th width="8%">الوحدة</th>
                                <th width="10%">الكمية الحالية</th>
                                <th width="8%">الحد الأدنى</th>
                                <th width="10%">حالة المخزون</th>
                                <th width="10%">قيمة المخزون</th>
                                <th width="12%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($inventory as $index => $item): ?>
                            <tr data-stock-status="<?php echo $item['stock_status']; ?>">
                                <td><?php echo $index + 1; ?></td>
                                <td>
                                    <?php if (!empty($item['code'])): ?>
                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($item['code']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($item['name']); ?></strong>
                                        <?php if (!empty($item['supplier_name'])): ?>
                                            <br><small class="text-muted">المورد: <?php echo htmlspecialchars($item['supplier_name']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo htmlspecialchars($item['category_name'] ?? 'غير محدد'); ?></span>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($item['unit_name'] ?? '-'); ?>
                                </td>
                                <td>
                                    <span class="fs-5 fw-bold <?php echo $item['stock_status'] === 'out_of_stock' ? 'text-danger' : ($item['stock_status'] === 'low_stock' ? 'text-warning' : 'text-success'); ?>">
                                        <?php echo $item['quantity']; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="text-muted"><?php echo $item['min_quantity']; ?></span>
                                </td>
                                <td>
                                    <?php
                                    $statusClass = 'bg-success';
                                    $statusText = 'طبيعي';

                                    switch ($item['stock_status']) {
                                        case 'out_of_stock':
                                            $statusClass = 'bg-danger';
                                            $statusText = 'نفد المخزون';
                                            break;
                                        case 'low_stock':
                                            $statusClass = 'bg-warning text-dark';
                                            $statusText = 'مخزون منخفض';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo formatCurrency($item['stock_value']); ?></strong>
                                        <br><small class="text-muted">تجزئة: <?php echo formatCurrency($item['retail_value']); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="quickAdjust(<?php echo $item['id']; ?>, '<?php echo htmlspecialchars($item['name']); ?>', <?php echo $item['quantity']; ?>)"
                                                title="تعديل سريع">
                                            <i class="bi bi-plus-minus"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info"
                                                onclick="viewMovements(<?php echo $item['id']; ?>, '<?php echo htmlspecialchars($item['name']); ?>')"
                                                title="حركات المخزون">
                                            <i class="bi bi-clock-history"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success"
                                                onclick="editProduct(<?php echo $item['id']; ?>)"
                                                title="تعديل المنتج">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Recent Stock Movements -->
        <div class="content-card">
            <h5 class="mb-3"><i class="bi bi-clock-history me-2"></i>آخر حركات المخزون</h5>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>نوع الحركة</th>
                            <th>الكمية</th>
                            <th>المستخدم</th>
                            <th>التاريخ</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentMovements as $movement): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($movement['product_name'] ?? 'منتج محذوف'); ?></strong>
                                <?php if (!empty($movement['product_code'])): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($movement['product_code']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $typeText = $movement['type'];
                                $typeClass = 'bg-secondary';

                                switch ($movement['type']) {
                                    case 'sale':
                                        $typeText = 'بيع';
                                        $typeClass = 'bg-primary';
                                        break;
                                    case 'purchase':
                                        $typeText = 'شراء';
                                        $typeClass = 'bg-success';
                                        break;
                                    case 'manual_adjustment':
                                        $typeText = 'تعديل يدوي';
                                        $typeClass = 'bg-warning text-dark';
                                        break;
                                    case 'bulk_adjustment':
                                        $typeText = 'تعديل بالجملة';
                                        $typeClass = 'bg-info';
                                        break;
                                    case 'purchase_cancel':
                                        $typeText = 'إلغاء شراء';
                                        $typeClass = 'bg-danger';
                                        break;
                                }
                                ?>
                                <span class="badge <?php echo $typeClass; ?>"><?php echo $typeText; ?></span>
                            </td>
                            <td>
                                <span class="<?php echo $movement['quantity'] > 0 ? 'movement-positive' : 'movement-negative'; ?>">
                                    <?php echo $movement['quantity'] > 0 ? '+' : ''; ?><?php echo $movement['quantity']; ?>
                                </span>
                            </td>
                            <td>
                                <?php echo htmlspecialchars($movement['user_name'] ?? 'غير محدد'); ?>
                            </td>
                            <td>
                                <small><?php echo formatDateTime($movement['movement_date']); ?></small>
                            </td>
                            <td>
                                <?php if (!empty($movement['notes'])): ?>
                                    <small><?php echo htmlspecialchars($movement['notes']); ?></small>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php if (empty($recentMovements)): ?>
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                لا توجد حركات مخزون حديثة
            </div>
            <?php endif; ?>
        </div>

        <!-- Adjust Stock Modal -->
        <div class="modal fade" id="adjustStockModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-plus-minus me-2"></i>تعديل المخزون</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST" id="adjustStockForm">
                        <div class="modal-body">
                            <input type="hidden" name="action" value="adjust_stock">

                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-box me-1"></i>المنتج *</label>
                                <select class="form-select" name="product_id" id="adjust_product_id" required>
                                    <option value="">اختر المنتج</option>
                                    <?php foreach ($products as $product): ?>
                                    <option value="<?php echo $product['id']; ?>" data-current-stock="<?php echo $product['quantity']; ?>">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                        <?php if (!empty($product['code'])): ?>
                                            (<?php echo htmlspecialchars($product['code']); ?>)
                                        <?php endif; ?>
                                        - المخزون الحالي: <?php echo $product['quantity']; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-arrow-up-down me-1"></i>نوع التعديل *</label>
                                    <select class="form-select" name="adjustment_type" id="adjustment_type" required>
                                        <option value="add">إضافة إلى المخزون</option>
                                        <option value="subtract">خصم من المخزون</option>
                                        <option value="set">تحديد الكمية</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-123 me-1"></i>الكمية *</label>
                                    <input type="number" class="form-control" name="quantity" id="adjust_quantity"
                                           required min="0" step="1" placeholder="أدخل الكمية">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-text-paragraph me-1"></i>ملاحظات</label>
                                <textarea class="form-control" name="notes" rows="3"
                                          placeholder="سبب التعديل أو ملاحظات إضافية"></textarea>
                            </div>

                            <div class="alert alert-info" id="current_stock_info" style="display: none;">
                                <i class="bi bi-info-circle me-2"></i>
                                <span id="stock_info_text"></span>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>إلغاء
                            </button>
                            <button type="submit" class="btn btn-gradient">
                                <i class="bi bi-check-circle me-1"></i>تطبيق التعديل
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Bulk Adjust Modal -->
        <div class="modal fade" id="bulkAdjustModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-list-check me-2"></i>تعديل المخزون بالجملة</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST" id="bulkAdjustForm">
                        <div class="modal-body">
                            <input type="hidden" name="action" value="bulk_adjust">

                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-text-paragraph me-1"></i>ملاحظات عامة</label>
                                <textarea class="form-control" name="bulk_notes" rows="2"
                                          placeholder="ملاحظات عامة لجميع التعديلات"></textarea>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">المنتجات للتعديل</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addBulkRow()">
                                    <i class="bi bi-plus me-1"></i>إضافة منتج
                                </button>
                            </div>

                            <div id="bulk_adjustments_container">
                                <div class="row mb-2 bulk-adjustment-row">
                                    <div class="col-md-5">
                                        <select class="form-select" name="adjustments[0][product_id]">
                                            <option value="">اختر المنتج</option>
                                            <?php foreach ($products as $product): ?>
                                            <option value="<?php echo $product['id']; ?>">
                                                <?php echo htmlspecialchars($product['name']); ?>
                                                <?php if (!empty($product['code'])): ?>
                                                    (<?php echo htmlspecialchars($product['code']); ?>)
                                                <?php endif; ?>
                                                - الحالي: <?php echo $product['quantity']; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" name="adjustments[0][type]">
                                            <option value="add">إضافة</option>
                                            <option value="subtract">خصم</option>
                                            <option value="set">تحديد</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" name="adjustments[0][quantity]"
                                               placeholder="الكمية" min="0" step="1">
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeBulkRow(this)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>إلغاء
                            </button>
                            <button type="submit" class="btn btn-gradient">
                                <i class="bi bi-check-circle me-1"></i>تطبيق التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Buttons -->
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let inventoryTable;
        let bulkRowCounter = 1;

        $(document).ready(function() {
            // Initialize DataTable
            inventoryTable = $('#inventoryTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[5, 'asc']], // Sort by quantity
                columnDefs: [
                    { orderable: false, targets: [9] }, // Actions column
                    { searchable: false, targets: [0, 9] } // ID and Actions columns
                ],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="bi bi-file-earmark-excel me-1"></i>Excel',
                        className: 'btn btn-success btn-sm',
                        exportOptions: {
                            columns: [1, 2, 3, 4, 5, 6, 7, 8] // Exclude ID and Actions
                        }
                    }
                ]
            });

            // Stock filter functionality
            $('input[name="stockFilter"]').on('change', function() {
                const filterValue = $(this).val();
                if (filterValue === 'all') {
                    inventoryTable.search('').draw();
                } else {
                    inventoryTable.column(7).search(getStatusText(filterValue)).draw();
                }
            });

            // Product selection change in adjust modal
            $('#adjust_product_id').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                const currentStock = selectedOption.data('current-stock');

                if (currentStock !== undefined) {
                    $('#current_stock_info').show();
                    $('#stock_info_text').text(`المخزون الحالي: ${currentStock}`);
                } else {
                    $('#current_stock_info').hide();
                }
            });

            // Adjustment type change
            $('#adjustment_type').on('change', function() {
                const type = $(this).val();
                const quantityInput = $('#adjust_quantity');

                switch (type) {
                    case 'add':
                        quantityInput.attr('placeholder', 'الكمية المراد إضافتها');
                        break;
                    case 'subtract':
                        quantityInput.attr('placeholder', 'الكمية المراد خصمها');
                        break;
                    case 'set':
                        quantityInput.attr('placeholder', 'الكمية الجديدة');
                        break;
                }
            });

            // Form validation
            $('#adjustStockForm').on('submit', function(e) {
                const productId = $('#adjust_product_id').val();
                const quantity = parseInt($('#adjust_quantity').val());

                if (!productId) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'يرجى اختيار المنتج'
                    });
                    return false;
                }

                if (isNaN(quantity) || quantity < 0) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'يرجى إدخال كمية صحيحة'
                    });
                    return false;
                }
            });

            // Clear forms when modals are hidden
            $('#adjustStockModal').on('hidden.bs.modal', function() {
                $('#adjustStockForm')[0].reset();
                $('#current_stock_info').hide();
            });

            $('#bulkAdjustModal').on('hidden.bs.modal', function() {
                $('#bulkAdjustForm')[0].reset();
                resetBulkForm();
            });
        });

        function getStatusText(status) {
            switch (status) {
                case 'out_of_stock': return 'نفد المخزون';
                case 'low_stock': return 'مخزون منخفض';
                case 'normal': return 'طبيعي';
                default: return '';
            }
        }

        function quickAdjust(productId, productName, currentStock) {
            Swal.fire({
                title: `تعديل سريع - ${productName}`,
                html: `
                    <div class="text-start">
                        <p><strong>المخزون الحالي:</strong> ${currentStock}</p>
                        <div class="mb-3">
                            <label class="form-label">نوع التعديل:</label>
                            <select class="form-select" id="quick_type">
                                <option value="add">إضافة إلى المخزون</option>
                                <option value="subtract">خصم من المخزون</option>
                                <option value="set">تحديد الكمية</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية:</label>
                            <input type="number" class="form-control" id="quick_quantity" min="0" step="1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات:</label>
                            <textarea class="form-control" id="quick_notes" rows="2"></textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'تطبيق',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const type = document.getElementById('quick_type').value;
                    const quantity = parseInt(document.getElementById('quick_quantity').value);
                    const notes = document.getElementById('quick_notes').value;

                    if (isNaN(quantity) || quantity < 0) {
                        Swal.showValidationMessage('يرجى إدخال كمية صحيحة');
                        return false;
                    }

                    return { type, quantity, notes };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const { type, quantity, notes } = result.value;

                    // Create and submit form
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="action" value="adjust_stock">
                        <input type="hidden" name="product_id" value="${productId}">
                        <input type="hidden" name="adjustment_type" value="${type}">
                        <input type="hidden" name="quantity" value="${quantity}">
                        <input type="hidden" name="notes" value="${notes}">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

        function viewMovements(productId, productName) {
            // This would typically load movement history via AJAX
            Swal.fire({
                title: `حركات المخزون - ${productName}`,
                html: `
                    <div class="text-center">
                        <i class="bi bi-hourglass-split fs-1 text-muted"></i>
                        <p class="mt-3">جاري تحميل حركات المخزون...</p>
                        <p class="text-muted">سيتم إضافة هذه الميزة قريباً</p>
                    </div>
                `,
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: 'إغلاق'
            });
        }

        function editProduct(productId) {
            window.location.href = `products.php?edit=${productId}`;
        }

        function addBulkRow() {
            const container = document.getElementById('bulk_adjustments_container');
            const newRow = document.createElement('div');
            newRow.className = 'row mb-2 bulk-adjustment-row';
            newRow.innerHTML = `
                <div class="col-md-5">
                    <select class="form-select" name="adjustments[${bulkRowCounter}][product_id]">
                        <option value="">اختر المنتج</option>
                        <?php foreach ($products as $product): ?>
                        <option value="<?php echo $product['id']; ?>">
                            <?php echo htmlspecialchars($product['name']); ?>
                            <?php if (!empty($product['code'])): ?>
                                (<?php echo htmlspecialchars($product['code']); ?>)
                            <?php endif; ?>
                            - الحالي: <?php echo $product['quantity']; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="adjustments[${bulkRowCounter}][type]">
                        <option value="add">إضافة</option>
                        <option value="subtract">خصم</option>
                        <option value="set">تحديد</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <input type="number" class="form-control" name="adjustments[${bulkRowCounter}][quantity]"
                           placeholder="الكمية" min="0" step="1">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeBulkRow(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(newRow);
            bulkRowCounter++;
        }

        function removeBulkRow(button) {
            const row = button.closest('.bulk-adjustment-row');
            if (document.querySelectorAll('.bulk-adjustment-row').length > 1) {
                row.remove();
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'تنبيه',
                    text: 'يجب أن يكون هناك صف واحد على الأقل'
                });
            }
        }

        function resetBulkForm() {
            const container = document.getElementById('bulk_adjustments_container');
            container.innerHTML = `
                <div class="row mb-2 bulk-adjustment-row">
                    <div class="col-md-5">
                        <select class="form-select" name="adjustments[0][product_id]">
                            <option value="">اختر المنتج</option>
                            <?php foreach ($products as $product): ?>
                            <option value="<?php echo $product['id']; ?>">
                                <?php echo htmlspecialchars($product['name']); ?>
                                <?php if (!empty($product['code'])): ?>
                                    (<?php echo htmlspecialchars($product['code']); ?>)
                                <?php endif; ?>
                                - الحالي: <?php echo $product['quantity']; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="adjustments[0][type]">
                            <option value="add">إضافة</option>
                            <option value="subtract">خصم</option>
                            <option value="set">تحديد</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="number" class="form-control" name="adjustments[0][quantity]"
                               placeholder="الكمية" min="0" step="1">
                    </div>
                    <div class="col-md-1">
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeBulkRow(this)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            bulkRowCounter = 1;
        }

        function refreshTable() {
            location.reload();
        }

        function exportInventory() {
            inventoryTable.button('.buttons-excel').trigger();
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Add loading animation to buttons
        $('form').on('submit', function() {
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            submitBtn.html('<i class="bi bi-hourglass-split me-1"></i>جاري المعالجة...').prop('disabled', true);

            setTimeout(function() {
                submitBtn.html(originalText).prop('disabled', false);
            }, 3000);
        });
    </script>
</body>
</html>
