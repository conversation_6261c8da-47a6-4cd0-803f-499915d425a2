<?php
echo "PHP يعمل بنجاح!<br>";
echo "الوقت الحالي: " . date('Y-m-d H:i:s') . "<br>";
echo "إصدار PHP: " . phpversion() . "<br>";

// اختبار الاتصال بقاعدة البيانات
try {
    $pdo = new PDO("mysql:host=localhost;dbname=pos3;charset=utf8mb4", "root", "", [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    echo "✅ الاتصال بقاعدة البيانات يعمل بنجاح!<br>";
    
    // اختبار وجود الجداول
    $tables = $pdo->query("SHOW TABLES")->fetchAll();
    echo "عدد الجداول: " . count($tables) . "<br>";
    
} catch (PDOException $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
}
?>

<a href="setup.php">تشغيل الإعداد</a> | 
<a href="login.php">صفحة تسجيل الدخول</a> | 
<a href="dashboard.php">لوحة التحكم</a>
