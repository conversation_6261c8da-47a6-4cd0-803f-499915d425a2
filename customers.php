<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';
require 'check_permissions.php';
require 'apply_permissions.php';

// التحقق من صلاحيات الوصول لإدارة العملاء
applyPagePermissions($pdo, $_SESSION['user_id'], 'customers.php');

// تحديث جدول العملاء لإضافة الأعمدة المطلوبة
try {
    // التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
    $columns = $pdo->query("SHOW COLUMNS FROM customers")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('email', $columns)) {
        $pdo->exec("ALTER TABLE customers ADD COLUMN email VARCHAR(255)");
    }
    if (!in_array('date_of_birth', $columns)) {
        $pdo->exec("ALTER TABLE customers ADD COLUMN date_of_birth DATE");
    }
    if (!in_array('gender', $columns)) {
        $pdo->exec("ALTER TABLE customers ADD COLUMN gender ENUM('male', 'female') DEFAULT 'male'");
    }
    if (!in_array('customer_type', $columns)) {
        $pdo->exec("ALTER TABLE customers ADD COLUMN customer_type ENUM('individual', 'company') DEFAULT 'individual'");
    }
    if (!in_array('tax_number', $columns)) {
        $pdo->exec("ALTER TABLE customers ADD COLUMN tax_number VARCHAR(50)");
    }
    if (!in_array('credit_limit', $columns)) {
        $pdo->exec("ALTER TABLE customers ADD COLUMN credit_limit DECIMAL(10,2) DEFAULT 0");
    }
    if (!in_array('discount_rate', $columns)) {
        $pdo->exec("ALTER TABLE customers ADD COLUMN discount_rate DECIMAL(5,2) DEFAULT 0");
    }
    if (!in_array('notes', $columns)) {
        $pdo->exec("ALTER TABLE customers ADD COLUMN notes TEXT");
    }
    if (!in_array('status', $columns)) {
        $pdo->exec("ALTER TABLE customers ADD COLUMN status TINYINT(1) DEFAULT 1");
    }
    if (!in_array('created_at', $columns)) {
        $pdo->exec("ALTER TABLE customers ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
    }
    if (!in_array('updated_at', $columns)) {
        $pdo->exec("ALTER TABLE customers ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
    }
    
    // تحديث الأعمدة الموجودة لتحديد القيم الافتراضية
    $pdo->exec("UPDATE customers SET status = 1 WHERE status IS NULL");
    $pdo->exec("UPDATE customers SET created_at = NOW() WHERE created_at IS NULL");
    $pdo->exec("UPDATE customers SET updated_at = NOW() WHERE updated_at IS NULL");
    
} catch (PDOException $e) {
    // في حالة فشل التحديث، إنشاء الجدول من جديد
    $pdo->exec("CREATE TABLE IF NOT EXISTS customers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(255),
        address VARCHAR(255),
        date_of_birth DATE,
        gender ENUM('male', 'female') DEFAULT 'male',
        customer_type ENUM('individual', 'company') DEFAULT 'individual',
        tax_number VARCHAR(50),
        credit_limit DECIMAL(10,2) DEFAULT 0,
        discount_rate DECIMAL(5,2) DEFAULT 0,
        notes TEXT,
        status TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
}

// معالجة إضافة/تعديل/حذف العميل
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            // التحقق من الصلاحيات
            $hasCreatePermission = hasPermission($pdo, $_SESSION['user_id'], 'customers.create') || isAdmin($pdo, $_SESSION['user_id']);
            $hasEditPermission = hasPermission($pdo, $_SESSION['user_id'], 'customers.edit') || isAdmin($pdo, $_SESSION['user_id']);
            $hasDeletePermission = hasPermission($pdo, $_SESSION['user_id'], 'customers.delete') || isAdmin($pdo, $_SESSION['user_id']);

            if ($_POST['action'] === 'add' && !$hasCreatePermission) {
                $message = 'ليس لديك صلاحية لإضافة العملاء';
                $messageType = 'danger';
            } elseif ($_POST['action'] === 'edit' && !$hasEditPermission) {
                $message = 'ليس لديك صلاحية لتعديل العملاء';
                $messageType = 'danger';
            } elseif ($_POST['action'] === 'delete' && !$hasDeletePermission) {
                $message = 'ليس لديك صلاحية لحذف العملاء';
                $messageType = 'danger';
            } elseif ($_POST['action'] === 'add') {
                // تسجيل عملية الإضافة
                logUserAction($pdo, $_SESSION['user_id'], 'customer_add', 'Customer: ' . $_POST['name']);
                // التحقق من عدم وجود عميل بنفس الاسم
                $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM customers WHERE name = ?");
                $checkStmt->execute([$_POST['name']]);
                if ($checkStmt->fetchColumn() > 0) {
                    $message = 'يوجد عميل بهذا الاسم مسبقاً';
                    $messageType = 'danger';
                } else {
                    $stmt = $pdo->prepare("INSERT INTO customers (name, phone, email, address, date_of_birth, gender, customer_type, tax_number, credit_limit, discount_rate, notes, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        trim($_POST['name']),
                        trim($_POST['phone']),
                        trim($_POST['email']),
                        trim($_POST['address']),
                        !empty($_POST['date_of_birth']) ? $_POST['date_of_birth'] : null,
                        $_POST['gender'],
                        $_POST['customer_type'],
                        trim($_POST['tax_number']),
                        floatval($_POST['credit_limit']),
                        floatval($_POST['discount_rate']),
                        trim($_POST['notes']),
                        isset($_POST['status']) ? 1 : 0
                    ]);
                    $message = 'تمت إضافة العميل بنجاح';
                    $messageType = 'success';
                }
            } elseif ($_POST['action'] === 'edit') {
                // تسجيل عملية التعديل
                logUserAction($pdo, $_SESSION['user_id'], 'customer_edit', 'Customer ID: ' . $_POST['id']);
                // التحقق من عدم وجود عميل بنفس الاسم (عدا العميل الحالي)
                $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM customers WHERE name = ? AND id != ?");
                $checkStmt->execute([$_POST['name'], $_POST['id']]);
                if ($checkStmt->fetchColumn() > 0) {
                    $message = 'يوجد عميل بهذا الاسم مسبقاً';
                    $messageType = 'danger';
                } else {
                    $stmt = $pdo->prepare("UPDATE customers SET name = ?, phone = ?, email = ?, address = ?, date_of_birth = ?, gender = ?, customer_type = ?, tax_number = ?, credit_limit = ?, discount_rate = ?, notes = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                    $stmt->execute([
                        trim($_POST['name']),
                        trim($_POST['phone']),
                        trim($_POST['email']),
                        trim($_POST['address']),
                        !empty($_POST['date_of_birth']) ? $_POST['date_of_birth'] : null,
                        $_POST['gender'],
                        $_POST['customer_type'],
                        trim($_POST['tax_number']),
                        floatval($_POST['credit_limit']),
                        floatval($_POST['discount_rate']),
                        trim($_POST['notes']),
                        isset($_POST['status']) ? 1 : 0,
                        $_POST['id']
                    ]);
                    $message = 'تم تحديث العميل بنجاح';
                    $messageType = 'success';
                }
            } elseif ($_POST['action'] === 'delete' && isset($_POST['id'])) {
                // تسجيل عملية الحذف
                logUserAction($pdo, $_SESSION['user_id'], 'customer_delete', 'Customer ID: ' . $_POST['id']);
                // التحقق من عدم وجود مبيعات مرتبطة بهذا العميل
                try {
                    $checkSales = $pdo->prepare("SELECT COUNT(*) FROM sales WHERE customer_id = ?");
                    $checkSales->execute([$_POST['id']]);
                    $salesCount = $checkSales->fetchColumn();
                    
                    if ($salesCount > 0) {
                        $message = 'لا يمكن حذف هذا العميل لأنه مرتبط بـ ' . $salesCount . ' عملية بيع';
                        $messageType = 'warning';
                    } else {
                        $stmt = $pdo->prepare("DELETE FROM customers WHERE id = ?");
                        $stmt->execute([$_POST['id']]);
                        $message = 'تم حذف العميل بنجاح';
                        $messageType = 'success';
                    }
                } catch (PDOException $e) {
                    // إذا لم يكن جدول المبيعات موجود، السماح بالحذف
                    $stmt = $pdo->prepare("DELETE FROM customers WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $message = 'تم حذف العميل بنجاح';
                    $messageType = 'success';
                }
            }
        }
    } catch (PDOException $e) {
        $message = 'حدث خطأ: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// جلب العملاء مع إحصائيات
try {
    $customersQuery = "
        SELECT c.*, 
               COUNT(DISTINCT s.id) as sales_count,
               COALESCE(SUM(s.total), 0) as total_sales_value,
               MAX(s.sale_date) as last_sale_date
        FROM customers c 
        LEFT JOIN sales s ON c.id = s.customer_id
        GROUP BY c.id 
        ORDER BY c.name
    ";
    $customers = $pdo->query($customersQuery)->fetchAll();
} catch (PDOException $e) {
    // في حالة حدوث خطأ، جلب العملاء بدون إحصائيات
    $customers = $pdo->query("SELECT *, 0 as sales_count, 0 as total_sales_value, NULL as last_sale_date FROM customers ORDER BY name")->fetchAll();
}

// إحصائيات عامة
$totalCustomers = count($customers);
$activeCustomers = count(array_filter($customers, function($customer) { return $customer['status'] == 1; }));
$inactiveCustomers = $totalCustomers - $activeCustomers;
$individualCustomers = count(array_filter($customers, function($customer) { return $customer['customer_type'] === 'individual'; }));
$companyCustomers = count(array_filter($customers, function($customer) { return $customer['customer_type'] === 'company'; }));

// حساب إجمالي قيمة المبيعات وحدود الائتمان
$totalSalesValue = 0;
$totalCreditLimit = 0;
$totalDiscountRate = 0;
foreach ($customers as $customer) {
    $totalSalesValue += $customer['total_sales_value'];
    $totalCreditLimit += $customer['credit_limit'];
    $totalDiscountRate += $customer['discount_rate'];
}
$averageDiscountRate = $totalCustomers > 0 ? $totalDiscountRate / $totalCustomers : 0;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام نقاط البيع</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-left-color: #fff;
            transform: translateX(-5px);
        }
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .page-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .table-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }
        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 20px 20px 0 0;
        }
        .customer-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .customer-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .contact-info {
            font-size: 0.9em;
            color: #6c757d;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            $companyName = getSetting('company_name', 'نظام نقاط البيع');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>
        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link active" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-people me-2"></i>إدارة العملاء</h2>
                    <small class="text-muted">إضافة وتعديل وإدارة معلومات العملاء وقاعدة البيانات التجارية</small>
                </div>
                <div class="col-auto">
                    <?php if (hasPermission($pdo, $_SESSION['user_id'], 'customers.create') || isAdmin($pdo, $_SESSION['user_id'])): ?>
                    <button type="button" class="btn btn-gradient" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة عميل جديد
                    </button>
                    <?php else: ?>
                    <button type="button" class="btn btn-secondary" disabled title="ليس لديك صلاحية لإضافة العملاء">
                        <i class="bi bi-lock me-2"></i>
                        غير مصرح
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $totalCustomers; ?></h4>
                            <p class="mb-0 small">إجمالي العملاء</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #2ecc71);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $activeCustomers; ?></h4>
                            <p class="mb-0 small">عملاء نشطين</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-person-check"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalSalesValue, false); ?></h4>
                            <p class="mb-0 small">إجمالي المبيعات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo number_format($averageDiscountRate, 1); ?>%</h4>
                            <p class="mb-0 small">متوسط الخصم</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-percent"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers Table -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0"><i class="bi bi-table me-2"></i>قائمة العملاء</h5>
                <div class="d-flex gap-2">
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="customerFilter" id="allCustomers" value="all" checked>
                        <label class="btn btn-outline-primary btn-sm" for="allCustomers">الكل</label>

                        <input type="radio" class="btn-check" name="customerFilter" id="individualCustomers" value="individual">
                        <label class="btn btn-outline-info btn-sm" for="individualCustomers">أفراد</label>

                        <input type="radio" class="btn-check" name="customerFilter" id="companyCustomers" value="company">
                        <label class="btn btn-outline-warning btn-sm" for="companyCustomers">شركات</label>

                        <input type="radio" class="btn-check" name="customerFilter" id="activeCustomers" value="active">
                        <label class="btn btn-outline-success btn-sm" for="activeCustomers">نشطين</label>
                    </div>
                    <button class="btn btn-outline-primary btn-sm" onclick="exportData()">
                        <i class="bi bi-download me-1"></i>تصدير
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshTable()">
                        <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                    </button>
                </div>
            </div>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="customersTable">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="18%">اسم العميل</th>
                                <th width="12%">الهاتف</th>
                                <th width="15%">البريد الإلكتروني</th>
                                <th width="10%">النوع</th>
                                <th width="8%">المبيعات</th>
                                <th width="10%">إجمالي المشتريات</th>
                                <th width="8%">نسبة الخصم</th>
                                <th width="8%">الحالة</th>
                                <th width="15%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($customers as $index => $customer): ?>
                            <tr data-customer-type="<?php echo $customer['customer_type']; ?>" data-status="<?php echo $customer['status'] ? 'active' : 'inactive'; ?>">
                                <td><?php echo $index + 1; ?></td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($customer['name']); ?></strong>
                                        <?php if (!empty($customer['tax_number'])): ?>
                                            <br><small class="text-muted">ض.ب: <?php echo htmlspecialchars($customer['tax_number']); ?></small>
                                        <?php endif; ?>
                                        <?php if ($customer['customer_type'] === 'individual' && !empty($customer['date_of_birth'])): ?>
                                            <br><small class="text-muted">العمر: <?php echo date_diff(date_create($customer['date_of_birth']), date_create('today'))->y; ?> سنة</small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($customer['phone'])): ?>
                                        <a href="tel:<?php echo $customer['phone']; ?>" class="text-decoration-none">
                                            <i class="bi bi-telephone me-1"></i><?php echo htmlspecialchars($customer['phone']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($customer['email'])): ?>
                                        <a href="mailto:<?php echo $customer['email']; ?>" class="text-decoration-none">
                                            <i class="bi bi-envelope me-1"></i><?php echo htmlspecialchars($customer['email']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $typeClass = $customer['customer_type'] === 'individual' ? 'bg-info' : 'bg-warning text-dark';
                                    $typeText = $customer['customer_type'] === 'individual' ? 'فرد' : 'شركة';
                                    if ($customer['customer_type'] === 'individual' && isset($customer['gender'])) {
                                        $typeText .= ' (' . ($customer['gender'] === 'male' ? 'ذكر' : 'أنثى') . ')';
                                    }
                                    ?>
                                    <span class="badge <?php echo $typeClass; ?>"><?php echo $typeText; ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo $customer['sales_count']; ?> عملية</span>
                                </td>
                                <td>
                                    <strong class="text-success"><?php echo formatCurrency($customer['total_sales_value']); ?></strong>
                                    <?php if (!empty($customer['last_sale_date'])): ?>
                                        <br><small class="text-muted">آخر شراء: <?php echo date('Y-m-d', strtotime($customer['last_sale_date'])); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($customer['discount_rate'] > 0): ?>
                                        <strong class="text-warning"><?php echo number_format($customer['discount_rate'], 1); ?>%</strong>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge <?php echo $customer['status'] ? 'bg-success' : 'bg-danger'; ?>">
                                        <?php echo $customer['status'] ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="viewCustomer(<?php echo htmlspecialchars(json_encode($customer)); ?>)"
                                                title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success"
                                                onclick="editCustomer(<?php echo htmlspecialchars(json_encode($customer)); ?>)"
                                                title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="deleteCustomer(<?php echo $customer['id']; ?>, '<?php echo htmlspecialchars($customer['name']); ?>')"
                                                title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Add Customer Modal -->
        <div class="modal fade" id="addCustomerModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-person-plus me-2"></i>إضافة عميل جديد</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST" id="addCustomerForm">
                        <div class="modal-body">
                            <input type="hidden" name="action" value="add">

                            <!-- Basic Information -->
                            <h6 class="mb-3"><i class="bi bi-info-circle me-2"></i>المعلومات الأساسية</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-person me-1"></i>اسم العميل *</label>
                                    <input type="text" class="form-control" name="name" required
                                           placeholder="أدخل اسم العميل" maxlength="100">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-building me-1"></i>نوع العميل</label>
                                    <select class="form-select" name="customer_type" id="add_customer_type">
                                        <option value="individual">فرد</option>
                                        <option value="company">شركة</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Personal Information (for individuals) -->
                            <div id="add_personal_info">
                                <h6 class="mb-3"><i class="bi bi-person-badge me-2"></i>المعلومات الشخصية</h6>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label"><i class="bi bi-calendar me-1"></i>تاريخ الميلاد</label>
                                        <input type="date" class="form-control" name="date_of_birth">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label"><i class="bi bi-gender-ambiguous me-1"></i>الجنس</label>
                                        <select class="form-select" name="gender">
                                            <option value="male">ذكر</option>
                                            <option value="female">أنثى</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <h6 class="mb-3"><i class="bi bi-telephone me-2"></i>معلومات الاتصال</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-telephone me-1"></i>رقم الهاتف</label>
                                    <input type="text" class="form-control" name="phone"
                                           placeholder="أدخل رقم الهاتف" maxlength="20">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-envelope me-1"></i>البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email"
                                           placeholder="<EMAIL>" maxlength="255">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-geo-alt me-1"></i>العنوان</label>
                                <textarea class="form-control" name="address" rows="2"
                                          placeholder="العنوان الكامل للعميل" maxlength="255"></textarea>
                            </div>

                            <!-- Business Information -->
                            <h6 class="mb-3"><i class="bi bi-briefcase me-2"></i>المعلومات التجارية</h6>
                            <div class="row mb-3">
                                <div class="col-md-6" id="add_tax_number_field">
                                    <label class="form-label"><i class="bi bi-receipt me-1"></i>الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="tax_number"
                                           placeholder="رقم السجل التجاري أو الضريبي" maxlength="50">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-credit-card me-1"></i>حد الائتمان</label>
                                    <input type="number" class="form-control" name="credit_limit"
                                           placeholder="0.00" step="0.01" min="0" value="0">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-percent me-1"></i>نسبة الخصم (%)</label>
                                    <input type="number" class="form-control" name="discount_rate"
                                           placeholder="0.00" step="0.01" min="0" max="100" value="0">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-toggles me-1"></i>حالة العميل</label>
                                    <div class="form-check form-switch mt-2">
                                        <input class="form-check-input" type="checkbox" name="status" checked>
                                        <label class="form-check-label">عميل نشط</label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-text-paragraph me-1"></i>ملاحظات</label>
                                <textarea class="form-control" name="notes" rows="3"
                                          placeholder="ملاحظات إضافية عن العميل (اختياري)"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>إلغاء
                            </button>
                            <button type="submit" class="btn btn-gradient">
                                <i class="bi bi-check-circle me-1"></i>حفظ العميل
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Edit Customer Modal -->
        <div class="modal fade" id="editCustomerModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-pencil me-2"></i>تعديل العميل</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST" id="editCustomerForm">
                        <div class="modal-body">
                            <input type="hidden" name="action" value="edit">
                            <input type="hidden" name="id" id="edit_id">

                            <!-- Basic Information -->
                            <h6 class="mb-3"><i class="bi bi-info-circle me-2"></i>المعلومات الأساسية</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-person me-1"></i>اسم العميل *</label>
                                    <input type="text" class="form-control" name="name" id="edit_name" required
                                           placeholder="أدخل اسم العميل" maxlength="100">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-building me-1"></i>نوع العميل</label>
                                    <select class="form-select" name="customer_type" id="edit_customer_type">
                                        <option value="individual">فرد</option>
                                        <option value="company">شركة</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Personal Information (for individuals) -->
                            <div id="edit_personal_info">
                                <h6 class="mb-3"><i class="bi bi-person-badge me-2"></i>المعلومات الشخصية</h6>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label"><i class="bi bi-calendar me-1"></i>تاريخ الميلاد</label>
                                        <input type="date" class="form-control" name="date_of_birth" id="edit_date_of_birth">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label"><i class="bi bi-gender-ambiguous me-1"></i>الجنس</label>
                                        <select class="form-select" name="gender" id="edit_gender">
                                            <option value="male">ذكر</option>
                                            <option value="female">أنثى</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <h6 class="mb-3"><i class="bi bi-telephone me-2"></i>معلومات الاتصال</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-telephone me-1"></i>رقم الهاتف</label>
                                    <input type="text" class="form-control" name="phone" id="edit_phone"
                                           placeholder="أدخل رقم الهاتف" maxlength="20">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-envelope me-1"></i>البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email" id="edit_email"
                                           placeholder="<EMAIL>" maxlength="255">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-geo-alt me-1"></i>العنوان</label>
                                <textarea class="form-control" name="address" id="edit_address" rows="2"
                                          placeholder="العنوان الكامل للعميل" maxlength="255"></textarea>
                            </div>

                            <!-- Business Information -->
                            <h6 class="mb-3"><i class="bi bi-briefcase me-2"></i>المعلومات التجارية</h6>
                            <div class="row mb-3">
                                <div class="col-md-6" id="edit_tax_number_field">
                                    <label class="form-label"><i class="bi bi-receipt me-1"></i>الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="tax_number" id="edit_tax_number"
                                           placeholder="رقم السجل التجاري أو الضريبي" maxlength="50">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-credit-card me-1"></i>حد الائتمان</label>
                                    <input type="number" class="form-control" name="credit_limit" id="edit_credit_limit"
                                           placeholder="0.00" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-percent me-1"></i>نسبة الخصم (%)</label>
                                    <input type="number" class="form-control" name="discount_rate" id="edit_discount_rate"
                                           placeholder="0.00" step="0.01" min="0" max="100">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><i class="bi bi-toggles me-1"></i>حالة العميل</label>
                                    <div class="form-check form-switch mt-2">
                                        <input class="form-check-input" type="checkbox" name="status" id="edit_status">
                                        <label class="form-check-label">عميل نشط</label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><i class="bi bi-text-paragraph me-1"></i>ملاحظات</label>
                                <textarea class="form-control" name="notes" id="edit_notes" rows="3"
                                          placeholder="ملاحظات إضافية عن العميل (اختياري)"></textarea>
                            </div>

                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>معلومة:</strong> تاريخ الإنشاء: <span id="edit_created_at"></span>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>إلغاء
                            </button>
                            <button type="submit" class="btn btn-gradient">
                                <i class="bi bi-check-circle me-1"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Buttons -->
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let customersTable;

        $(document).ready(function() {
            // Initialize DataTable
            customersTable = $('#customersTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[1, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [9] }, // Actions column
                    { searchable: false, targets: [0, 9] } // ID and Actions columns
                ],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="bi bi-file-earmark-excel me-1"></i>Excel',
                        className: 'btn btn-success btn-sm',
                        exportOptions: {
                            columns: [1, 2, 3, 4, 5, 6, 7, 8] // Exclude ID and Actions
                        }
                    }
                ]
            });

            // Customer filter functionality
            $('input[name="customerFilter"]').on('change', function() {
                const filterValue = $(this).val();
                customersTable.search('').draw();

                if (filterValue !== 'all') {
                    customersTable.rows().every(function() {
                        const row = this.node();
                        const $row = $(row);

                        let show = false;
                        switch (filterValue) {
                            case 'individual':
                                show = $row.data('customer-type') === 'individual';
                                break;
                            case 'company':
                                show = $row.data('customer-type') === 'company';
                                break;
                            case 'active':
                                show = $row.data('status') === 'active';
                                break;
                        }

                        if (show) {
                            $row.show();
                        } else {
                            $row.hide();
                        }
                    });
                } else {
                    customersTable.rows().every(function() {
                        $(this.node()).show();
                    });
                }
            });

            // Customer type change handlers
            $('#add_customer_type').on('change', function() {
                togglePersonalInfo('add', $(this).val());
            });

            $('#edit_customer_type').on('change', function() {
                togglePersonalInfo('edit', $(this).val());
            });

            // Form validation
            $('#addCustomerForm').on('submit', function(e) {
                if (!validateCustomerForm(this)) {
                    e.preventDefault();
                    return false;
                }
            });

            $('#editCustomerForm').on('submit', function(e) {
                if (!validateCustomerForm(this)) {
                    e.preventDefault();
                    return false;
                }
            });

            // Clear form when modal is hidden
            $('#addCustomerModal').on('hidden.bs.modal', function() {
                $('#addCustomerForm')[0].reset();
                togglePersonalInfo('add', 'individual');
            });
        });

        function togglePersonalInfo(prefix, customerType) {
            const personalInfoDiv = document.getElementById(prefix + '_personal_info');
            const taxNumberField = document.getElementById(prefix + '_tax_number_field');

            if (customerType === 'individual') {
                personalInfoDiv.style.display = 'block';
                if (taxNumberField) {
                    taxNumberField.style.display = 'none';
                }
            } else {
                personalInfoDiv.style.display = 'none';
                if (taxNumberField) {
                    taxNumberField.style.display = 'block';
                }
            }
        }

        function validateCustomerForm(form) {
            const name = $(form).find('input[name="name"]').val().trim();
            const email = $(form).find('input[name="email"]').val().trim();
            const discountRate = parseFloat($(form).find('input[name="discount_rate"]').val());

            if (name.length < 2) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'يجب أن يكون اسم العميل أكثر من حرفين'
                });
                return false;
            }

            if (email && !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'البريد الإلكتروني غير صحيح'
                });
                return false;
            }

            if (discountRate < 0 || discountRate > 100) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'نسبة الخصم يجب أن تكون بين 0 و 100'
                });
                return false;
            }

            return true;
        }

        function viewCustomer(customer) {
            const salesValue = customer.total_sales_value || 0;
            const salesCount = customer.sales_count || 0;
            const lastSaleDate = customer.last_sale_date ? new Date(customer.last_sale_date).toLocaleDateString('ar-SA') : 'لا توجد مشتريات';

            let personalInfo = '';
            if (customer.customer_type === 'individual') {
                const age = customer.date_of_birth ? calculateAge(customer.date_of_birth) : 'غير محدد';
                const gender = customer.gender === 'male' ? 'ذكر' : 'أنثى';
                personalInfo = `
                    <p><strong>العمر:</strong> ${age}</p>
                    <p><strong>الجنس:</strong> ${gender}</p>
                `;
            }

            Swal.fire({
                title: customer.name,
                html: `
                    <div class="text-start">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الاتصال</h6>
                                <p><strong>الهاتف:</strong> ${customer.phone || 'غير محدد'}</p>
                                <p><strong>البريد الإلكتروني:</strong> ${customer.email || 'غير محدد'}</p>
                                <p><strong>العنوان:</strong> ${customer.address || 'غير محدد'}</p>
                                ${personalInfo}
                            </div>
                            <div class="col-md-6">
                                <h6>المعلومات التجارية</h6>
                                <p><strong>نوع العميل:</strong> ${customer.customer_type === 'individual' ? 'فرد' : 'شركة'}</p>
                                <p><strong>الرقم الضريبي:</strong> ${customer.tax_number || 'غير محدد'}</p>
                                <p><strong>حد الائتمان:</strong> ${customer.credit_limit > 0 ? customer.credit_limit : 'غير محدد'}</p>
                                <p><strong>نسبة الخصم:</strong> ${customer.discount_rate}%</p>
                                <p><strong>الحالة:</strong> ${customer.status ? 'نشط' : 'غير نشط'}</p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <h5 class="text-primary">${salesCount}</h5>
                                <small>عملية شراء</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <h5 class="text-success">${salesValue.toFixed(2)}</h5>
                                <small>إجمالي المشتريات</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <h5 class="text-info">${lastSaleDate}</h5>
                                <small>آخر شراء</small>
                            </div>
                        </div>
                        ${customer.notes ? `<hr><p><strong>ملاحظات:</strong> ${customer.notes}</p>` : ''}
                    </div>
                `,
                width: 700,
                showCloseButton: true,
                showConfirmButton: false
            });
        }

        function editCustomer(customer) {
            document.getElementById('edit_id').value = customer.id;
            document.getElementById('edit_name').value = customer.name || '';
            document.getElementById('edit_phone').value = customer.phone || '';
            document.getElementById('edit_email').value = customer.email || '';
            document.getElementById('edit_address').value = customer.address || '';
            document.getElementById('edit_date_of_birth').value = customer.date_of_birth || '';
            document.getElementById('edit_gender').value = customer.gender || 'male';
            document.getElementById('edit_customer_type').value = customer.customer_type || 'individual';
            document.getElementById('edit_tax_number').value = customer.tax_number || '';
            document.getElementById('edit_credit_limit').value = customer.credit_limit || '';
            document.getElementById('edit_discount_rate').value = customer.discount_rate || '';
            document.getElementById('edit_notes').value = customer.notes || '';
            document.getElementById('edit_status').checked = customer.status == 1;

            togglePersonalInfo('edit', customer.customer_type || 'individual');

            const createdAtElement = document.getElementById('edit_created_at');
            if (customer.created_at && customer.created_at !== '0000-00-00 00:00:00') {
                createdAtElement.textContent = formatDate(customer.created_at);
            } else {
                createdAtElement.textContent = 'غير محدد';
            }

            new bootstrap.Modal(document.getElementById('editCustomerModal')).show();
        }

        function deleteCustomer(id, name) {
            Swal.fire({
                title: 'تأكيد الحذف',
                text: `هل أنت متأكد من حذف العميل "${name}"؟`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Create and submit form
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" value="${id}">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

        function calculateAge(birthDate) {
            const today = new Date();
            const birth = new Date(birthDate);
            let age = today.getFullYear() - birth.getFullYear();
            const monthDiff = today.getMonth() - birth.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                age--;
            }

            return age + ' سنة';
        }

        function refreshTable() {
            location.reload();
        }

        function exportData() {
            customersTable.button('.buttons-excel').trigger();
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Add loading animation to buttons
        $('form').on('submit', function() {
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            submitBtn.html('<i class="bi bi-hourglass-split me-1"></i>جاري الحفظ...').prop('disabled', true);

            setTimeout(function() {
                submitBtn.html(originalText).prop('disabled', false);
            }, 3000);
        });
    </script>
</body>
</html>
