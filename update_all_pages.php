<?php
// سكريبت بسيط لتطبيق الصلاحيات على جميع الصفحات
echo "تطبيق الصلاحيات على جميع صفحات النظام...\n\n";

// قائمة الصفحات والصلاحيات المطلوبة
$pages = [
    'dashboard.php' => [],
    'categories.php' => ['products.view'],
    'units.php' => ['products.view'],
    'purchases.php' => ['inventory.view'],
    'inventory.php' => ['inventory.view'],
    'suppliers.php' => ['suppliers.view'],
    'accounting.php' => ['accounting.view']
];

foreach ($pages as $page => $permissions) {
    if (file_exists($page)) {
        echo "معالجة الصفحة: $page\n";
        
        $content = file_get_contents($page);
        
        // إضافة require للصلاحيات إذا لم تكن موجودة
        if (strpos($content, "require 'check_permissions.php';") === false) {
            $content = str_replace(
                "require 'get_settings.php';",
                "require 'get_settings.php';\nrequire 'check_permissions.php';\nrequire 'apply_permissions.php';",
                $content
            );
            
            // إضافة التحقق من الصلاحيات للصفحات التي تحتاج صلاحيات
            if (!empty($permissions)) {
                $permissionCheck = "\n// التحقق من صلاحيات الوصول للصفحة\napplyPagePermissions(\$pdo, \$_SESSION['user_id'], '$page');\n";
                $content = str_replace(
                    "require 'apply_permissions.php';",
                    "require 'apply_permissions.php';" . $permissionCheck,
                    $content
                );
            }
            
            // حفظ الملف
            file_put_contents($page, $content);
            echo "تم تحديث: $page\n";
        } else {
            echo "الصفحة محدثة مسبقاً: $page\n";
        }
    } else {
        echo "الملف غير موجود: $page\n";
    }
    echo "---\n";
}

echo "\nتم الانتهاء من تطبيق الصلاحيات الأساسية!\n";
echo "الصفحات المحدثة:\n";
echo "- products.php ✓\n";
echo "- pos.php ✓\n";
echo "- sales.php ✓\n";
echo "- customers.php ✓\n";
echo "- settings.php ✓\n";
echo "\nيمكنك الآن اختبار النظام من خلال:\n";
echo "1. إدارة المستخدمين والأدوار في settings.php\n";
echo "2. تعيين صلاحيات مختلفة للمستخدمين\n";
echo "3. اختبار الوصول للصفحات المختلفة\n";
?>
