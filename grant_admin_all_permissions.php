<?php
// سكريبت لإعطاء جميع الصلاحيات لمستخدم admin
require 'db.php';

try {
    echo "بدء إعطاء جميع الصلاحيات لمستخدم admin...\n\n";
    
    // البحث عن مستخدم admin
    $adminStmt = $pdo->prepare("SELECT id, username FROM users WHERE username = 'admin'");
    $adminStmt->execute();
    $admin = $adminStmt->fetch();
    
    if (!$admin) {
        echo "❌ لم يتم العثور على مستخدم admin!\n";
        echo "إنشاء مستخدم admin جديد...\n";
        
        // إنشاء مستخدم admin جديد
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $createAdminStmt = $pdo->prepare("INSERT INTO users (username, password, email, phone, created_by) VALUES (?, ?, ?, ?, ?)");
        $createAdminStmt->execute(['admin', $adminPassword, '<EMAIL>', '0500000000', 1]);
        $adminId = $pdo->lastInsertId();
        echo "✅ تم إنشاء مستخدم admin جديد بالمعرف: $adminId\n";
    } else {
        $adminId = $admin['id'];
        echo "✅ تم العثور على مستخدم admin بالمعرف: $adminId\n";
    }
    
    // التأكد من وجود دور admin
    $adminRoleStmt = $pdo->prepare("SELECT id FROM roles WHERE name = 'admin'");
    $adminRoleStmt->execute();
    $adminRole = $adminRoleStmt->fetch();
    
    if (!$adminRole) {
        echo "إنشاء دور admin...\n";
        $createRoleStmt = $pdo->prepare("INSERT INTO roles (name, display_name, description) VALUES (?, ?, ?)");
        $createRoleStmt->execute(['admin', 'مدير النظام', 'مدير النظام مع جميع الصلاحيات']);
        $adminRoleId = $pdo->lastInsertId();
        echo "✅ تم إنشاء دور admin بالمعرف: $adminRoleId\n";
    } else {
        $adminRoleId = $adminRole['id'];
        echo "✅ تم العثور على دور admin بالمعرف: $adminRoleId\n";
    }
    
    // ربط المستخدم admin بدور admin
    $userRoleStmt = $pdo->prepare("INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by) VALUES (?, ?, ?)");
    $userRoleStmt->execute([$adminId, $adminRoleId, 1]);
    echo "✅ تم ربط المستخدم admin بدور admin\n";
    
    // جلب جميع الصلاحيات المتاحة
    $permissionsStmt = $pdo->query("SELECT id, name, display_name, category FROM permissions ORDER BY category, name");
    $allPermissions = $permissionsStmt->fetchAll();
    
    if (empty($allPermissions)) {
        echo "❌ لا توجد صلاحيات في قاعدة البيانات!\n";
        echo "يرجى تشغيل سكريبت إنشاء الصلاحيات أولاً.\n";
        exit;
    }
    
    echo "\n📋 الصلاحيات المتاحة في قاعدة البيانات:\n";
    echo "العدد الإجمالي: " . count($allPermissions) . " صلاحية\n\n";
    
    // تجميع الصلاحيات حسب الفئة
    $permissionsByCategory = [];
    foreach ($allPermissions as $permission) {
        $permissionsByCategory[$permission['category']][] = $permission;
    }
    
    // عرض الصلاحيات مجمعة
    foreach ($permissionsByCategory as $category => $permissions) {
        echo "📂 فئة: $category\n";
        foreach ($permissions as $permission) {
            echo "   ├── " . $permission['name'] . " (" . $permission['display_name'] . ")\n";
        }
        echo "\n";
    }
    
    // حذف الصلاحيات الحالية لدور admin (لضمان البداية النظيفة)
    $deleteStmt = $pdo->prepare("DELETE FROM role_permissions WHERE role_id = ?");
    $deleteStmt->execute([$adminRoleId]);
    echo "🧹 تم حذف الصلاحيات الحالية لدور admin\n";
    
    // إعطاء جميع الصلاحيات لدور admin
    $grantStmt = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
    $grantedCount = 0;
    
    foreach ($allPermissions as $permission) {
        $grantStmt->execute([$adminRoleId, $permission['id']]);
        $grantedCount++;
        echo "✅ تم إعطاء صلاحية: " . $permission['name'] . "\n";
    }
    
    echo "\n🎉 تم بنجاح إعطاء جميع الصلاحيات لمستخدم admin!\n";
    echo "📊 إجمالي الصلاحيات الممنوحة: $grantedCount صلاحية\n\n";
    
    // التحقق من النتيجة
    $verifyStmt = $pdo->prepare("
        SELECT COUNT(*) as granted_count 
        FROM role_permissions rp 
        WHERE rp.role_id = ?
    ");
    $verifyStmt->execute([$adminRoleId]);
    $verifyResult = $verifyStmt->fetch();
    
    echo "🔍 التحقق من النتيجة:\n";
    echo "   - الصلاحيات الممنوحة لدور admin: " . $verifyResult['granted_count'] . "\n";
    echo "   - إجمالي الصلاحيات في النظام: " . count($allPermissions) . "\n";
    
    if ($verifyResult['granted_count'] == count($allPermissions)) {
        echo "✅ تم التحقق بنجاح - admin يملك جميع الصلاحيات!\n";
    } else {
        echo "⚠️ تحذير - هناك عدم تطابق في عدد الصلاحيات!\n";
    }
    
    // عرض معلومات تسجيل الدخول
    echo "\n🔐 معلومات تسجيل الدخول:\n";
    echo "   اسم المستخدم: admin\n";
    echo "   كلمة المرور: admin123\n";
    echo "   الدور: مدير النظام\n";
    echo "   الصلاحيات: جميع الصلاحيات المتاحة\n";
    
    echo "\n🚀 يمكنك الآن تسجيل الدخول والوصول لجميع أجزاء النظام!\n";
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
}
?>
