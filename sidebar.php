<?php
// ملف الشريط الجانبي الموحد مع الصلاحيات
require_once 'check_permissions.php';

function renderSidebar($pdo, $userId, $currentPage = '') {
    $companyName = getSetting('company_name', 'نظام نقاط البيع');
    $companyLogo = getSetting('company_logo');
    
    // جلب معلومات المستخدم
    $userStmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
    $userStmt->execute([$userId]);
    $user = $userStmt->fetch();
    $username = $user['username'] ?? 'مستخدم';
    
    echo '<div class="sidebar">';
    echo '<div class="logo">';
    
    if (!empty($companyLogo) && file_exists($companyLogo)) {
        echo '<img src="' . $companyLogo . '" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">';
        echo '<h5 class="mb-0">' . htmlspecialchars($companyName) . '</h5>';
    } else {
        echo '<h4 class="mb-0"><i class="bi bi-shop"></i> ' . htmlspecialchars($companyName) . '</h4>';
    }
    
    echo '<small class="text-light">مرحباً، ' . htmlspecialchars($username) . '</small>';
    echo '</div>';
    
    echo '<nav class="nav flex-column mt-3">';

    // تعريف جميع عناصر القائمة مع صلاحياتها
    $menuItems = [
        [
            'page' => 'dashboard.php',
            'title' => 'لوحة التحكم',
            'icon' => 'bi-house-door',
            'permission' => '', // متاح للجميع
            'category' => 'main'
        ],
        [
            'page' => 'pos.php',
            'title' => 'نقطة البيع',
            'icon' => 'bi-cart',
            'permission' => 'pos.access',
            'category' => 'sales'
        ],
        [
            'page' => 'sales.php',
            'title' => 'سجل المبيعات',
            'icon' => 'bi-graph-up',
            'permission' => 'sales.view',
            'category' => 'sales'
        ],
        [
            'page' => 'products.php',
            'title' => 'إدارة المنتجات',
            'icon' => 'bi-box',
            'permission' => 'products.view',
            'category' => 'products'
        ],
        [
            'page' => 'categories.php',
            'title' => 'إدارة الفئات',
            'icon' => 'bi-folder',
            'permission' => 'products.view',
            'category' => 'products'
        ],
        [
            'page' => 'units.php',
            'title' => 'إدارة الوحدات',
            'icon' => 'bi-rulers',
            'permission' => 'products.view',
            'category' => 'products'
        ],
        [
            'page' => 'inventory.php',
            'title' => 'إدارة المخزون',
            'icon' => 'bi-boxes',
            'permission' => 'inventory.view',
            'category' => 'inventory'
        ],
        [
            'page' => 'purchases.php',
            'title' => 'سجل المشتريات',
            'icon' => 'bi-clipboard-data',
            'permission' => 'inventory.view',
            'category' => 'inventory'
        ],
        [
            'page' => 'customers.php',
            'title' => 'إدارة العملاء',
            'icon' => 'bi-people',
            'permission' => 'customers.view',
            'category' => 'customers'
        ],
        [
            'page' => 'suppliers.php',
            'title' => 'إدارة الموردين',
            'icon' => 'bi-building',
            'permission' => 'suppliers.view',
            'category' => 'customers'
        ],
        [
            'page' => 'accounting.php',
            'title' => 'النظام المحاسبي',
            'icon' => 'bi-currency-dollar',
            'permission' => 'accounting.view',
            'category' => 'reports'
        ],
        [
            'page' => 'reports.php',
            'title' => 'التقارير والإحصائيات',
            'icon' => 'bi-bar-chart',
            'permission' => 'reports.view',
            'category' => 'reports'
        ],
        [
            'page' => 'settings.php',
            'title' => 'إعدادات النظام',
            'icon' => 'bi-gear',
            'permission' => 'settings.view',
            'category' => 'settings'
        ]
    ];

    // تجميع العناصر حسب الفئة
    $categorizedItems = [];
    $visibleItemsCount = 0;

    foreach ($menuItems as $item) {
        // التحقق من الصلاحية
        $hasPermission = empty($item['permission']) ||
                        hasPermission($pdo, $userId, $item['permission']) ||
                        isAdmin($pdo, $userId);

        if ($hasPermission) {
            $categorizedItems[$item['category']][] = $item;
            $visibleItemsCount++;
        }
    }

    // عرض العناصر مع فواصل الفئات
    $categoryTitles = [
        'main' => '',
        'sales' => 'المبيعات',
        'products' => 'إدارة المنتجات',
        'inventory' => 'إدارة المخزون',
        'customers' => 'العملاء والموردين',
        'reports' => 'التقارير والمحاسبة',
        'settings' => 'الإعدادات'
    ];

    $categoryOrder = ['main', 'sales', 'products', 'inventory', 'customers', 'reports', 'settings'];

    foreach ($categoryOrder as $category) {
        if (!isset($categorizedItems[$category])) continue;

        // عرض عنوان الفئة (إذا لم تكن الفئة الرئيسية)
        if ($category !== 'main' && !empty($categoryTitles[$category])) {
            echo '<div class="nav-category-divider mt-3 mb-2">';
            echo '<small class="text-light opacity-75 px-3">' . $categoryTitles[$category] . '</small>';
            echo '</div>';
        }

        // عرض عناصر الفئة
        foreach ($categorizedItems[$category] as $item) {
            $activeClass = ($currentPage === $item['page']) ? 'active' : '';
            echo '<a class="nav-link ' . $activeClass . '" href="' . $item['page'] . '">';
            echo '<i class="' . $item['icon'] . ' me-2"></i> ' . $item['title'];
            echo '</a>';
        }
    }

    // إذا لم يكن هناك عناصر مرئية (عدا لوحة التحكم)
    if ($visibleItemsCount <= 1) {
        echo '<div class="alert alert-warning mx-3 mt-3" style="font-size: 0.8rem;">';
        echo '<i class="bi bi-exclamation-triangle me-2"></i>';
        echo 'صلاحيات محدودة - تواصل مع المدير';
        echo '</div>';
    }
    
    echo '<hr class="text-light">';
    echo '<a class="nav-link text-warning" href="logout.php">';
    echo '<i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج';
    echo '</a>';
    echo '</nav>';
    echo '</div>';
}

// دالة لإنشاء CSS الموحد
function renderCommonCSS() {
    echo '<style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 0;
            text-decoration: none;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-left-color: #fff;
            transform: translateX(-5px);
        }

        .nav-category-divider {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 10px;
        }

        .nav-category-divider small {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .page-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .table-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .permission-denied {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .permission-denied i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #dc3545;
        }
    </style>';
}

// دالة لإنشاء JavaScript الموحد
function renderCommonJS($pdo, $userId) {
    generatePermissionJS($pdo, $userId);
    
    echo '<script>
        // دوال مساعدة للصلاحيات
        function checkPermissionAndShow(permission, elementId) {
            if (!hasPermission(permission)) {
                document.getElementById(elementId).style.display = "none";
            }
        }
        
        function showPermissionDenied(message = "ليس لديك صلاحية لتنفيذ هذا الإجراء") {
            Swal.fire({
                icon: "error",
                title: "غير مصرح",
                text: message,
                confirmButtonText: "موافق"
            });
        }
        
        function executeWithPermission(permission, callback, errorMessage) {
            if (hasPermission(permission)) {
                callback();
            } else {
                showPermissionDenied(errorMessage);
            }
        }
        
        // إخفاء العناصر بدون صلاحية عند تحميل الصفحة
        document.addEventListener("DOMContentLoaded", function() {
            hideElementsWithoutPermission();
        });
    </script>';
}
?>
