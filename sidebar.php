<?php
// ملف الشريط الجانبي الموحد مع الصلاحيات
require_once 'check_permissions.php';

function renderSidebar($pdo, $userId, $currentPage = '') {
    $companyName = getSetting('company_name', 'نظام نقاط البيع');
    $companyLogo = getSetting('company_logo');
    
    // جلب معلومات المستخدم
    $userStmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
    $userStmt->execute([$userId]);
    $user = $userStmt->fetch();
    $username = $user['username'] ?? 'مستخدم';
    
    echo '<div class="sidebar">';
    echo '<div class="logo">';
    
    if (!empty($companyLogo) && file_exists($companyLogo)) {
        echo '<img src="' . $companyLogo . '" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">';
        echo '<h5 class="mb-0">' . htmlspecialchars($companyName) . '</h5>';
    } else {
        echo '<h4 class="mb-0"><i class="bi bi-shop"></i> ' . htmlspecialchars($companyName) . '</h4>';
    }
    
    echo '<small class="text-light">مرحباً، ' . htmlspecialchars($username) . '</small>';
    echo '</div>';
    
    echo '<nav class="nav flex-column mt-3">';
    
    // لوحة التحكم - متاحة للجميع
    $activeClass = ($currentPage === 'dashboard.php') ? 'active' : '';
    echo '<a class="nav-link ' . $activeClass . '" href="dashboard.php">';
    echo '<i class="bi bi-house-door me-2"></i> لوحة التحكم';
    echo '</a>';
    
    // إدارة المنتجات
    if (hasPermission($pdo, $userId, 'products.view') || isAdmin($pdo, $userId)) {
        $activeClass = ($currentPage === 'products.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="products.php">';
        echo '<i class="bi bi-box me-2"></i> إدارة المنتجات';
        echo '</a>';
        
        $activeClass = ($currentPage === 'categories.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="categories.php">';
        echo '<i class="bi bi-folder me-2"></i> إدارة الفئات';
        echo '</a>';
        
        $activeClass = ($currentPage === 'units.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="units.php">';
        echo '<i class="bi bi-rulers me-2"></i> إدارة الوحدات';
        echo '</a>';
    }
    
    // نقطة البيع
    if (hasPermission($pdo, $userId, 'pos.access') || isAdmin($pdo, $userId)) {
        $activeClass = ($currentPage === 'pos.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="pos.php">';
        echo '<i class="bi bi-cart me-2"></i> نقطة البيع';
        echo '</a>';
    }
    
    // المبيعات
    if (hasPermission($pdo, $userId, 'sales.view') || isAdmin($pdo, $userId)) {
        $activeClass = ($currentPage === 'sales.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="sales.php">';
        echo '<i class="bi bi-graph-up me-2"></i> سجل المبيعات';
        echo '</a>';
    }
    
    // إدارة المخزون
    if (hasPermission($pdo, $userId, 'inventory.view') || isAdmin($pdo, $userId)) {
        $activeClass = ($currentPage === 'purchases.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="purchases.php">';
        echo '<i class="bi bi-clipboard-data me-2"></i> سجل المشتريات';
        echo '</a>';
        
        $activeClass = ($currentPage === 'inventory.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="inventory.php">';
        echo '<i class="bi bi-boxes me-2"></i> إدارة المخزون';
        echo '</a>';
    }
    
    // العملاء
    if (hasPermission($pdo, $userId, 'customers.view') || isAdmin($pdo, $userId)) {
        $activeClass = ($currentPage === 'customers.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="customers.php">';
        echo '<i class="bi bi-people me-2"></i> إدارة العملاء';
        echo '</a>';
    }
    
    // الموردين
    if (hasPermission($pdo, $userId, 'suppliers.view') || isAdmin($pdo, $userId)) {
        $activeClass = ($currentPage === 'suppliers.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="suppliers.php">';
        echo '<i class="bi bi-building me-2"></i> إدارة الموردين';
        echo '</a>';
    }
    
    // النظام المحاسبي
    if (hasPermission($pdo, $userId, 'accounting.view') || isAdmin($pdo, $userId)) {
        $activeClass = ($currentPage === 'accounting.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="accounting.php">';
        echo '<i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي';
        echo '</a>';
    }
    
    // التقارير
    if (hasPermission($pdo, $userId, 'reports.view') || isAdmin($pdo, $userId)) {
        $activeClass = ($currentPage === 'reports.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="reports.php">';
        echo '<i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات';
        echo '</a>';
    }
    
    // الإعدادات
    if (hasPermission($pdo, $userId, 'settings.view') || isAdmin($pdo, $userId)) {
        $activeClass = ($currentPage === 'settings.php') ? 'active' : '';
        echo '<a class="nav-link ' . $activeClass . '" href="settings.php">';
        echo '<i class="bi bi-gear me-2"></i> إعدادات النظام';
        echo '</a>';
    }
    
    echo '<hr class="text-light">';
    echo '<a class="nav-link text-warning" href="logout.php">';
    echo '<i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج';
    echo '</a>';
    echo '</nav>';
    echo '</div>';
}

// دالة لإنشاء CSS الموحد
function renderCommonCSS() {
    echo '<style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 0;
            text-decoration: none;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-left-color: #fff;
            transform: translateX(-5px);
        }
        
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .page-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .table-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .permission-denied {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .permission-denied i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #dc3545;
        }
    </style>';
}

// دالة لإنشاء JavaScript الموحد
function renderCommonJS($pdo, $userId) {
    generatePermissionJS($pdo, $userId);
    
    echo '<script>
        // دوال مساعدة للصلاحيات
        function checkPermissionAndShow(permission, elementId) {
            if (!hasPermission(permission)) {
                document.getElementById(elementId).style.display = "none";
            }
        }
        
        function showPermissionDenied(message = "ليس لديك صلاحية لتنفيذ هذا الإجراء") {
            Swal.fire({
                icon: "error",
                title: "غير مصرح",
                text: message,
                confirmButtonText: "موافق"
            });
        }
        
        function executeWithPermission(permission, callback, errorMessage) {
            if (hasPermission(permission)) {
                callback();
            } else {
                showPermissionDenied(errorMessage);
            }
        }
        
        // إخفاء العناصر بدون صلاحية عند تحميل الصفحة
        document.addEventListener("DOMContentLoaded", function() {
            hideElementsWithoutPermission();
        });
    </script>';
}
?>
