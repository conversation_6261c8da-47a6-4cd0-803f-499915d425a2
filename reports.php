<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';

// إعدادات النظام
$currency = getSetting('currency', 'ر.س');
$companyName = getSetting('company_name', 'نظام نقاط البيع');

// جلب الإحصائيات العامة
try {
    // إحصائيات المبيعات
    $salesStats = $pdo->query("
        SELECT 
            COUNT(*) as total_sales,
            COALESCE(SUM(total), 0) as total_amount,
            COALESCE(AVG(total), 0) as average_sale,
            COALESCE(SUM(tax), 0) as total_tax
        FROM sales
    ")->fetch();

    // إحصائيات المنتجات
    $productsStats = $pdo->query("
        SELECT 
            COUNT(*) as total_products,
            COALESCE(SUM(quantity), 0) as total_quantity,
            COALESCE(SUM(quantity * cost_price), 0) as inventory_value,
            COUNT(CASE WHEN quantity <= 5 THEN 1 END) as low_stock_count
        FROM products
    ")->fetch();

    // إحصائيات العملاء
    $customersStats = $pdo->query("
        SELECT 
            COUNT(*) as total_customers,
            COUNT(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 END) as customers_with_phone
        FROM customers
    ")->fetch();

    // أفضل المنتجات مبيعاً
    $topProducts = $pdo->query("
        SELECT 
            p.name,
            p.barcode,
            SUM(si.quantity) as total_sold,
            SUM(si.quantity * si.price) as total_revenue
        FROM sale_items si
        JOIN products p ON si.product_id = p.id
        GROUP BY p.id, p.name, p.barcode
        ORDER BY total_sold DESC
        LIMIT 10
    ")->fetchAll();

    // المبيعات الشهرية
    $monthlySales = $pdo->query("
        SELECT 
            DATE_FORMAT(sale_date, '%Y-%m') as month,
            COUNT(*) as sales_count,
            SUM(total) as total_amount
        FROM sales
        WHERE sale_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(sale_date, '%Y-%m')
        ORDER BY month DESC
        LIMIT 12
    ")->fetchAll();

    // المبيعات اليومية للأسبوع الماضي
    $dailySales = $pdo->query("
        SELECT 
            DATE(sale_date) as sale_day,
            COUNT(*) as sales_count,
            SUM(total) as total_amount
        FROM sales
        WHERE sale_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(sale_date)
        ORDER BY sale_day DESC
    ")->fetchAll();

    // طرق الدفع
    $paymentMethods = $pdo->query("
        SELECT 
            payment_method,
            COUNT(*) as count,
            SUM(total) as amount
        FROM sales
        GROUP BY payment_method
        ORDER BY amount DESC
    ")->fetchAll();

} catch (PDOException $e) {
    // في حالة وجود خطأ، تعيين قيم افتراضية
    $salesStats = ['total_sales' => 0, 'total_amount' => 0, 'average_sale' => 0, 'total_tax' => 0];
    $productsStats = ['total_products' => 0, 'total_quantity' => 0, 'inventory_value' => 0, 'low_stock_count' => 0];
    $customersStats = ['total_customers' => 0, 'customers_with_phone' => 0];
    $topProducts = [];
    $monthlySales = [];
    $dailySales = [];
    $paymentMethods = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - <?php echo $companyName; ?></title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-left-color: #fff;
            transform: translateX(-5px);
        }
        
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .page-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .report-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .table-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>
        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link active" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-bar-chart me-2"></i>التقارير والإحصائيات</h2>
                    <small class="text-muted">تقارير شاملة وإحصائيات مفصلة لجميع عمليات النظام</small>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-gradient" onclick="printAllReports()">
                        <i class="bi bi-printer me-2"></i>طباعة التقارير
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo number_format($salesStats['total_sales']); ?></h4>
                            <p class="mb-0 small">إجمالي المبيعات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-cart-check"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #2ecc71);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo number_format($salesStats['total_amount'], 2); ?> <?php echo $currency; ?></h4>
                            <p class="mb-0 small">إجمالي المبلغ</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo number_format($productsStats['total_products']); ?></h4>
                            <p class="mb-0 small">إجمالي المنتجات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-box"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo number_format($customersStats['total_customers']); ?></h4>
                            <p class="mb-0 small">إجمالي العملاء</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Reports Section -->
        <div class="content-card">
            <h5 class="mb-4"><i class="bi bi-file-earmark-text me-2"></i>التقارير المالية</h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="report-card" onclick="openReport('income_statement.php')">
                        <div class="text-center">
                            <i class="bi bi-file-earmark-text display-4 text-primary mb-3"></i>
                            <h5>قائمة الدخل</h5>
                            <p class="text-muted">عرض الإيرادات والمصروفات وصافي الربح</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="report-card" onclick="openReport('balance_sheet.php')">
                        <div class="text-center">
                            <i class="bi bi-building display-4 text-success mb-3"></i>
                            <h5>الميزانية العمومية</h5>
                            <p class="text-muted">عرض الأصول والخصوم وحقوق الملكية</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="report-card" onclick="openReport('cash_flow.php')">
                        <div class="text-center">
                            <i class="bi bi-cash-stack display-4 text-info mb-3"></i>
                            <h5>قائمة التدفقات النقدية</h5>
                            <p class="text-muted">عرض التدفقات النقدية الداخلة والخارجة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-bar-chart me-2"></i>المبيعات الشهرية</h5>
                    <div class="chart-container">
                        <canvas id="monthlySalesChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-pie-chart me-2"></i>طرق الدفع</h5>
                    <div class="chart-container">
                        <canvas id="paymentMethodsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Products Section -->
        <div class="content-card">
            <h5 class="mb-3"><i class="bi bi-trophy me-2"></i>أفضل المنتجات مبيعاً</h5>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>اسم المنتج</th>
                                <th>الباركود</th>
                                <th>الكمية المباعة</th>
                                <th>إجمالي الإيرادات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($topProducts)): ?>
                                <?php foreach ($topProducts as $product): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($product['name']); ?></strong></td>
                                    <td><span class="badge bg-primary"><?php echo htmlspecialchars($product['barcode']); ?></span></td>
                                    <td><?php echo number_format($product['total_sold']); ?></td>
                                    <td><strong><?php echo number_format($product['total_revenue'], 2); ?> <?php echo $currency; ?></strong></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="4" class="text-center text-muted">لا توجد بيانات مبيعات</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Daily Sales Section -->
        <div class="content-card">
            <h5 class="mb-3"><i class="bi bi-calendar-week me-2"></i>المبيعات اليومية (آخر 7 أيام)</h5>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>عدد المبيعات</th>
                                <th>إجمالي المبلغ</th>
                                <th>متوسط البيع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($dailySales)): ?>
                                <?php foreach ($dailySales as $day): ?>
                                <tr>
                                    <td><strong><?php echo date('Y-m-d', strtotime($day['sale_day'])); ?></strong></td>
                                    <td><?php echo number_format($day['sales_count']); ?></td>
                                    <td><strong><?php echo number_format($day['total_amount'], 2); ?> <?php echo $currency; ?></strong></td>
                                    <td><?php echo number_format($day['total_amount'] / max($day['sales_count'], 1), 2); ?> <?php echo $currency; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="4" class="text-center text-muted">لا توجد مبيعات في الأسبوع الماضي</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Additional Statistics -->
        <div class="row">
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-boxes me-2"></i>إحصائيات المخزون</h5>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>إجمالي المنتجات:</strong></td>
                            <td><?php echo number_format($productsStats['total_products']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>إجمالي الكمية:</strong></td>
                            <td><?php echo number_format($productsStats['total_quantity']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>قيمة المخزون:</strong></td>
                            <td><strong><?php echo number_format($productsStats['inventory_value'], 2); ?> <?php echo $currency; ?></strong></td>
                        </tr>
                        <tr>
                            <td><strong>منتجات قليلة المخزون:</strong></td>
                            <td><span class="badge bg-warning"><?php echo number_format($productsStats['low_stock_count']); ?></span></td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-people me-2"></i>إحصائيات العملاء</h5>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>إجمالي العملاء:</strong></td>
                            <td><?php echo number_format($customersStats['total_customers']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>عملاء لديهم أرقام هاتف:</strong></td>
                            <td><?php echo number_format($customersStats['customers_with_phone']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>متوسط البيع للعميل:</strong></td>
                            <td><strong><?php echo number_format($salesStats['average_sale'], 2); ?> <?php echo $currency; ?></strong></td>
                        </tr>
                        <tr>
                            <td><strong>إجمالي الضرائب:</strong></td>
                            <td><strong><?php echo number_format($salesStats['total_tax'], 2); ?> <?php echo $currency; ?></strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="content-card">
            <h5 class="mb-3"><i class="bi bi-lightning me-2"></i>إجراءات سريعة</h5>
            <div class="row">
                <div class="col-md-3">
                    <button class="btn btn-gradient w-100 mb-2" onclick="openReport('sales.php')">
                        <i class="bi bi-graph-up me-2"></i>سجل المبيعات
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="openReport('inventory.php')">
                        <i class="bi bi-boxes me-2"></i>تقرير المخزون
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-success w-100 mb-2" onclick="openReport('customers.php')">
                        <i class="bi bi-people me-2"></i>تقرير العملاء
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-info w-100 mb-2" onclick="openReport('accounting.php')">
                        <i class="bi bi-currency-dollar me-2"></i>النظام المحاسبي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let monthlySalesChart, paymentMethodsChart;
        let currency = '<?php echo $currency; ?>';

        $(document).ready(function() {
            // Initialize DataTables
            $('.table').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 10,
                order: []
            });

            // Initialize Charts
            initializeCharts();
        });

        function initializeCharts() {
            // Monthly Sales Chart
            const monthlySalesCtx = document.getElementById('monthlySalesChart').getContext('2d');

            const monthlyData = <?php echo json_encode($monthlySales); ?>;
            const monthLabels = monthlyData.map(item => {
                const date = new Date(item.month + '-01');
                return date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
            });
            const monthlyAmounts = monthlyData.map(item => parseFloat(item.total_amount));

            monthlySalesChart = new Chart(monthlySalesCtx, {
                type: 'line',
                data: {
                    labels: monthLabels.reverse(),
                    datasets: [{
                        label: 'المبيعات الشهرية',
                        data: monthlyAmounts.reverse(),
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ' + currency;
                                }
                            }
                        }
                    }
                }
            });

            // Payment Methods Chart
            const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');

            const paymentData = <?php echo json_encode($paymentMethods); ?>;
            const paymentLabels = paymentData.map(item => {
                const methods = {
                    'cash': 'نقدي',
                    'card': 'بطاقة ائتمان',
                    'transfer': 'تحويل بنكي',
                    'credit': 'آجل'
                };
                return methods[item.payment_method] || item.payment_method;
            });
            const paymentAmounts = paymentData.map(item => parseFloat(item.amount));

            paymentMethodsChart = new Chart(paymentMethodsCtx, {
                type: 'doughnut',
                data: {
                    labels: paymentLabels,
                    datasets: [{
                        data: paymentAmounts,
                        backgroundColor: [
                            '#27ae60',
                            '#3498db',
                            '#f39c12',
                            '#e74c3c',
                            '#9b59b6'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return context.label + ': ' + context.parsed.toLocaleString() + ' ' + currency + ' (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });
        }

        function openReport(url) {
            window.open(url, '_blank');
        }

        function printAllReports() {
            Swal.fire({
                title: 'طباعة التقارير',
                text: 'هل تريد طباعة جميع التقارير؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، اطبع',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.print();
                }
            });
        }

        // Auto-refresh data every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000); // 5 minutes
    </script>
</body>
</html>
