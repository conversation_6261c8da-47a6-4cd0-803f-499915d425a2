<?php
// ملف إعداد قاعدة البيانات
$host = 'localhost';
$user = 'root';
$pass = '';
$charset = 'utf8mb4';

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO("mysql:host=$host;charset=$charset", $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "✅ تم الاتصال بـ MySQL بنجاح<br>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS pos3 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات pos3<br>";
    
    // الاتصال بقاعدة البيانات
    $pdo->exec("USE pos3");
    echo "✅ تم الاتصال بقاعدة البيانات pos3<br>";
    
    // قراءة وتنفيذ ملف إنشاء الجداول
    $schema = file_get_contents('pos_schema.sql');
    if ($schema) {
        // تقسيم الاستعلامات
        $queries = explode(';', $schema);
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                $pdo->exec($query);
            }
        }
        echo "✅ تم إنشاء جميع الجداول بنجاح<br>";
    }
    
    // قراءة وتنفيذ البيانات التجريبية
    $sampleData = file_get_contents('sample_data.sql');
    if ($sampleData) {
        // تقسيم الاستعلامات
        $queries = explode(';', $sampleData);
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^--/', $query)) {
                try {
                    $pdo->exec($query);
                } catch (PDOException $e) {
                    // تجاهل الأخطاء المتعلقة بالبيانات المكررة
                    if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                        echo "⚠️ خطأ في تنفيذ: " . $e->getMessage() . "<br>";
                    }
                }
            }
        }
        echo "✅ تم إدراج البيانات التجريبية بنجاح<br>";
    }
    
    echo "<br><h3>🎉 تم إعداد النظام بنجاح!</h3>";
    echo "<p><a href='login.php' class='btn btn-primary'>انتقل إلى صفحة تسجيل الدخول</a></p>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong><br>";
    echo "اسم المستخدم: admin<br>";
    echo "كلمة المرور: admin123</p>";
    
} catch (PDOException $e) {
    echo "❌ خطأ في الاتصال: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام نقاط البيع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 50px 0;
        }
        .setup-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .btn {
            border-radius: 10px;
            padding: 10px 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <h2 class="text-center mb-4">🛠️ إعداد نظام نقاط البيع</h2>
            <div class="setup-output">
                <!-- النتائج ستظهر هنا -->
            </div>
        </div>
    </div>
</body>
</html>
