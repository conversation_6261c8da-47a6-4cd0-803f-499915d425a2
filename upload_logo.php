<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';

$response = ['success' => false, 'message' => '', 'logo_path' => ''];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['company_logo'])) {
    $uploadDir = 'uploads/';
    
    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    
    $file = $_FILES['company_logo'];
    $fileName = $file['name'];
    $fileTmpName = $file['tmp_name'];
    $fileSize = $file['size'];
    $fileError = $file['error'];
    $fileType = $file['type'];
    
    // التحقق من نوع الملف
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
    
    $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    
    if ($fileError === 0) {
        if (in_array($fileType, $allowedTypes) && in_array($fileExtension, $allowedExtensions)) {
            if ($fileSize < 5000000) { // 5MB max
                // إنشاء اسم ملف فريد
                $newFileName = 'logo_' . time() . '.' . $fileExtension;
                $uploadPath = $uploadDir . $newFileName;
                
                if (move_uploaded_file($fileTmpName, $uploadPath)) {
                    // حذف الشعار القديم إن وجد
                    $oldLogo = $pdo->query("SELECT setting_value FROM system_settings WHERE setting_key = 'company_logo'")->fetchColumn();
                    if ($oldLogo && file_exists($oldLogo)) {
                        unlink($oldLogo);
                    }
                    
                    // تحديث قاعدة البيانات
                    $stmt = $pdo->prepare("UPDATE system_settings SET setting_value = ? WHERE setting_key = 'company_logo'");
                    if ($stmt->execute([$uploadPath])) {
                        $response['success'] = true;
                        $response['message'] = 'تم رفع الشعار بنجاح!';
                        $response['logo_path'] = $uploadPath;
                    } else {
                        $response['message'] = 'خطأ في حفظ مسار الشعار في قاعدة البيانات';
                    }
                } else {
                    $response['message'] = 'خطأ في رفع الملف';
                }
            } else {
                $response['message'] = 'حجم الملف كبير جداً (الحد الأقصى 5MB)';
            }
        } else {
            $response['message'] = 'نوع الملف غير مدعوم. يرجى رفع صورة (JPG, PNG, GIF)';
        }
    } else {
        $response['message'] = 'خطأ في رفع الملف';
    }
}

header('Content-Type: application/json');
echo json_encode($response);
?>
