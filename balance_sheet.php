<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'generate_balance_sheet') {
            $asOfDate = $_POST['as_of_date'];
            
            // جلب الأصول من البيانات الحقيقية
            $assetsQuery = "
                SELECT
                    '1111' as account_code,
                    'النقدية في الصندوق' as account_name,
                    null as parent_id,
                    COALESCE(
                        (SELECT SUM(total) FROM sales WHERE payment_method = 'cash' AND DATE(sale_date) <= ?) -
                        (SELECT SUM(amount) FROM expenses WHERE payment_method = 'cash' AND DATE(expense_date) <= ?) -
                        (SELECT SUM(net_salary) FROM payroll WHERE payment_method = 'cash' AND DATE(payment_date) <= ?),
                        0
                    ) as balance
                UNION ALL
                SELECT
                    '1112' as account_code,
                    'البنك' as account_name,
                    null as parent_id,
                    COALESCE(
                        (SELECT SUM(total) FROM sales WHERE payment_method IN ('card', 'transfer') AND DATE(sale_date) <= ?) -
                        (SELECT SUM(amount) FROM expenses WHERE payment_method = 'bank' AND DATE(expense_date) <= ?) -
                        (SELECT SUM(net_salary) FROM payroll WHERE payment_method = 'bank' AND DATE(payment_date) <= ?),
                        0
                    ) as balance
                UNION ALL
                SELECT
                    '1120' as account_code,
                    'المخزون' as account_name,
                    null as parent_id,
                    COALESCE(SUM(p.quantity * p.cost_price), 0) as balance
                FROM products p
                WHERE p.quantity > 0
                UNION ALL
                SELECT
                    '1130' as account_code,
                    'العملاء' as account_name,
                    null as parent_id,
                    COALESCE(SUM(s.total), 0) as balance
                FROM sales s
                WHERE s.payment_method = 'credit' AND DATE(s.sale_date) <= ?
                ORDER BY account_code
            ";
            
            $assetsStmt = $pdo->prepare($assetsQuery);
            $assetsStmt->execute([$asOfDate, $asOfDate, $asOfDate, $asOfDate, $asOfDate, $asOfDate, $asOfDate]);
            $assets = $assetsStmt->fetchAll();
            
            // جلب الخصوم من البيانات الحقيقية
            $liabilitiesQuery = "
                SELECT
                    '2110' as account_code,
                    'الموردين' as account_name,
                    null as parent_id,
                    0 as balance
                UNION ALL
                SELECT
                    '2120' as account_code,
                    'الضرائب المستحقة' as account_name,
                    null as parent_id,
                    COALESCE(SUM(s.tax), 0) as balance
                FROM sales s
                WHERE DATE(s.sale_date) <= ?
                ORDER BY account_code
            ";
            
            $liabilitiesStmt = $pdo->prepare($liabilitiesQuery);
            $liabilitiesStmt->execute([$asOfDate]);
            $liabilities = $liabilitiesStmt->fetchAll();
            
            // جلب حقوق الملكية (افتراضية)
            $equityQuery = "
                SELECT
                    '3100' as account_code,
                    'رأس المال' as account_name,
                    null as parent_id,
                    100000 as balance
                UNION ALL
                SELECT
                    '3200' as account_code,
                    'الأرباح المحتجزة' as account_name,
                    null as parent_id,
                    0 as balance
                ORDER BY account_code
            ";
            
            $equityStmt = $pdo->prepare($equityQuery);
            $equityStmt->execute([$asOfDate]);
            $equity = $equityStmt->fetchAll();
            
            // حساب صافي الدخل للفترة الحالية
            $netIncome = calculateNetIncome($pdo, $asOfDate);
            
            // حساب المجاميع
            $totalAssets = array_sum(array_column($assets, 'balance'));
            $totalLiabilities = array_sum(array_column($liabilities, 'balance'));
            $totalEquity = array_sum(array_column($equity, 'balance')) + $netIncome;
            
            // تصنيف الأصول
            $currentAssets = [];
            $fixedAssets = [];
            $otherAssets = [];
            
            foreach ($assets as $asset) {
                if (strpos($asset['account_code'], '11') === 0) {
                    $currentAssets[] = $asset;
                } elseif (strpos($asset['account_code'], '12') === 0) {
                    $fixedAssets[] = $asset;
                } else {
                    $otherAssets[] = $asset;
                }
            }
            
            // تصنيف الخصوم
            $currentLiabilities = [];
            $longTermLiabilities = [];
            
            foreach ($liabilities as $liability) {
                if (strpos($liability['account_code'], '21') === 0) {
                    $currentLiabilities[] = $liability;
                } else {
                    $longTermLiabilities[] = $liability;
                }
            }
            
            // جلب بيانات إضافية للتحليل
            $analysisData = getBalanceSheetAnalysis($pdo, $asOfDate);
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'assets' => $assets,
                    'liabilities' => $liabilities,
                    'equity' => $equity,
                    'current_assets' => $currentAssets,
                    'fixed_assets' => $fixedAssets,
                    'other_assets' => $otherAssets,
                    'current_liabilities' => $currentLiabilities,
                    'long_term_liabilities' => $longTermLiabilities,
                    'total_assets' => $totalAssets,
                    'total_liabilities' => $totalLiabilities,
                    'total_equity' => $totalEquity,
                    'net_income' => $netIncome,
                    'as_of_date' => $asOfDate,
                    'analysis' => $analysisData
                ]
            ]);
            exit;
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

function calculateNetIncome($pdo, $asOfDate) {
    // حساب صافي الدخل من بداية السنة حتى التاريخ المحدد
    $yearStart = date('Y-01-01', strtotime($asOfDate));

    try {
        // حساب الإيرادات من المبيعات الفعلية
        $revenueStmt = $pdo->prepare("
            SELECT COALESCE(SUM(total), 0) as total_revenue
            FROM sales
            WHERE DATE(sale_date) BETWEEN ? AND ?
        ");
        $revenueStmt->execute([$yearStart, $asOfDate]);
        $totalRevenue = $revenueStmt->fetchColumn() ?: 0;

        // حساب المصروفات
        $totalExpenses = 0;

        // تكلفة البضاعة المباعة
        $cogsStmt = $pdo->prepare("
            SELECT COALESCE(SUM(si.quantity * p.cost_price), 0) as cogs
            FROM sales s
            JOIN sale_items si ON s.id = si.sale_id
            JOIN products p ON si.product_id = p.id
            WHERE DATE(s.sale_date) BETWEEN ? AND ?
            AND p.cost_price > 0
        ");
        $cogsStmt->execute([$yearStart, $asOfDate]);
        $totalExpenses += $cogsStmt->fetchColumn() ?: 0;

        // المصروفات الأخرى
        $expensesStmt = $pdo->prepare("
            SELECT COALESCE(SUM(amount), 0) as expenses
            FROM expenses
            WHERE DATE(expense_date) BETWEEN ? AND ?
        ");
        $expensesStmt->execute([$yearStart, $asOfDate]);
        $totalExpenses += $expensesStmt->fetchColumn() ?: 0;

        // الرواتب
        $payrollStmt = $pdo->prepare("
            SELECT COALESCE(SUM(net_salary), 0) as payroll
            FROM payroll
            WHERE DATE(payment_date) BETWEEN ? AND ?
        ");
        $payrollStmt->execute([$yearStart, $asOfDate]);
        $totalExpenses += $payrollStmt->fetchColumn() ?: 0;

        return $totalRevenue - $totalExpenses;

    } catch (PDOException $e) {
        return 0;
    }
}

function getBalanceSheetAnalysis($pdo, $asOfDate) {
    // حساب النسب المالية والتحليلات
    $analysis = [];
    
    // جلب بيانات المخزون
    $inventoryStmt = $pdo->prepare("
        SELECT SUM(quantity * cost_price) as inventory_value
        FROM products 
        WHERE quantity > 0
    ");
    $inventoryStmt->execute();
    $inventoryValue = $inventoryStmt->fetchColumn() ?: 0;
    
    // جلب بيانات العملاء
    $customersStmt = $pdo->prepare("
        SELECT COUNT(*) as total_customers,
               SUM(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 ELSE 0 END) as customers_with_phone
        FROM customers
    ");
    $customersStmt->execute();
    $customersData = $customersStmt->fetch();
    
    // جلب بيانات المبيعات للشهر الحالي
    $monthStart = date('Y-m-01', strtotime($asOfDate));
    $salesStmt = $pdo->prepare("
        SELECT COUNT(*) as sales_count,
               SUM(total) as sales_amount
        FROM sales 
        WHERE DATE(sale_date) BETWEEN ? AND ?
    ");
    $salesStmt->execute([$monthStart, $asOfDate]);
    $salesData = $salesStmt->fetch();
    
    $analysis = [
        'inventory_value' => $inventoryValue,
        'customers_data' => $customersData,
        'sales_data' => $salesData
    ];
    
    return $analysis;
}

// إعدادات النظام
$currency = getSetting('currency', 'ر.س');
$companyName = getSetting('company_name', 'نظام نقاط البيع');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الميزانية العمومية - <?php echo $companyName; ?></title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .report-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .report-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            text-align: center;
        }
        
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .filter-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .balance-sheet-table {
            border: none;
        }
        
        .balance-sheet-table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
        }
        
        .balance-sheet-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .section-header {
            background: #f8f9fa;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .subsection-header {
            background: #f1f3f4;
            font-weight: 600;
            color: var(--secondary-color);
            font-size: 0.95em;
        }
        
        .total-row {
            background: #e3f2fd;
            font-weight: bold;
            border-top: 2px solid var(--primary-color);
        }
        
        .grand-total-row {
            background: #1976d2;
            color: white;
            font-weight: bold;
            border-top: 3px solid var(--primary-color);
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .balance-check {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
            text-align: center;
        }
        
        .balance-check.balanced {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .balance-check.unbalanced {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            .filter-card, .btn, .chart-container {
                display: none !important;
            }
            .content-card {
                box-shadow: none;
                border: 1px solid #000;
                margin: 0;
                padding: 20px;
            }
            .report-header {
                background: white;
                box-shadow: none;
                border-bottom: 2px solid #000;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <div class="report-container fade-in">
        <!-- Report Header -->
        <div class="report-header">
            <div class="row align-items-center">
                <div class="col-md-2">
                    <?php
                    $companyLogo = getSetting('company_logo');
                    if (!empty($companyLogo) && file_exists($companyLogo)):
                    ?>
                        <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 80px; max-height: 80px; border-radius: 10px;">
                    <?php endif; ?>
                </div>
                <div class="col-md-8">
                    <h2 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h2>
                    <h4 class="text-primary mb-0">الميزانية العمومية</h4>
                    <p class="text-muted mb-0" id="reportDate">كما في تاريخ __</p>
                </div>
                <div class="col-md-2 text-end">
                    <a href="accounting.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-right me-1"></i>العودة
                    </a>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card">
            <form id="balanceSheetForm">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label class="form-label">كما في تاريخ</label>
                        <input type="date" class="form-control" id="asOfDate" name="as_of_date"
                               value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-gradient w-100">
                            <i class="bi bi-building me-1"></i>إنشاء الميزانية
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="printReport()">
                            <i class="bi bi-printer me-1"></i>طباعة
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-success w-100" onclick="exportToExcel()">
                            <i class="bi bi-file-earmark-excel me-1"></i>تصدير Excel
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4" id="summaryCards" style="display: none;">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="totalAssetsCard">0</h4>
                            <p class="mb-0 small">إجمالي الأصول</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-building"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--danger-color), #c0392b);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="totalLiabilitiesCard">0</h4>
                            <p class="mb-0 small">إجمالي الخصوم</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-credit-card"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #2ecc71);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="totalEquityCard">0</h4>
                            <p class="mb-0 small">حقوق الملكية</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-person-check"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0" id="netIncomeCard">0</h4>
                            <p class="mb-0 small">صافي الدخل</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-graph-up"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Balance Check -->
        <div id="balanceCheck" style="display: none;"></div>

        <!-- Charts Row -->
        <div class="row mb-4" id="chartsRow" style="display: none;">
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-pie-chart me-2"></i>توزيع الأصول</h5>
                    <div class="chart-container">
                        <canvas id="assetsDistributionChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-bar-chart me-2"></i>الهيكل المالي</h5>
                    <div class="chart-container">
                        <canvas id="financialStructureChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Balance Sheet Table -->
        <div class="row" id="balanceSheetTables" style="display: none;">
            <!-- Assets Column -->
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-4 text-center">
                        <i class="bi bi-building me-2"></i>الأصول
                    </h5>

                    <div class="table-responsive">
                        <table class="table balance-sheet-table">
                            <thead>
                                <tr>
                                    <th width="60%">البيان</th>
                                    <th width="20%" class="text-center">كود الحساب</th>
                                    <th width="20%" class="text-end">المبلغ</th>
                                </tr>
                            </thead>
                            <tbody id="assetsTableBody">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Liabilities & Equity Column -->
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-4 text-center">
                        <i class="bi bi-credit-card me-2"></i>الخصوم وحقوق الملكية
                    </h5>

                    <div class="table-responsive">
                        <table class="table balance-sheet-table">
                            <thead>
                                <tr>
                                    <th width="60%">البيان</th>
                                    <th width="20%" class="text-center">كود الحساب</th>
                                    <th width="20%" class="text-end">المبلغ</th>
                                </tr>
                            </thead>
                            <tbody id="liabilitiesEquityTableBody">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Analysis -->
        <div class="content-card" id="financialAnalysis" style="display: none;">
            <h5 class="mb-3"><i class="bi bi-calculator me-2"></i>التحليل المالي</h5>
            <div class="row">
                <div class="col-md-4">
                    <h6>نسب السيولة</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>نسبة التداول:</strong></td>
                            <td id="currentRatio">-</td>
                        </tr>
                        <tr>
                            <td><strong>نسبة السيولة السريعة:</strong></td>
                            <td id="quickRatio">-</td>
                        </tr>
                        <tr>
                            <td><strong>نسبة النقدية:</strong></td>
                            <td id="cashRatio">-</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-4">
                    <h6>نسب المديونية</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>نسبة الدين إلى الأصول:</strong></td>
                            <td id="debtToAssetsRatio">-</td>
                        </tr>
                        <tr>
                            <td><strong>نسبة الدين إلى حقوق الملكية:</strong></td>
                            <td id="debtToEquityRatio">-</td>
                        </tr>
                        <tr>
                            <td><strong>نسبة حقوق الملكية:</strong></td>
                            <td id="equityRatio">-</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-4">
                    <h6>معلومات إضافية</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>قيمة المخزون:</strong></td>
                            <td id="inventoryValue">-</td>
                        </tr>
                        <tr>
                            <td><strong>عدد العملاء:</strong></td>
                            <td id="totalCustomers">-</td>
                        </tr>
                        <tr>
                            <td><strong>مبيعات الشهر:</strong></td>
                            <td id="monthlySales">-</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Initial Message -->
        <div class="content-card text-center" id="initialMessage">
            <i class="bi bi-building display-1 text-muted"></i>
            <h5 class="text-muted mt-3">الميزانية العمومية</h5>
            <p class="text-muted">اختر التاريخ واضغط "إنشاء الميزانية" لعرض الميزانية العمومية التفصيلية</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let currency = '<?php echo $currency; ?>';
        let assetsDistributionChart, financialStructureChart;

        $(document).ready(function() {
            $('#balanceSheetForm').on('submit', function(e) {
                e.preventDefault();
                generateBalanceSheet();
            });
        });

        function generateBalanceSheet() {
            const asOfDate = $('#asOfDate').val();

            if (!asOfDate) {
                Swal.fire('خطأ', 'يرجى تحديد التاريخ', 'error');
                return;
            }

            // Show loading
            Swal.fire({
                title: 'جاري إنشاء الميزانية العمومية...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.post('balance_sheet.php', {
                action: 'generate_balance_sheet',
                as_of_date: asOfDate
            })
            .done(function(response) {
                const result = JSON.parse(response);
                if (result.success) {
                    displayBalanceSheet(result.data);
                    Swal.close();
                } else {
                    Swal.fire('خطأ', result.message, 'error');
                }
            })
            .fail(function() {
                Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
            });
        }

        function displayBalanceSheet(data) {
            // Update report date
            const asOfDate = new Date(data.as_of_date).toLocaleDateString('ar-SA');
            $('#reportDate').text(`كما في تاريخ ${asOfDate}`);

            // Update summary cards
            $('#totalAssetsCard').text(formatCurrency(data.total_assets));
            $('#totalLiabilitiesCard').text(formatCurrency(data.total_liabilities));
            $('#totalEquityCard').text(formatCurrency(data.total_equity));
            $('#netIncomeCard').text(formatCurrency(data.net_income));

            // Check balance
            const isBalanced = Math.abs(data.total_assets - (data.total_liabilities + data.total_equity)) < 0.01;
            const balanceCheckHtml = isBalanced
                ? '<div class="balance-check balanced"><i class="bi bi-check-circle me-2"></i>الميزانية متوازنة: الأصول = الخصوم + حقوق الملكية</div>'
                : '<div class="balance-check unbalanced"><i class="bi bi-exclamation-triangle me-2"></i>تحذير: الميزانية غير متوازنة</div>';

            $('#balanceCheck').html(balanceCheckHtml);

            // Build balance sheet tables
            buildAssetsTable(data);
            buildLiabilitiesEquityTable(data);

            // Calculate and display financial ratios
            calculateFinancialRatios(data);

            // Create charts
            createCharts(data);

            // Show all sections
            $('#initialMessage').hide();
            $('#summaryCards, #balanceCheck, #chartsRow, #balanceSheetTables, #financialAnalysis').show();
        }

        function buildAssetsTable(data) {
            let tableBody = '';

            // الأصول المتداولة
            if (data.current_assets.length > 0) {
                tableBody += '<tr class="section-header"><td colspan="3"><strong>الأصول المتداولة</strong></td></tr>';

                let currentAssetsTotal = 0;
                data.current_assets.forEach(asset => {
                    if (asset.balance > 0) {
                        tableBody += `
                            <tr>
                                <td>&nbsp;&nbsp;&nbsp;&nbsp;${asset.account_name}</td>
                                <td class="text-center">${asset.account_code}</td>
                                <td class="text-end">${formatCurrency(asset.balance)}</td>
                            </tr>
                        `;
                        currentAssetsTotal += parseFloat(asset.balance);
                    }
                });

                tableBody += `
                    <tr class="subsection-header">
                        <td><strong>إجمالي الأصول المتداولة</strong></td>
                        <td class="text-center">-</td>
                        <td class="text-end"><strong>${formatCurrency(currentAssetsTotal)}</strong></td>
                    </tr>
                `;
            }

            // الأصول الثابتة
            if (data.fixed_assets.length > 0) {
                tableBody += '<tr class="section-header"><td colspan="3"><strong>الأصول الثابتة</strong></td></tr>';

                let fixedAssetsTotal = 0;
                data.fixed_assets.forEach(asset => {
                    if (asset.balance > 0) {
                        tableBody += `
                            <tr>
                                <td>&nbsp;&nbsp;&nbsp;&nbsp;${asset.account_name}</td>
                                <td class="text-center">${asset.account_code}</td>
                                <td class="text-end">${formatCurrency(asset.balance)}</td>
                            </tr>
                        `;
                        fixedAssetsTotal += parseFloat(asset.balance);
                    }
                });

                tableBody += `
                    <tr class="subsection-header">
                        <td><strong>إجمالي الأصول الثابتة</strong></td>
                        <td class="text-center">-</td>
                        <td class="text-end"><strong>${formatCurrency(fixedAssetsTotal)}</strong></td>
                    </tr>
                `;
            }

            // أصول أخرى
            if (data.other_assets.length > 0) {
                tableBody += '<tr class="section-header"><td colspan="3"><strong>أصول أخرى</strong></td></tr>';

                let otherAssetsTotal = 0;
                data.other_assets.forEach(asset => {
                    if (asset.balance > 0) {
                        tableBody += `
                            <tr>
                                <td>&nbsp;&nbsp;&nbsp;&nbsp;${asset.account_name}</td>
                                <td class="text-center">${asset.account_code}</td>
                                <td class="text-end">${formatCurrency(asset.balance)}</td>
                            </tr>
                        `;
                        otherAssetsTotal += parseFloat(asset.balance);
                    }
                });

                if (otherAssetsTotal > 0) {
                    tableBody += `
                        <tr class="subsection-header">
                            <td><strong>إجمالي الأصول الأخرى</strong></td>
                            <td class="text-center">-</td>
                            <td class="text-end"><strong>${formatCurrency(otherAssetsTotal)}</strong></td>
                        </tr>
                    `;
                }
            }

            // إجمالي الأصول
            tableBody += `
                <tr class="grand-total-row">
                    <td><strong>إجمالي الأصول</strong></td>
                    <td class="text-center">-</td>
                    <td class="text-end"><strong>${formatCurrency(data.total_assets)}</strong></td>
                </tr>
            `;

            $('#assetsTableBody').html(tableBody);
        }

        function buildLiabilitiesEquityTable(data) {
            let tableBody = '';

            // الخصوم المتداولة
            if (data.current_liabilities.length > 0) {
                tableBody += '<tr class="section-header"><td colspan="3"><strong>الخصوم المتداولة</strong></td></tr>';

                let currentLiabilitiesTotal = 0;
                data.current_liabilities.forEach(liability => {
                    if (liability.balance > 0) {
                        tableBody += `
                            <tr>
                                <td>&nbsp;&nbsp;&nbsp;&nbsp;${liability.account_name}</td>
                                <td class="text-center">${liability.account_code}</td>
                                <td class="text-end">${formatCurrency(liability.balance)}</td>
                            </tr>
                        `;
                        currentLiabilitiesTotal += parseFloat(liability.balance);
                    }
                });

                tableBody += `
                    <tr class="subsection-header">
                        <td><strong>إجمالي الخصوم المتداولة</strong></td>
                        <td class="text-center">-</td>
                        <td class="text-end"><strong>${formatCurrency(currentLiabilitiesTotal)}</strong></td>
                    </tr>
                `;
            }

            // الخصوم طويلة الأجل
            if (data.long_term_liabilities.length > 0) {
                tableBody += '<tr class="section-header"><td colspan="3"><strong>الخصوم طويلة الأجل</strong></td></tr>';

                let longTermLiabilitiesTotal = 0;
                data.long_term_liabilities.forEach(liability => {
                    if (liability.balance > 0) {
                        tableBody += `
                            <tr>
                                <td>&nbsp;&nbsp;&nbsp;&nbsp;${liability.account_name}</td>
                                <td class="text-center">${liability.account_code}</td>
                                <td class="text-end">${formatCurrency(liability.balance)}</td>
                            </tr>
                        `;
                        longTermLiabilitiesTotal += parseFloat(liability.balance);
                    }
                });

                if (longTermLiabilitiesTotal > 0) {
                    tableBody += `
                        <tr class="subsection-header">
                            <td><strong>إجمالي الخصوم طويلة الأجل</strong></td>
                            <td class="text-center">-</td>
                            <td class="text-end"><strong>${formatCurrency(longTermLiabilitiesTotal)}</strong></td>
                        </tr>
                    `;
                }
            }

            // إجمالي الخصوم
            tableBody += `
                <tr class="total-row">
                    <td><strong>إجمالي الخصوم</strong></td>
                    <td class="text-center">-</td>
                    <td class="text-end"><strong>${formatCurrency(data.total_liabilities)}</strong></td>
                </tr>
            `;

            // حقوق الملكية
            tableBody += '<tr class="section-header"><td colspan="3"><strong>حقوق الملكية</strong></td></tr>';

            data.equity.forEach(equity => {
                if (equity.balance > 0) {
                    tableBody += `
                        <tr>
                            <td>&nbsp;&nbsp;&nbsp;&nbsp;${equity.account_name}</td>
                            <td class="text-center">${equity.account_code}</td>
                            <td class="text-end">${formatCurrency(equity.balance)}</td>
                        </tr>
                    `;
                }
            });

            // صافي الدخل
            if (data.net_income !== 0) {
                const incomeLabel = data.net_income >= 0 ? 'صافي الدخل (أرباح محتجزة)' : 'صافي الخسارة';
                tableBody += `
                    <tr>
                        <td>&nbsp;&nbsp;&nbsp;&nbsp;${incomeLabel}</td>
                        <td class="text-center">-</td>
                        <td class="text-end">${formatCurrency(data.net_income)}</td>
                    </tr>
                `;
            }

            // إجمالي حقوق الملكية
            tableBody += `
                <tr class="total-row">
                    <td><strong>إجمالي حقوق الملكية</strong></td>
                    <td class="text-center">-</td>
                    <td class="text-end"><strong>${formatCurrency(data.total_equity)}</strong></td>
                </tr>
            `;

            // إجمالي الخصوم وحقوق الملكية
            tableBody += `
                <tr class="grand-total-row">
                    <td><strong>إجمالي الخصوم وحقوق الملكية</strong></td>
                    <td class="text-center">-</td>
                    <td class="text-end"><strong>${formatCurrency(data.total_liabilities + data.total_equity)}</strong></td>
                </tr>
            `;

            $('#liabilitiesEquityTableBody').html(tableBody);
        }

        function calculateFinancialRatios(data) {
            // حساب الأصول والخصوم المتداولة
            const currentAssets = data.current_assets.reduce((sum, asset) => sum + parseFloat(asset.balance), 0);
            const currentLiabilities = data.current_liabilities.reduce((sum, liability) => sum + parseFloat(liability.balance), 0);

            // نسب السيولة
            const currentRatio = currentLiabilities > 0 ? (currentAssets / currentLiabilities).toFixed(2) : 'غير محدد';
            const quickRatio = currentLiabilities > 0 ? ((currentAssets - data.analysis.inventory_value) / currentLiabilities).toFixed(2) : 'غير محدد';

            // البحث عن النقدية
            const cashAssets = data.current_assets.filter(asset =>
                asset.account_code.startsWith('111') // حسابات النقدية
            );
            const totalCash = cashAssets.reduce((sum, asset) => sum + parseFloat(asset.balance), 0);
            const cashRatio = currentLiabilities > 0 ? (totalCash / currentLiabilities).toFixed(2) : 'غير محدد';

            // نسب المديونية
            const debtToAssetsRatio = data.total_assets > 0 ? ((data.total_liabilities / data.total_assets) * 100).toFixed(1) + '%' : 'غير محدد';
            const debtToEquityRatio = data.total_equity > 0 ? (data.total_liabilities / data.total_equity).toFixed(2) : 'غير محدد';
            const equityRatio = data.total_assets > 0 ? ((data.total_equity / data.total_assets) * 100).toFixed(1) + '%' : 'غير محدد';

            // تحديث العرض
            $('#currentRatio').text(currentRatio);
            $('#quickRatio').text(quickRatio);
            $('#cashRatio').text(cashRatio);
            $('#debtToAssetsRatio').text(debtToAssetsRatio);
            $('#debtToEquityRatio').text(debtToEquityRatio);
            $('#equityRatio').text(equityRatio);

            // معلومات إضافية
            $('#inventoryValue').text(formatCurrency(data.analysis.inventory_value));
            $('#totalCustomers').text(data.analysis.customers_data.total_customers || 0);
            $('#monthlySales').text(formatCurrency(data.analysis.sales_data.sales_amount || 0));
        }

        function createCharts(data) {
            // Assets Distribution Chart
            const assetsDistributionCtx = document.getElementById('assetsDistributionChart').getContext('2d');

            if (assetsDistributionChart) {
                assetsDistributionChart.destroy();
            }

            // حساب توزيع الأصول
            const currentAssetsTotal = data.current_assets.reduce((sum, asset) => sum + parseFloat(asset.balance), 0);
            const fixedAssetsTotal = data.fixed_assets.reduce((sum, asset) => sum + parseFloat(asset.balance), 0);
            const otherAssetsTotal = data.other_assets.reduce((sum, asset) => sum + parseFloat(asset.balance), 0);

            const assetsLabels = [];
            const assetsData = [];
            const assetsColors = ['#3498db', '#e74c3c', '#f39c12'];

            if (currentAssetsTotal > 0) {
                assetsLabels.push('الأصول المتداولة');
                assetsData.push(currentAssetsTotal);
            }
            if (fixedAssetsTotal > 0) {
                assetsLabels.push('الأصول الثابتة');
                assetsData.push(fixedAssetsTotal);
            }
            if (otherAssetsTotal > 0) {
                assetsLabels.push('أصول أخرى');
                assetsData.push(otherAssetsTotal);
            }

            assetsDistributionChart = new Chart(assetsDistributionCtx, {
                type: 'doughnut',
                data: {
                    labels: assetsLabels,
                    datasets: [{
                        data: assetsData,
                        backgroundColor: assetsColors.slice(0, assetsLabels.length),
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const percentage = ((context.parsed / data.total_assets) * 100).toFixed(1);
                                    return context.label + ': ' + formatCurrency(context.parsed) + ' (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });

            // Financial Structure Chart
            const financialStructureCtx = document.getElementById('financialStructureChart').getContext('2d');

            if (financialStructureChart) {
                financialStructureChart.destroy();
            }

            financialStructureChart = new Chart(financialStructureCtx, {
                type: 'bar',
                data: {
                    labels: ['الأصول', 'الخصوم', 'حقوق الملكية'],
                    datasets: [{
                        label: 'المبلغ',
                        data: [data.total_assets, data.total_liabilities, data.total_equity],
                        backgroundColor: ['#27ae60', '#e74c3c', '#3498db'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }

        function formatCurrency(amount) {
            return parseFloat(amount || 0).toFixed(2) + ' ' + currency;
        }

        function printReport() {
            window.print();
        }

        function exportToExcel() {
            Swal.fire('معلومة', 'ميزة تصدير Excel قيد التطوير', 'info');
        }
    </script>
</body>
</html>
