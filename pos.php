<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';

// تحديث جداول المبيعات لإضافة الأعمدة المطلوبة
try {
    // التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
    $salesColumns = $pdo->query("SHOW COLUMNS FROM sales")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('discount', $salesColumns)) {
        $pdo->exec("ALTER TABLE sales ADD COLUMN discount DECIMAL(10,2) DEFAULT 0");
    }
    if (!in_array('tax', $salesColumns)) {
        $pdo->exec("ALTER TABLE sales ADD COLUMN tax DECIMAL(10,2) DEFAULT 0");
    }
    if (!in_array('payment_method', $salesColumns)) {
        $pdo->exec("ALTER TABLE sales ADD COLUMN payment_method ENUM('cash', 'card', 'transfer') DEFAULT 'cash'");
    }
    if (!in_array('notes', $salesColumns)) {
        $pdo->exec("ALTER TABLE sales ADD COLUMN notes TEXT");
    }
    
    // التحقق من جدول sale_items
    $itemsColumns = $pdo->query("SHOW COLUMNS FROM sale_items")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('unit_price', $itemsColumns)) {
        $pdo->exec("ALTER TABLE sale_items ADD COLUMN unit_price DECIMAL(10,2)");
    }
    if (!in_array('total_price', $itemsColumns)) {
        $pdo->exec("ALTER TABLE sale_items ADD COLUMN total_price DECIMAL(10,2)");
    }
    
    // تحديث البيانات الموجودة
    $pdo->exec("UPDATE sale_items SET unit_price = price WHERE unit_price IS NULL");
    $pdo->exec("UPDATE sale_items SET total_price = quantity * price WHERE total_price IS NULL");
    
} catch (PDOException $e) {
    // في حالة فشل التحديث، إنشاء الجداول من جديد
    $pdo->exec("CREATE TABLE IF NOT EXISTS sales (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT,
        user_id INT,
        total DECIMAL(10,2),
        discount DECIMAL(10,2) DEFAULT 0,
        tax DECIMAL(10,2) DEFAULT 0,
        payment_method ENUM('cash', 'card', 'transfer') DEFAULT 'cash',
        notes TEXT,
        sale_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id),
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");
    
    $pdo->exec("CREATE TABLE IF NOT EXISTS sale_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sale_id INT,
        product_id INT,
        quantity INT,
        unit_price DECIMAL(10,2),
        total_price DECIMAL(10,2),
        FOREIGN KEY (sale_id) REFERENCES sales(id),
        FOREIGN KEY (product_id) REFERENCES products(id)
    )");
}

// معالجة إتمام البيع
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'complete_sale') {
    try {
        $pdo->beginTransaction();
        
        $items = json_decode($_POST['cart_items'], true);
        $customerId = !empty($_POST['customer_id']) ? intval($_POST['customer_id']) : null;
        $discount = floatval($_POST['discount'] ?? 0);
        $tax = floatval($_POST['tax'] ?? 0);
        $paymentMethod = $_POST['payment_method'] ?? 'cash';
        $notes = trim($_POST['notes'] ?? '');
        
        if (empty($items)) {
            throw new Exception('لا توجد منتجات في السلة');
        }
        
        // حساب الإجمالي
        $subtotal = 0;
        foreach ($items as $item) {
            $subtotal += $item['quantity'] * $item['price'];
        }
        $total = $subtotal - $discount + $tax;
        
        // إدراج البيع
        $saleStmt = $pdo->prepare("INSERT INTO sales (customer_id, user_id, total, discount, tax, payment_method, notes) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $saleStmt->execute([$customerId, $_SESSION['user_id'], $total, $discount, $tax, $paymentMethod, $notes]);
        
        $saleId = $pdo->lastInsertId();
        
        // إدراج عناصر البيع وتحديث المخزون
        $itemStmt = $pdo->prepare("INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
        $stockStmt = $pdo->prepare("UPDATE products SET quantity = quantity - ? WHERE id = ? AND quantity >= ?");
        $movementStmt = $pdo->prepare("INSERT INTO stock_movements (product_id, type, reference_id, quantity, user_id) VALUES (?, 'sale', ?, ?, ?)");
        
        foreach ($items as $item) {
            $quantity = intval($item['quantity']);
            $unitPrice = floatval($item['price']);
            $totalPrice = $quantity * $unitPrice;
            
            // التحقق من توفر المخزون
            $stockCheck = $pdo->prepare("SELECT quantity FROM products WHERE id = ?");
            $stockCheck->execute([$item['id']]);
            $currentStock = $stockCheck->fetchColumn();
            
            if ($currentStock < $quantity) {
                throw new Exception("المخزون غير كافي للمنتج: " . $item['name']);
            }
            
            // إدراج عنصر البيع
            $itemStmt->execute([$saleId, $item['id'], $quantity, $unitPrice, $totalPrice]);
            
            // تحديث المخزون
            $stockStmt->execute([$quantity, $item['id'], $quantity]);
            
            // إضافة حركة مخزون
            $movementStmt->execute([$item['id'], $saleId, -$quantity, $_SESSION['user_id']]);
        }
        
        $pdo->commit();
        
        // إرجاع معرف البيع للطباعة
        echo json_encode([
            'success' => true,
            'sale_id' => $saleId,
            'message' => 'تم إتمام البيع بنجاح'
        ]);
        exit;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
        exit;
    }
}

// جلب المنتجات النشطة
try {
    $productsQuery = "
        SELECT p.id, p.code, p.name, p.selling_price as price, p.quantity, p.image,
               c.name as category_name,
               u.name as unit_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN units u ON p.unit_id = u.id
        WHERE p.status = 1 AND p.quantity > 0
        ORDER BY p.name
    ";
    $products = $pdo->query($productsQuery)->fetchAll();
} catch (PDOException $e) {
    $products = [];
}

// جلب الفئات للفلترة
try {
    $categories = $pdo->query("SELECT * FROM categories ORDER BY name")->fetchAll();
} catch (PDOException $e) {
    $categories = [];
}

// جلب العملاء
try {
    $customers = $pdo->query("SELECT id, name, phone, discount_rate FROM customers WHERE status = 1 ORDER BY name")->fetchAll();
} catch (PDOException $e) {
    $customers = [];
}

// إعدادات النظام
$taxRate = floatval(getSetting('tax_rate', 15));
$currency = getSetting('currency', 'ر.س');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقطة البيع - نظام نقاط البيع</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        .pos-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .pos-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .pos-main {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .products-section {
            flex: 2;
            background: rgba(255,255,255,0.95);
            padding: 20px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }
        
        .cart-section {
            flex: 1;
            background: rgba(255,255,255,0.98);
            border-left: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            backdrop-filter: blur(10px);
        }
        
        .search-bar {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .barcode-input {
            font-size: 1.2rem;
            padding: 12px 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .barcode-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }
        
        .category-filters {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .category-btn {
            padding: 8px 16px;
            border: 2px solid var(--primary-color);
            background: white;
            color: var(--primary-color);
            border-radius: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
            font-weight: 500;
        }
        
        .category-btn.active,
        .category-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }
        
        .product-card.out-of-stock {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .product-card.out-of-stock::after {
            content: 'نفد المخزون';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            background: var(--danger-color);
            color: white;
            padding: 5px 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
        }
        
        .product-name {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--dark-color);
            font-size: 0.95rem;
        }
        
        .product-price {
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--success-color);
        }
        
        .product-stock {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .cart-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .cart-items {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }
        
        .cart-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
        }
        
        .cart-item-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .cart-item-controls {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .quantity-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            font-weight: bold;
        }

        .quantity-btn:hover:not(.disabled) {
            background: var(--secondary-color);
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .quantity-btn:active:not(.disabled) {
            transform: scale(0.95);
        }

        .quantity-btn.disabled {
            background: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .quantity-input-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .quantity-input {
            width: 70px;
            text-align: center;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 8px 5px;
            font-weight: bold;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .quantity-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
            outline: none;
        }

        .quantity-input.text-warning {
            border-color: var(--warning-color);
            background-color: rgba(255, 193, 7, 0.1);
        }

        .quantity-actions {
            display: flex;
            gap: 5px;
            margin-left: 10px;
        }

        .quantity-actions .btn {
            width: 35px;
            height: 35px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
        }

        .stock-info {
            font-size: 0.75rem;
            padding: 5px 8px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 3px solid var(--info-color);
        }
        
        .cart-summary {
            background: white;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .summary-row.total {
            border-top: 2px solid var(--primary-color);
            padding-top: 15px;
            margin-top: 15px;
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .checkout-btn {
            width: 100%;
            padding: 15px;
            font-size: 1.2rem;
            font-weight: bold;
            background: linear-gradient(135deg, var(--success-color), #2ecc71);
            border: none;
            border-radius: 10px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
        }
        
        .checkout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }
        
        .checkout-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .empty-cart {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }
        
        .empty-cart i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            .pos-main {
                flex-direction: column;
            }
            
            .products-section {
                flex: none;
                height: 60vh;
            }
            
            .cart-section {
                flex: none;
                height: 40vh;
                border-left: none;
                border-top: 1px solid #e9ecef;
            }
            
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 10px;
            }
            
            .product-card {
                padding: 10px;
            }
            
            .product-image {
                height: 80px;
                font-size: 2rem;
            }
        }
        
        .touch-friendly {
            min-height: 44px;
            min-width: 44px;
        }
        
        .btn-touch {
            padding: 12px 20px;
            font-size: 1.1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-touch:active {
            transform: scale(0.95);
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <div class="pos-container">
        <!-- Header -->
        <div class="pos-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h4 class="mb-0 me-3">
                        <i class="bi bi-cart me-2"></i>نقطة البيع
                    </h4>
                    <span class="badge bg-light text-dark">
                        <i class="bi bi-person me-1"></i><?php echo $_SESSION['username']; ?>
                    </span>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <span class="badge bg-success" id="current-time"></span>
                    <div class="btn-group">
                        <button type="button" class="btn btn-light btn-sm" onclick="showSalesHistory()">
                            <i class="bi bi-clock-history me-1"></i>السجل
                        </button>
                        <a href="review_sale.php" class="btn btn-light btn-sm" target="_blank">
                            <i class="bi bi-receipt me-1"></i>مراجعة فاتورة
                        </a>
                        <button type="button" class="btn btn-light btn-sm" onclick="showSettings()">
                            <i class="bi bi-gear me-1"></i>الإعدادات
                        </button>
                        <a href="dashboard.php" class="btn btn-warning btn-sm">
                            <i class="bi bi-house me-1"></i>الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="pos-main">
            <!-- Products Section -->
            <div class="products-section">
                <!-- Search Bar -->
                <div class="search-bar">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-upc-scan"></i>
                                </span>
                                <input type="text" class="form-control barcode-input" id="barcodeInput"
                                       placeholder="امسح الباركود أو ابحث عن المنتج..."
                                       autocomplete="off" autofocus>
                                <button class="btn btn-primary" type="button" onclick="searchProduct()">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="customerSelect">
                                <option value="">عميل عادي</option>
                                <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo $customer['id']; ?>"
                                        data-discount="<?php echo $customer['discount_rate']; ?>">
                                    <?php echo htmlspecialchars($customer['name']); ?>
                                    <?php if ($customer['discount_rate'] > 0): ?>
                                        (خصم <?php echo $customer['discount_rate']; ?>%)
                                    <?php endif; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Category Filters -->
                <div class="category-filters">
                    <button class="category-btn active" data-category="all">
                        <i class="bi bi-grid me-1"></i>جميع المنتجات
                    </button>
                    <?php foreach ($categories as $category): ?>
                    <button class="category-btn" data-category="<?php echo $category['id']; ?>">
                        <i class="bi bi-folder me-1"></i><?php echo htmlspecialchars($category['name']); ?>
                    </button>
                    <?php endforeach; ?>
                </div>

                <!-- Products Grid -->
                <div class="products-grid" id="productsGrid">
                    <?php foreach ($products as $product): ?>
                    <div class="product-card <?php echo $product['quantity'] <= 0 ? 'out-of-stock' : ''; ?>"
                         data-product='<?php echo json_encode($product); ?>'
                         data-category="<?php echo $product['category_id'] ?? 'none'; ?>"
                         onclick="<?php echo $product['quantity'] > 0 ? 'addToCart(this, event)' : ''; ?>"
                         title="<?php echo $product['quantity'] > 0 ? 'انقر للإضافة • Ctrl+انقر لتحديد الكمية' : 'نفد المخزون'; ?>">

                        <div class="product-image">
                            <?php if (!empty($product['image']) && file_exists($product['image'])): ?>
                                <img src="<?php echo $product['image']; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     style="width: 100%; height: 100%; object-fit: cover; border-radius: 10px;">
                            <?php else: ?>
                                <i class="bi bi-box"></i>
                            <?php endif; ?>
                        </div>

                        <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>

                        <div class="product-price">
                            <?php echo number_format($product['price'], 2); ?> <?php echo $currency; ?>
                        </div>

                        <div class="product-stock">
                            المخزون: <?php echo $product['quantity']; ?> <?php echo htmlspecialchars($product['unit_name'] ?? ''); ?>
                        </div>

                        <?php if (!empty($product['code'])): ?>
                        <div class="product-stock">
                            <small class="text-muted">كود: <?php echo htmlspecialchars($product['code']); ?></small>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>

                <?php if (empty($products)): ?>
                <div class="text-center py-5">
                    <i class="bi bi-box display-1 text-muted"></i>
                    <h5 class="text-muted mt-3">لا توجد منتجات متاحة</h5>
                    <p class="text-muted">يرجى إضافة منتجات من صفحة إدارة المنتجات</p>
                    <a href="products.php" class="btn btn-primary">
                        <i class="bi bi-plus me-1"></i>إضافة منتجات
                    </a>
                </div>
                <?php endif; ?>
            </div>

            <!-- Cart Section -->
            <div class="cart-section">
                <!-- Cart Header -->
                <div class="cart-header">
                    <h5 class="mb-0">
                        <i class="bi bi-cart me-2"></i>سلة المشتريات
                        <span class="badge bg-light text-dark ms-2" id="cartCount">0</span>
                    </h5>
                </div>

                <!-- Cart Items -->
                <div class="cart-items" id="cartItems">
                    <div class="empty-cart">
                        <i class="bi bi-cart-x"></i>
                        <h6>السلة فارغة</h6>
                        <p class="small text-muted">اختر المنتجات لإضافتها إلى السلة</p>
                    </div>
                </div>

                <!-- Cart Summary -->
                <div class="cart-summary">
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span id="subtotal">0.00 <?php echo $currency; ?></span>
                    </div>
                    <div class="summary-row">
                        <span>الخصم:</span>
                        <div class="d-flex align-items-center">
                            <input type="number" class="form-control form-control-sm me-2"
                                   id="discountInput" placeholder="0" min="0" step="0.01"
                                   style="width: 80px;" onchange="updateTotals()">
                            <span id="discount">0.00 <?php echo $currency; ?></span>
                        </div>
                    </div>
                    <div class="summary-row">
                        <span>الضريبة (<?php echo $taxRate; ?>%):</span>
                        <span id="tax">0.00 <?php echo $currency; ?></span>
                    </div>
                    <div class="summary-row total">
                        <span>الإجمالي:</span>
                        <span id="total">0.00 <?php echo $currency; ?></span>
                    </div>

                    <div class="row g-2 mt-3">
                        <div class="col-6">
                            <button type="button" class="btn btn-outline-danger w-100 btn-touch"
                                    onclick="clearCart()" id="clearBtn" disabled>
                                <i class="bi bi-trash me-1"></i>مسح الكل
                            </button>
                        </div>
                        <div class="col-6">
                            <button type="button" class="btn btn-outline-primary w-100 btn-touch"
                                    onclick="holdSale()" id="holdBtn" disabled>
                                <i class="bi bi-pause me-1"></i>تعليق
                            </button>
                        </div>
                    </div>

                    <button type="button" class="checkout-btn touch-friendly"
                            onclick="showCheckoutModal()" id="checkoutBtn" disabled>
                        <i class="bi bi-credit-card me-2"></i>إتمام البيع
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Checkout Modal -->
    <div class="modal fade" id="checkoutModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-credit-card me-2"></i>إتمام البيع
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="checkoutForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="paymentMethod" name="payment_method">
                                    <option value="cash">نقدي</option>
                                    <option value="card">بطاقة ائتمان</option>
                                    <option value="transfer">تحويل بنكي</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">العميل</label>
                                <select class="form-select" id="checkoutCustomer" name="customer_id">
                                    <option value="">عميل عادي</option>
                                    <?php foreach ($customers as $customer): ?>
                                    <option value="<?php echo $customer['id']; ?>">
                                        <?php echo htmlspecialchars($customer['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="saleNotes" name="notes" rows="2"
                                      placeholder="ملاحظات إضافية (اختياري)"></textarea>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">المبلغ المدفوع</label>
                                <input type="number" class="form-control" id="paidAmount"
                                       placeholder="0.00" step="0.01" min="0">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الباقي</label>
                                <input type="text" class="form-control" id="changeAmount" readonly>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <div class="d-flex justify-content-between">
                                <span>الإجمالي:</span>
                                <strong id="checkoutTotal">0.00 <?php echo $currency; ?></strong>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-success" onclick="completeSale()">
                        <i class="bi bi-check-circle me-1"></i>تأكيد البيع
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Receipt Modal -->
    <div class="modal fade" id="receiptModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-receipt me-2"></i>فاتورة البيع
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="receiptContent">
                    <!-- Receipt content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>إغلاق
                    </button>
                    <button type="button" class="btn btn-primary" onclick="printReceipt()">
                        <i class="bi bi-printer me-1"></i>طباعة
                    </button>
                    <button type="button" class="btn btn-success" onclick="newSale()">
                        <i class="bi bi-plus-circle me-1"></i>بيع جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- QuaggaJS for Barcode Scanning -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/quagga/0.12.1/quagga.min.js"></script>

    <script>
        // Global variables
        let cart = [];
        let currentCustomer = null;
        let taxRate = <?php echo $taxRate; ?>;
        let currency = '<?php echo $currency; ?>';
        let products = <?php echo json_encode($products); ?>;

        $(document).ready(function() {
            // Initialize
            updateTime();
            setInterval(updateTime, 1000);
            updateHeldSalesButton();

            // Barcode input handler
            $('#barcodeInput').on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    searchProduct();
                }
            });

            // Customer selection
            $('#customerSelect').on('change', function() {
                const customerId = $(this).val();
                const customerDiscount = $(this).find('option:selected').data('discount') || 0;

                if (customerId) {
                    currentCustomer = {
                        id: customerId,
                        discount: customerDiscount
                    };
                } else {
                    currentCustomer = null;
                }

                updateTotals();
            });

            // Category filters
            $('.category-btn').on('click', function() {
                $('.category-btn').removeClass('active');
                $(this).addClass('active');

                const category = $(this).data('category');
                filterProducts(category);
            });

            // Paid amount change
            $('#paidAmount').on('input', function() {
                calculateChange();
            });

            // Auto-focus barcode input
            $(document).on('click', function() {
                if (!$('.modal').hasClass('show')) {
                    $('#barcodeInput').focus();
                }
            });
        });

        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            $('#current-time').text(timeString);
        }

        function searchProduct() {
            const query = $('#barcodeInput').val().trim().toLowerCase();
            if (!query) return;

            // Search by code or name
            const product = products.find(p =>
                (p.code && p.code.toLowerCase() === query) ||
                p.name.toLowerCase().includes(query)
            );

            if (product) {
                if (product.quantity > 0) {
                    addProductToCart(product);
                    $('#barcodeInput').val('').focus();
                } else {
                    Swal.fire({
                        icon: 'warning',
                        title: 'نفد المخزون',
                        text: `المنتج "${product.name}" غير متوفر في المخزون`,
                        timer: 2000,
                        showConfirmButton: false
                    });
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'منتج غير موجود',
                    text: 'لم يتم العثور على المنتج المطلوب',
                    timer: 2000,
                    showConfirmButton: false
                });
            }

            $('#barcodeInput').val('');
        }

        function filterProducts(category) {
            $('.product-card').each(function() {
                const productCategory = $(this).data('category');

                if (category === 'all' || productCategory == category || (category === 'none' && !productCategory)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }

        function addToCart(element, event) {
            const productData = $(element).data('product');

            // Check if Ctrl/Cmd key is pressed for quick quantity selection
            if (event && (event.ctrlKey || event.metaKey)) {
                showQuickQuantityDialog(productData);
            } else {
                addProductToCart(productData, 1);
            }
        }

        function addProductToCart(product, quantityToAdd = 1) {
            const existingItem = cart.find(item => item.id === product.id);

            if (existingItem) {
                const newQuantity = existingItem.quantity + quantityToAdd;
                if (newQuantity <= product.quantity) {
                    existingItem.quantity = newQuantity;
                    updateCartDisplay();
                    showQuantityFeedback(cart.indexOf(existingItem), 'increase', quantityToAdd);
                } else {
                    const availableToAdd = product.quantity - existingItem.quantity;
                    if (availableToAdd > 0) {
                        existingItem.quantity = product.quantity;
                        updateCartDisplay();
                        Swal.fire({
                            icon: 'warning',
                            title: 'تم إضافة الكمية المتاحة فقط',
                            text: `تم إضافة ${availableToAdd} وحدة فقط من "${product.name}" (الحد الأقصى المتاح)`,
                            timer: 3000,
                            showConfirmButton: false
                        });
                    } else {
                        showStockLimitWarning(product.name, product.quantity);
                    }
                }
            } else {
                const initialQuantity = Math.min(quantityToAdd, product.quantity);
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: parseFloat(product.price),
                    quantity: initialQuantity,
                    maxQuantity: product.quantity,
                    unit: product.unit_name || ''
                });
                updateCartDisplay();

                if (initialQuantity < quantityToAdd) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'تم إضافة الكمية المتاحة فقط',
                        text: `تم إضافة ${initialQuantity} وحدة فقط من "${product.name}"`,
                        timer: 3000,
                        showConfirmButton: false
                    });
                } else {
                    showQuantityFeedback(cart.length - 1, 'increase', initialQuantity);
                }
            }
        }

        function showQuickQuantityDialog(product) {
            const existingItem = cart.find(item => item.id === product.id);
            const currentQuantity = existingItem ? existingItem.quantity : 0;
            const maxAvailable = product.quantity - currentQuantity;

            if (maxAvailable <= 0) {
                showStockLimitWarning(product.name, product.quantity);
                return;
            }

            Swal.fire({
                title: `إضافة كمية - ${product.name}`,
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <div class="alert alert-info">
                                <strong>في السلة حالياً:</strong> ${currentQuantity}<br>
                                <strong>المتاح للإضافة:</strong> ${maxAvailable}<br>
                                <strong>السعر:</strong> ${product.price} ${currency}
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية المراد إضافتها:</label>
                            <input type="number" class="form-control" id="quickQuantity"
                                   value="1" min="1" max="${maxAvailable}"
                                   placeholder="أدخل الكمية">
                        </div>
                        <div class="mb-3">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('quickQuantity').value = 1">1</button>
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('quickQuantity').value = 5">5</button>
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('quickQuantity').value = 10">10</button>
                                <button type="button" class="btn btn-outline-success" onclick="document.getElementById('quickQuantity').value = ${maxAvailable}">الكل</button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                نصيحة: اضغط Ctrl + النقر للوصول السريع لهذه النافذة
                            </small>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'إضافة',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const quantity = parseInt(document.getElementById('quickQuantity').value);
                    if (isNaN(quantity) || quantity < 1) {
                        Swal.showValidationMessage('يجب أن تكون الكمية رقم صحيح أكبر من صفر');
                        return false;
                    }
                    if (quantity > maxAvailable) {
                        Swal.showValidationMessage(`الكمية لا يمكن أن تتجاوز ${maxAvailable}`);
                        return false;
                    }
                    return quantity;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    addProductToCart(product, result.value);
                }
            });

            // Focus and select the input
            setTimeout(() => {
                const input = document.getElementById('quickQuantity');
                if (input) {
                    input.focus();
                    input.select();
                }
            }, 100);
        }

        function updateCartDisplay() {
            const cartContainer = $('#cartItems');

            if (cart.length === 0) {
                cartContainer.html(`
                    <div class="empty-cart">
                        <i class="bi bi-cart-x"></i>
                        <h6>السلة فارغة</h6>
                        <p class="small text-muted">اختر المنتجات لإضافتها إلى السلة</p>
                    </div>
                `);
                $('#cartCount').text('0');
                $('#clearBtn, #holdBtn, #checkoutBtn').prop('disabled', true);
            } else {
                let cartHtml = '';
                let totalItems = 0;

                cart.forEach((item, index) => {
                    totalItems += item.quantity;
                    const stockWarning = item.quantity >= item.maxQuantity ? 'text-warning' : '';
                    const stockIcon = item.quantity >= item.maxQuantity ? '<i class="bi bi-exclamation-triangle text-warning ms-1" title="الحد الأقصى"></i>' : '';

                    cartHtml += `
                        <div class="cart-item" data-index="${index}">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div class="cart-item-name flex-grow-1">${item.name}</div>
                                <button class="btn btn-sm btn-outline-danger ms-2" onclick="removeFromCart(${index})" title="حذف المنتج">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">${item.price.toFixed(2)} ${currency} × ${item.quantity}</span>
                                <span class="fw-bold text-success">${(item.quantity * item.price).toFixed(2)} ${currency}</span>
                            </div>

                            <div class="cart-item-controls">
                                <div class="quantity-controls">
                                    <button class="quantity-btn ${item.quantity <= 1 ? 'disabled' : ''}"
                                            onclick="decreaseQuantity(${index})"
                                            ${item.quantity <= 1 ? 'disabled' : ''}
                                            title="تقليل الكمية">
                                        <i class="bi bi-dash"></i>
                                    </button>

                                    <div class="quantity-input-container">
                                        <input type="number" class="quantity-input ${stockWarning}"
                                               value="${item.quantity}"
                                               min="1" max="${item.maxQuantity}"
                                               onchange="updateQuantity(${index}, this.value)"
                                               onkeypress="handleQuantityKeypress(event, ${index})"
                                               onfocus="this.select()"
                                               title="الكمية المطلوبة (الحد الأقصى: ${item.maxQuantity})">
                                        ${stockIcon}
                                    </div>

                                    <button class="quantity-btn ${item.quantity >= item.maxQuantity ? 'disabled' : ''}"
                                            onclick="increaseQuantity(${index})"
                                            ${item.quantity >= item.maxQuantity ? 'disabled' : ''}
                                            title="زيادة الكمية">
                                        <i class="bi bi-plus"></i>
                                    </button>
                                </div>

                                <div class="quantity-actions">
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="setQuantityDialog(${index})"
                                            title="تحديد كمية مخصصة">
                                        <i class="bi bi-123"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info"
                                            onclick="addMaxQuantity(${index})"
                                            title="إضافة الحد الأقصى">
                                        <i class="bi bi-arrow-up-square"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="stock-info mt-2">
                                <small class="text-muted">
                                    المتوفر: ${item.maxQuantity} ${item.unit}
                                    ${item.quantity >= item.maxQuantity ?
                                        '<span class="text-warning ms-1">• تم الوصول للحد الأقصى</span>' :
                                        `<span class="text-success ms-1">• يمكن إضافة ${item.maxQuantity - item.quantity} أكثر</span>`
                                    }
                                </small>
                            </div>
                        </div>
                    `;
                });

                cartContainer.html(cartHtml);
                $('#cartCount').text(`${cart.length} (${totalItems})`);
                $('#clearBtn, #holdBtn, #checkoutBtn').prop('disabled', false);
            }

            updateTotals();
        }

        function increaseQuantity(index) {
            if (cart[index].quantity < cart[index].maxQuantity) {
                cart[index].quantity++;
                updateCartDisplay();
                showQuantityFeedback(index, 'increase');
            } else {
                showStockLimitWarning(cart[index].name, cart[index].maxQuantity);
            }
        }

        function decreaseQuantity(index) {
            if (cart[index].quantity > 1) {
                cart[index].quantity--;
                updateCartDisplay();
                showQuantityFeedback(index, 'decrease');
            } else {
                Swal.fire({
                    icon: 'question',
                    title: 'حذف المنتج؟',
                    text: `هل تريد حذف "${cart[index].name}" من السلة؟`,
                    showCancelButton: true,
                    confirmButtonText: 'نعم، احذف',
                    cancelButtonText: 'إلغاء',
                    confirmButtonColor: '#d33'
                }).then((result) => {
                    if (result.isConfirmed) {
                        removeFromCart(index);
                    }
                });
            }
        }

        function updateQuantity(index, newQuantity) {
            const quantity = parseInt(newQuantity);
            const item = cart[index];

            if (isNaN(quantity) || quantity < 1) {
                Swal.fire({
                    icon: 'error',
                    title: 'كمية غير صحيحة',
                    text: 'يجب أن تكون الكمية رقم صحيح أكبر من صفر',
                    timer: 2000,
                    showConfirmButton: false
                });
                updateCartDisplay(); // Reset to previous value
                return;
            }

            if (quantity > item.maxQuantity) {
                showStockLimitWarning(item.name, item.maxQuantity);
                updateCartDisplay(); // Reset to previous value
                return;
            }

            const oldQuantity = item.quantity;
            item.quantity = quantity;
            updateCartDisplay();

            // Show feedback for significant changes
            if (Math.abs(quantity - oldQuantity) > 1) {
                showQuantityFeedback(index, quantity > oldQuantity ? 'increase' : 'decrease', Math.abs(quantity - oldQuantity));
            }
        }

        function handleQuantityKeypress(event, index) {
            // Allow Enter key to confirm quantity
            if (event.which === 13) {
                event.target.blur();
                showQuantityFeedback(index, 'update');
            }

            // Allow only numbers, backspace, delete, arrow keys
            const allowedKeys = [8, 9, 13, 27, 46, 37, 38, 39, 40];
            if (allowedKeys.indexOf(event.which) !== -1 ||
                (event.which >= 48 && event.which <= 57) ||
                (event.which >= 96 && event.which <= 105)) {
                return true;
            }
            event.preventDefault();
            return false;
        }

        function setQuantityDialog(index) {
            const item = cart[index];

            Swal.fire({
                title: `تحديد كمية - ${item.name}`,
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">الكمية الحالية: <strong>${item.quantity}</strong></label>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية الجديدة:</label>
                            <input type="number" class="form-control" id="newQuantity"
                                   value="${item.quantity}" min="1" max="${item.maxQuantity}"
                                   placeholder="أدخل الكمية المطلوبة">
                            <small class="text-muted">الحد الأقصى: ${item.maxQuantity}</small>
                        </div>
                        <div class="mb-3">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('newQuantity').value = 1">1</button>
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('newQuantity').value = 5">5</button>
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('newQuantity').value = 10">10</button>
                                <button type="button" class="btn btn-outline-success" onclick="document.getElementById('newQuantity').value = ${item.maxQuantity}">الكل</button>
                            </div>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'تحديث',
                cancelButtonText: 'إلغاء',
                preConfirm: () => {
                    const newQuantity = parseInt(document.getElementById('newQuantity').value);
                    if (isNaN(newQuantity) || newQuantity < 1) {
                        Swal.showValidationMessage('يجب أن تكون الكمية رقم صحيح أكبر من صفر');
                        return false;
                    }
                    if (newQuantity > item.maxQuantity) {
                        Swal.showValidationMessage(`الكمية لا يمكن أن تتجاوز ${item.maxQuantity}`);
                        return false;
                    }
                    return newQuantity;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    updateQuantity(index, result.value);
                }
            });

            // Focus and select the input
            setTimeout(() => {
                const input = document.getElementById('newQuantity');
                if (input) {
                    input.focus();
                    input.select();
                }
            }, 100);
        }

        function addMaxQuantity(index) {
            const item = cart[index];
            const remainingStock = item.maxQuantity - item.quantity;

            if (remainingStock > 0) {
                Swal.fire({
                    title: 'إضافة الحد الأقصى',
                    text: `هل تريد إضافة ${remainingStock} وحدة أخرى من "${item.name}"؟`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'نعم، أضف',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        item.quantity = item.maxQuantity;
                        updateCartDisplay();
                        showQuantityFeedback(index, 'max');
                    }
                });
            } else {
                Swal.fire({
                    icon: 'info',
                    title: 'تم الوصول للحد الأقصى',
                    text: `تم إضافة الحد الأقصى المتاح من "${item.name}"`,
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        }

        function showQuantityFeedback(index, action, amount = 1) {
            const item = cart[index];
            let message = '';
            let icon = 'success';

            switch (action) {
                case 'increase':
                    message = `تم زيادة ${amount} وحدة`;
                    icon = 'success';
                    break;
                case 'decrease':
                    message = `تم تقليل ${amount} وحدة`;
                    icon = 'info';
                    break;
                case 'update':
                    message = `تم تحديث الكمية إلى ${item.quantity}`;
                    icon = 'success';
                    break;
                case 'max':
                    message = `تم إضافة الحد الأقصى المتاح`;
                    icon = 'success';
                    break;
            }

            // Show toast notification
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 1500,
                timerProgressBar: true
            });

            Toast.fire({
                icon: icon,
                title: message
            });
        }

        function showStockLimitWarning(productName, maxQuantity) {
            Swal.fire({
                icon: 'warning',
                title: 'تجاوز حد المخزون',
                html: `
                    <div class="text-start">
                        <p>لا يمكن إضافة كمية أكثر من المتوفر في المخزون</p>
                        <div class="alert alert-warning">
                            <strong>المنتج:</strong> ${productName}<br>
                            <strong>الحد الأقصى:</strong> ${maxQuantity} وحدة
                        </div>
                    </div>
                `,
                confirmButtonText: 'فهمت'
            });
        }

        function removeFromCart(index) {
            cart.splice(index, 1);
            updateCartDisplay();
        }

        function clearCart() {
            Swal.fire({
                title: 'تأكيد المسح',
                text: 'هل أنت متأكد من مسح جميع المنتجات من السلة؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، امسح الكل',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    cart = [];
                    updateCartDisplay();
                }
            });
        }

        function updateTotals() {
            let subtotal = 0;
            cart.forEach(item => {
                subtotal += item.quantity * item.price;
            });

            let discount = parseFloat($('#discountInput').val()) || 0;

            // Apply customer discount if available
            if (currentCustomer && currentCustomer.discount > 0) {
                discount += (subtotal * currentCustomer.discount / 100);
            }

            const tax = (subtotal - discount) * taxRate / 100;
            const total = subtotal - discount + tax;

            $('#subtotal').text(subtotal.toFixed(2) + ' ' + currency);
            $('#discount').text(discount.toFixed(2) + ' ' + currency);
            $('#tax').text(tax.toFixed(2) + ' ' + currency);
            $('#total').text(total.toFixed(2) + ' ' + currency);
            $('#checkoutTotal').text(total.toFixed(2) + ' ' + currency);
        }

        function showCheckoutModal() {
            if (cart.length === 0) return;

            // Set customer in checkout modal
            if (currentCustomer) {
                $('#checkoutCustomer').val(currentCustomer.id);
            }

            $('#checkoutModal').modal('show');
            $('#paidAmount').focus();
        }

        function calculateChange() {
            const total = parseFloat($('#total').text().replace(currency, '').trim());
            const paid = parseFloat($('#paidAmount').val()) || 0;
            const change = paid - total;

            $('#changeAmount').val(change >= 0 ? change.toFixed(2) + ' ' + currency : 'غير كافي');
        }

        function completeSale() {
            if (cart.length === 0) {
                Swal.fire('خطأ', 'السلة فارغة', 'error');
                return;
            }

            const total = parseFloat($('#total').text().replace(currency, '').trim());
            const paid = parseFloat($('#paidAmount').val()) || 0;

            if (paid < total) {
                Swal.fire('خطأ', 'المبلغ المدفوع غير كافي', 'error');
                return;
            }

            // Prepare sale data
            const saleData = {
                action: 'complete_sale',
                cart_items: JSON.stringify(cart),
                customer_id: $('#checkoutCustomer').val() || null,
                discount: parseFloat($('#discountInput').val()) || 0,
                tax: parseFloat($('#tax').text().replace(currency, '').trim()),
                payment_method: $('#paymentMethod').val(),
                notes: $('#saleNotes').val()
            };

            // Add customer discount if applicable
            if (currentCustomer && currentCustomer.discount > 0) {
                const subtotal = parseFloat($('#subtotal').text().replace(currency, '').trim());
                saleData.discount += (subtotal * currentCustomer.discount / 100);
            }

            // Show loading
            Swal.fire({
                title: 'جاري معالجة البيع...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Submit sale
            $.post('pos.php', saleData)
                .done(function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        $('#checkoutModal').modal('hide');
                        showReceipt(result.sale_id);
                        cart = [];
                        currentCustomer = null;
                        $('#customerSelect').val('');
                        $('#discountInput').val('');
                        updateCartDisplay();

                        Swal.fire({
                            icon: 'success',
                            title: 'تم البيع بنجاح!',
                            text: result.message,
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire('خطأ', result.message, 'error');
                    }
                })
                .fail(function() {
                    Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                });
        }

        function showReceipt(saleId) {
            // Load receipt content via AJAX
            $.get('get_receipt.php', { id: saleId })
                .done(function(response) {
                    $('#receiptContent').html(response);
                    $('#receiptModal').modal('show');
                })
                .fail(function() {
                    Swal.fire('خطأ', 'فشل في تحميل الفاتورة', 'error');
                });
        }

        function printReceipt() {
            const printContent = $('#receiptContent').html();
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>فاتورة البيع</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .receipt { max-width: 300px; margin: 0 auto; }
                        .text-center { text-align: center; }
                        .text-end { text-align: right; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { padding: 5px; text-align: right; }
                        .border-top { border-top: 1px solid #000; }
                        .fw-bold { font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="receipt">${printContent}</div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        function newSale() {
            $('#receiptModal').modal('hide');
            $('#barcodeInput').focus();
        }

        function holdSale() {
            if (cart.length === 0) {
                Swal.fire('تنبيه', 'السلة فارغة، لا يوجد شيء لتعليقه', 'warning');
                return;
            }

            Swal.fire({
                title: 'تعليق البيع',
                input: 'text',
                inputLabel: 'اسم البيع المعلق (اختياري)',
                inputPlaceholder: 'مثال: طلب أحمد، طاولة رقم 5',
                showCancelButton: true,
                confirmButtonText: 'تعليق',
                cancelButtonText: 'إلغاء',
                inputValidator: (value) => {
                    // Allow empty value
                    return null;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const holdName = result.value || `بيع معلق ${new Date().toLocaleTimeString('ar-SA')}`;

                    // Save to localStorage
                    const heldSales = JSON.parse(localStorage.getItem('heldSales') || '[]');
                    const heldSale = {
                        id: Date.now(),
                        name: holdName,
                        cart: [...cart],
                        customer: currentCustomer,
                        discount: parseFloat($('#discountInput').val()) || 0,
                        timestamp: new Date().toISOString()
                    };

                    heldSales.push(heldSale);
                    localStorage.setItem('heldSales', JSON.stringify(heldSales));

                    // Clear current cart
                    cart = [];
                    currentCustomer = null;
                    $('#customerSelect').val('');
                    $('#discountInput').val('');
                    updateCartDisplay();

                    Swal.fire({
                        icon: 'success',
                        title: 'تم تعليق البيع',
                        text: `تم حفظ "${holdName}" بنجاح`,
                        timer: 2000,
                        showConfirmButton: false
                    });

                    updateHeldSalesButton();
                }
            });
        }

        function showHeldSales() {
            const heldSales = JSON.parse(localStorage.getItem('heldSales') || '[]');

            if (heldSales.length === 0) {
                Swal.fire('معلومة', 'لا توجد مبيعات معلقة', 'info');
                return;
            }

            let salesHtml = '<div class="held-sales-list">';
            heldSales.forEach((sale, index) => {
                const itemCount = sale.cart.reduce((sum, item) => sum + item.quantity, 0);
                const total = sale.cart.reduce((sum, item) => sum + (item.quantity * item.price), 0);

                salesHtml += `
                    <div class="held-sale-item" style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">${sale.name}</h6>
                                <small class="text-muted">${new Date(sale.timestamp).toLocaleString('ar-SA')}</small>
                                <div class="mt-2">
                                    <span class="badge bg-primary">${itemCount} منتج</span>
                                    <span class="badge bg-success">${total.toFixed(2)} ${currency}</span>
                                    ${sale.customer ? `<span class="badge bg-info">${sale.customer.id}</span>` : ''}
                                </div>
                            </div>
                            <div class="btn-group-vertical">
                                <button class="btn btn-sm btn-success" onclick="restoreHeldSale(${index})">
                                    <i class="bi bi-arrow-clockwise"></i> استعادة
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteHeldSale(${index})">
                                    <i class="bi bi-trash"></i> حذف
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            salesHtml += '</div>';

            Swal.fire({
                title: 'المبيعات المعلقة',
                html: salesHtml,
                width: 600,
                showCloseButton: true,
                showConfirmButton: false,
                customClass: {
                    popup: 'held-sales-popup'
                }
            });
        }

        function restoreHeldSale(index) {
            const heldSales = JSON.parse(localStorage.getItem('heldSales') || '[]');
            const sale = heldSales[index];

            if (!sale) return;

            // Check if current cart has items
            if (cart.length > 0) {
                Swal.fire({
                    title: 'تحذير',
                    text: 'السلة الحالية تحتوي على منتجات. هل تريد استبدالها بالبيع المعلق؟',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'نعم، استبدل',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        performRestore(sale, index);
                    }
                });
            } else {
                performRestore(sale, index);
            }
        }

        function performRestore(sale, index) {
            // Restore cart
            cart = [...sale.cart];
            currentCustomer = sale.customer;

            // Restore UI
            if (currentCustomer) {
                $('#customerSelect').val(currentCustomer.id);
            }
            $('#discountInput').val(sale.discount || 0);

            updateCartDisplay();

            // Remove from held sales
            const heldSales = JSON.parse(localStorage.getItem('heldSales') || '[]');
            heldSales.splice(index, 1);
            localStorage.setItem('heldSales', JSON.stringify(heldSales));

            updateHeldSalesButton();

            Swal.fire({
                icon: 'success',
                title: 'تم استعادة البيع',
                text: `تم استعادة "${sale.name}" بنجاح`,
                timer: 2000,
                showConfirmButton: false
            });
        }

        function deleteHeldSale(index) {
            const heldSales = JSON.parse(localStorage.getItem('heldSales') || '[]');
            const sale = heldSales[index];

            Swal.fire({
                title: 'تأكيد الحذف',
                text: `هل أنت متأكد من حذف "${sale.name}"؟`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#d33'
            }).then((result) => {
                if (result.isConfirmed) {
                    heldSales.splice(index, 1);
                    localStorage.setItem('heldSales', JSON.stringify(heldSales));
                    updateHeldSalesButton();
                    showHeldSales(); // Refresh the list
                }
            });
        }

        function updateHeldSalesButton() {
            const heldSales = JSON.parse(localStorage.getItem('heldSales') || '[]');
            const holdBtn = $('#holdBtn');

            if (heldSales.length > 0) {
                holdBtn.html(`<i class="bi bi-pause me-1"></i>تعليق (${heldSales.length})`);

                // Add held sales button if not exists
                if ($('#heldSalesBtn').length === 0) {
                    holdBtn.after(`
                        <button type="button" class="btn btn-outline-info w-100 btn-touch mt-2"
                                onclick="showHeldSales()" id="heldSalesBtn">
                            <i class="bi bi-list me-1"></i>المعلقة (${heldSales.length})
                        </button>
                    `);
                } else {
                    $('#heldSalesBtn').html(`<i class="bi bi-list me-1"></i>المعلقة (${heldSales.length})`);
                }
            } else {
                holdBtn.html('<i class="bi bi-pause me-1"></i>تعليق');
                $('#heldSalesBtn').remove();
            }
        }

        function showSalesHistory() {
            window.open('sales.php', '_blank');
        }

        function showSettings() {
            window.open('settings.php', '_blank');
        }

        // Keyboard shortcuts
        $(document).on('keydown', function(e) {
            if (!$('.modal').hasClass('show')) {
                switch(e.which) {
                    case 113: // F2
                        e.preventDefault();
                        showCheckoutModal();
                        break;
                    case 115: // F4
                        e.preventDefault();
                        clearCart();
                        break;
                    case 27: // Escape
                        e.preventDefault();
                        $('#barcodeInput').focus();
                        break;
                }
            }
        });
    </script>
</body>
</html>
