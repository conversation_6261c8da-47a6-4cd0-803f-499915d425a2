<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';

// تحديث جداول المبيعات لإضافة الأعمدة المطلوبة
try {
    // التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
    $salesColumns = $pdo->query("SHOW COLUMNS FROM sales")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('discount', $salesColumns)) {
        $pdo->exec("ALTER TABLE sales ADD COLUMN discount DECIMAL(10,2) DEFAULT 0");
    }
    if (!in_array('tax', $salesColumns)) {
        $pdo->exec("ALTER TABLE sales ADD COLUMN tax DECIMAL(10,2) DEFAULT 0");
    }
    if (!in_array('payment_method', $salesColumns)) {
        $pdo->exec("ALTER TABLE sales ADD COLUMN payment_method ENUM('cash', 'card', 'transfer') DEFAULT 'cash'");
    }
    if (!in_array('notes', $salesColumns)) {
        $pdo->exec("ALTER TABLE sales ADD COLUMN notes TEXT");
    }
    
    // التحقق من جدول sale_items
    $itemsColumns = $pdo->query("SHOW COLUMNS FROM sale_items")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('unit_price', $itemsColumns)) {
        $pdo->exec("ALTER TABLE sale_items ADD COLUMN unit_price DECIMAL(10,2)");
    }
    if (!in_array('total_price', $itemsColumns)) {
        $pdo->exec("ALTER TABLE sale_items ADD COLUMN total_price DECIMAL(10,2)");
    }
    
    // تحديث البيانات الموجودة
    $pdo->exec("UPDATE sale_items SET unit_price = price WHERE unit_price IS NULL");
    $pdo->exec("UPDATE sale_items SET total_price = quantity * price WHERE total_price IS NULL");
    
} catch (PDOException $e) {
    // في حالة فشل التحديث، إنشاء الجداول من جديد
    $pdo->exec("CREATE TABLE IF NOT EXISTS sales (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT,
        user_id INT,
        total DECIMAL(10,2),
        discount DECIMAL(10,2) DEFAULT 0,
        tax DECIMAL(10,2) DEFAULT 0,
        payment_method ENUM('cash', 'card', 'transfer') DEFAULT 'cash',
        notes TEXT,
        sale_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id),
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");
    
    $pdo->exec("CREATE TABLE IF NOT EXISTS sale_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sale_id INT,
        product_id INT,
        quantity INT,
        unit_price DECIMAL(10,2),
        total_price DECIMAL(10,2),
        FOREIGN KEY (sale_id) REFERENCES sales(id),
        FOREIGN KEY (product_id) REFERENCES products(id)
    )");
}

// معالجة إتمام البيع
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'complete_sale') {
    try {
        $pdo->beginTransaction();
        
        $items = json_decode($_POST['cart_items'], true);
        $customerId = !empty($_POST['customer_id']) ? intval($_POST['customer_id']) : null;
        $discount = floatval($_POST['discount'] ?? 0);
        $tax = floatval($_POST['tax'] ?? 0);
        $paymentMethod = $_POST['payment_method'] ?? 'cash';
        $notes = trim($_POST['notes'] ?? '');
        
        if (empty($items)) {
            throw new Exception('لا توجد منتجات في السلة');
        }
        
        // حساب الإجمالي
        $subtotal = 0;
        foreach ($items as $item) {
            $subtotal += $item['quantity'] * $item['price'];
        }
        $total = $subtotal - $discount + $tax;
        
        // إدراج البيع
        $saleStmt = $pdo->prepare("INSERT INTO sales (customer_id, user_id, total, discount, tax, payment_method, notes) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $saleStmt->execute([$customerId, $_SESSION['user_id'], $total, $discount, $tax, $paymentMethod, $notes]);
        
        $saleId = $pdo->lastInsertId();
        
        // إدراج عناصر البيع وتحديث المخزون
        $itemStmt = $pdo->prepare("INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
        $stockStmt = $pdo->prepare("UPDATE products SET quantity = quantity - ? WHERE id = ? AND quantity >= ?");
        $movementStmt = $pdo->prepare("INSERT INTO stock_movements (product_id, type, reference_id, quantity, user_id) VALUES (?, 'sale', ?, ?, ?)");
        
        foreach ($items as $item) {
            $quantity = intval($item['quantity']);
            $unitPrice = floatval($item['price']);
            $totalPrice = $quantity * $unitPrice;
            
            // التحقق من توفر المخزون
            $stockCheck = $pdo->prepare("SELECT quantity FROM products WHERE id = ?");
            $stockCheck->execute([$item['id']]);
            $currentStock = $stockCheck->fetchColumn();
            
            if ($currentStock < $quantity) {
                throw new Exception("المخزون غير كافي للمنتج: " . $item['name']);
            }
            
            // إدراج عنصر البيع
            $itemStmt->execute([$saleId, $item['id'], $quantity, $unitPrice, $totalPrice]);
            
            // تحديث المخزون
            $stockStmt->execute([$quantity, $item['id'], $quantity]);
            
            // إضافة حركة مخزون
            $movementStmt->execute([$item['id'], $saleId, -$quantity, $_SESSION['user_id']]);
        }
        
        $pdo->commit();
        
        // إرجاع معرف البيع للطباعة
        echo json_encode([
            'success' => true,
            'sale_id' => $saleId,
            'message' => 'تم إتمام البيع بنجاح'
        ]);
        exit;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
        exit;
    }
}

// جلب المنتجات النشطة
try {
    $productsQuery = "
        SELECT p.id, p.code, p.name, p.selling_price as price, p.quantity, p.image,
               c.name as category_name,
               u.name as unit_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN units u ON p.unit_id = u.id
        WHERE p.status = 1 AND p.quantity > 0
        ORDER BY p.name
    ";
    $products = $pdo->query($productsQuery)->fetchAll();
} catch (PDOException $e) {
    $products = [];
}

// جلب الفئات للفلترة
try {
    $categories = $pdo->query("SELECT * FROM categories ORDER BY name")->fetchAll();
} catch (PDOException $e) {
    $categories = [];
}

// جلب العملاء
try {
    $customers = $pdo->query("SELECT id, name, phone, discount_rate FROM customers WHERE status = 1 ORDER BY name")->fetchAll();
} catch (PDOException $e) {
    $customers = [];
}

// إعدادات النظام
$taxRate = floatval(getSetting('tax_rate', 15));
$currency = getSetting('currency', 'ر.س');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقطة البيع - نظام نقاط البيع</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        .pos-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .pos-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .pos-main {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .products-section {
            flex: 2;
            background: rgba(255,255,255,0.95);
            padding: 20px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }
        
        .cart-section {
            flex: 1;
            background: rgba(255,255,255,0.98);
            border-left: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            backdrop-filter: blur(10px);
        }
        
        .search-bar {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .barcode-input {
            font-size: 1.2rem;
            padding: 12px 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .barcode-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }
        
        .category-filters {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .category-btn {
            padding: 8px 16px;
            border: 2px solid var(--primary-color);
            background: white;
            color: var(--primary-color);
            border-radius: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
            font-weight: 500;
        }
        
        .category-btn.active,
        .category-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }
        
        .product-card.out-of-stock {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .product-card.out-of-stock::after {
            content: 'نفد المخزون';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            background: var(--danger-color);
            color: white;
            padding: 5px 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
        }
        
        .product-name {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--dark-color);
            font-size: 0.95rem;
        }
        
        .product-price {
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--success-color);
        }
        
        .product-stock {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .cart-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .cart-items {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }
        
        .cart-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
        }
        
        .cart-item-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .cart-item-controls {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .quantity-btn {
            width: 35px;
            height: 35px;
            border: none;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .quantity-btn:hover {
            background: var(--secondary-color);
            transform: scale(1.1);
        }
        
        .quantity-input {
            width: 60px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 5px;
        }
        
        .cart-summary {
            background: white;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .summary-row.total {
            border-top: 2px solid var(--primary-color);
            padding-top: 15px;
            margin-top: 15px;
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .checkout-btn {
            width: 100%;
            padding: 15px;
            font-size: 1.2rem;
            font-weight: bold;
            background: linear-gradient(135deg, var(--success-color), #2ecc71);
            border: none;
            border-radius: 10px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
        }
        
        .checkout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }
        
        .checkout-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .empty-cart {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }
        
        .empty-cart i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            .pos-main {
                flex-direction: column;
            }
            
            .products-section {
                flex: none;
                height: 60vh;
            }
            
            .cart-section {
                flex: none;
                height: 40vh;
                border-left: none;
                border-top: 1px solid #e9ecef;
            }
            
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 10px;
            }
            
            .product-card {
                padding: 10px;
            }
            
            .product-image {
                height: 80px;
                font-size: 2rem;
            }
        }
        
        .touch-friendly {
            min-height: 44px;
            min-width: 44px;
        }
        
        .btn-touch {
            padding: 12px 20px;
            font-size: 1.1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-touch:active {
            transform: scale(0.95);
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <div class="pos-container">
        <!-- Header -->
        <div class="pos-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h4 class="mb-0 me-3">
                        <i class="bi bi-cart me-2"></i>نقطة البيع
                    </h4>
                    <span class="badge bg-light text-dark">
                        <i class="bi bi-person me-1"></i><?php echo $_SESSION['username']; ?>
                    </span>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <span class="badge bg-success" id="current-time"></span>
                    <div class="btn-group">
                        <button type="button" class="btn btn-light btn-sm" onclick="showSalesHistory()">
                            <i class="bi bi-clock-history me-1"></i>السجل
                        </button>
                        <button type="button" class="btn btn-light btn-sm" onclick="showSettings()">
                            <i class="bi bi-gear me-1"></i>الإعدادات
                        </button>
                        <a href="dashboard.php" class="btn btn-warning btn-sm">
                            <i class="bi bi-house me-1"></i>الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="pos-main">
            <!-- Products Section -->
            <div class="products-section">
                <!-- Search Bar -->
                <div class="search-bar">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-upc-scan"></i>
                                </span>
                                <input type="text" class="form-control barcode-input" id="barcodeInput"
                                       placeholder="امسح الباركود أو ابحث عن المنتج..."
                                       autocomplete="off" autofocus>
                                <button class="btn btn-primary" type="button" onclick="searchProduct()">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="customerSelect">
                                <option value="">عميل عادي</option>
                                <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo $customer['id']; ?>"
                                        data-discount="<?php echo $customer['discount_rate']; ?>">
                                    <?php echo htmlspecialchars($customer['name']); ?>
                                    <?php if ($customer['discount_rate'] > 0): ?>
                                        (خصم <?php echo $customer['discount_rate']; ?>%)
                                    <?php endif; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Category Filters -->
                <div class="category-filters">
                    <button class="category-btn active" data-category="all">
                        <i class="bi bi-grid me-1"></i>جميع المنتجات
                    </button>
                    <?php foreach ($categories as $category): ?>
                    <button class="category-btn" data-category="<?php echo $category['id']; ?>">
                        <i class="bi bi-folder me-1"></i><?php echo htmlspecialchars($category['name']); ?>
                    </button>
                    <?php endforeach; ?>
                </div>

                <!-- Products Grid -->
                <div class="products-grid" id="productsGrid">
                    <?php foreach ($products as $product): ?>
                    <div class="product-card <?php echo $product['quantity'] <= 0 ? 'out-of-stock' : ''; ?>"
                         data-product='<?php echo json_encode($product); ?>'
                         data-category="<?php echo $product['category_id'] ?? 'none'; ?>"
                         onclick="<?php echo $product['quantity'] > 0 ? 'addToCart(this)' : ''; ?>">

                        <div class="product-image">
                            <?php if (!empty($product['image']) && file_exists($product['image'])): ?>
                                <img src="<?php echo $product['image']; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     style="width: 100%; height: 100%; object-fit: cover; border-radius: 10px;">
                            <?php else: ?>
                                <i class="bi bi-box"></i>
                            <?php endif; ?>
                        </div>

                        <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>

                        <div class="product-price">
                            <?php echo number_format($product['price'], 2); ?> <?php echo $currency; ?>
                        </div>

                        <div class="product-stock">
                            المخزون: <?php echo $product['quantity']; ?> <?php echo htmlspecialchars($product['unit_name'] ?? ''); ?>
                        </div>

                        <?php if (!empty($product['code'])): ?>
                        <div class="product-stock">
                            <small class="text-muted">كود: <?php echo htmlspecialchars($product['code']); ?></small>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>

                <?php if (empty($products)): ?>
                <div class="text-center py-5">
                    <i class="bi bi-box display-1 text-muted"></i>
                    <h5 class="text-muted mt-3">لا توجد منتجات متاحة</h5>
                    <p class="text-muted">يرجى إضافة منتجات من صفحة إدارة المنتجات</p>
                    <a href="products.php" class="btn btn-primary">
                        <i class="bi bi-plus me-1"></i>إضافة منتجات
                    </a>
                </div>
                <?php endif; ?>
            </div>

            <!-- Cart Section -->
            <div class="cart-section">
                <!-- Cart Header -->
                <div class="cart-header">
                    <h5 class="mb-0">
                        <i class="bi bi-cart me-2"></i>سلة المشتريات
                        <span class="badge bg-light text-dark ms-2" id="cartCount">0</span>
                    </h5>
                </div>

                <!-- Cart Items -->
                <div class="cart-items" id="cartItems">
                    <div class="empty-cart">
                        <i class="bi bi-cart-x"></i>
                        <h6>السلة فارغة</h6>
                        <p class="small text-muted">اختر المنتجات لإضافتها إلى السلة</p>
                    </div>
                </div>

                <!-- Cart Summary -->
                <div class="cart-summary">
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span id="subtotal">0.00 <?php echo $currency; ?></span>
                    </div>
                    <div class="summary-row">
                        <span>الخصم:</span>
                        <div class="d-flex align-items-center">
                            <input type="number" class="form-control form-control-sm me-2"
                                   id="discountInput" placeholder="0" min="0" step="0.01"
                                   style="width: 80px;" onchange="updateTotals()">
                            <span id="discount">0.00 <?php echo $currency; ?></span>
                        </div>
                    </div>
                    <div class="summary-row">
                        <span>الضريبة (<?php echo $taxRate; ?>%):</span>
                        <span id="tax">0.00 <?php echo $currency; ?></span>
                    </div>
                    <div class="summary-row total">
                        <span>الإجمالي:</span>
                        <span id="total">0.00 <?php echo $currency; ?></span>
                    </div>

                    <div class="row g-2 mt-3">
                        <div class="col-6">
                            <button type="button" class="btn btn-outline-danger w-100 btn-touch"
                                    onclick="clearCart()" id="clearBtn" disabled>
                                <i class="bi bi-trash me-1"></i>مسح الكل
                            </button>
                        </div>
                        <div class="col-6">
                            <button type="button" class="btn btn-outline-primary w-100 btn-touch"
                                    onclick="holdSale()" id="holdBtn" disabled>
                                <i class="bi bi-pause me-1"></i>تعليق
                            </button>
                        </div>
                    </div>

                    <button type="button" class="checkout-btn touch-friendly"
                            onclick="showCheckoutModal()" id="checkoutBtn" disabled>
                        <i class="bi bi-credit-card me-2"></i>إتمام البيع
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Checkout Modal -->
    <div class="modal fade" id="checkoutModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-credit-card me-2"></i>إتمام البيع
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="checkoutForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="paymentMethod" name="payment_method">
                                    <option value="cash">نقدي</option>
                                    <option value="card">بطاقة ائتمان</option>
                                    <option value="transfer">تحويل بنكي</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">العميل</label>
                                <select class="form-select" id="checkoutCustomer" name="customer_id">
                                    <option value="">عميل عادي</option>
                                    <?php foreach ($customers as $customer): ?>
                                    <option value="<?php echo $customer['id']; ?>">
                                        <?php echo htmlspecialchars($customer['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="saleNotes" name="notes" rows="2"
                                      placeholder="ملاحظات إضافية (اختياري)"></textarea>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">المبلغ المدفوع</label>
                                <input type="number" class="form-control" id="paidAmount"
                                       placeholder="0.00" step="0.01" min="0">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الباقي</label>
                                <input type="text" class="form-control" id="changeAmount" readonly>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <div class="d-flex justify-content-between">
                                <span>الإجمالي:</span>
                                <strong id="checkoutTotal">0.00 <?php echo $currency; ?></strong>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-success" onclick="completeSale()">
                        <i class="bi bi-check-circle me-1"></i>تأكيد البيع
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Receipt Modal -->
    <div class="modal fade" id="receiptModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-receipt me-2"></i>فاتورة البيع
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="receiptContent">
                    <!-- Receipt content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>إغلاق
                    </button>
                    <button type="button" class="btn btn-primary" onclick="printReceipt()">
                        <i class="bi bi-printer me-1"></i>طباعة
                    </button>
                    <button type="button" class="btn btn-success" onclick="newSale()">
                        <i class="bi bi-plus-circle me-1"></i>بيع جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- QuaggaJS for Barcode Scanning -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/quagga/0.12.1/quagga.min.js"></script>

    <script>
        // Global variables
        let cart = [];
        let currentCustomer = null;
        let taxRate = <?php echo $taxRate; ?>;
        let currency = '<?php echo $currency; ?>';
        let products = <?php echo json_encode($products); ?>;

        $(document).ready(function() {
            // Initialize
            updateTime();
            setInterval(updateTime, 1000);

            // Barcode input handler
            $('#barcodeInput').on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    searchProduct();
                }
            });

            // Customer selection
            $('#customerSelect').on('change', function() {
                const customerId = $(this).val();
                const customerDiscount = $(this).find('option:selected').data('discount') || 0;

                if (customerId) {
                    currentCustomer = {
                        id: customerId,
                        discount: customerDiscount
                    };
                } else {
                    currentCustomer = null;
                }

                updateTotals();
            });

            // Category filters
            $('.category-btn').on('click', function() {
                $('.category-btn').removeClass('active');
                $(this).addClass('active');

                const category = $(this).data('category');
                filterProducts(category);
            });

            // Paid amount change
            $('#paidAmount').on('input', function() {
                calculateChange();
            });

            // Auto-focus barcode input
            $(document).on('click', function() {
                if (!$('.modal').hasClass('show')) {
                    $('#barcodeInput').focus();
                }
            });
        });

        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            $('#current-time').text(timeString);
        }

        function searchProduct() {
            const query = $('#barcodeInput').val().trim().toLowerCase();
            if (!query) return;

            // Search by code or name
            const product = products.find(p =>
                (p.code && p.code.toLowerCase() === query) ||
                p.name.toLowerCase().includes(query)
            );

            if (product) {
                if (product.quantity > 0) {
                    addProductToCart(product);
                    $('#barcodeInput').val('').focus();
                } else {
                    Swal.fire({
                        icon: 'warning',
                        title: 'نفد المخزون',
                        text: `المنتج "${product.name}" غير متوفر في المخزون`,
                        timer: 2000,
                        showConfirmButton: false
                    });
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'منتج غير موجود',
                    text: 'لم يتم العثور على المنتج المطلوب',
                    timer: 2000,
                    showConfirmButton: false
                });
            }

            $('#barcodeInput').val('');
        }

        function filterProducts(category) {
            $('.product-card').each(function() {
                const productCategory = $(this).data('category');

                if (category === 'all' || productCategory == category || (category === 'none' && !productCategory)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }

        function addToCart(element) {
            const productData = $(element).data('product');
            addProductToCart(productData);
        }

        function addProductToCart(product) {
            const existingItem = cart.find(item => item.id === product.id);

            if (existingItem) {
                if (existingItem.quantity < product.quantity) {
                    existingItem.quantity++;
                    updateCartDisplay();
                } else {
                    Swal.fire({
                        icon: 'warning',
                        title: 'تحذير',
                        text: 'لا يمكن إضافة كمية أكثر من المتوفر في المخزون',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: parseFloat(product.price),
                    quantity: 1,
                    maxQuantity: product.quantity,
                    unit: product.unit_name || ''
                });
                updateCartDisplay();
            }
        }

        function updateCartDisplay() {
            const cartContainer = $('#cartItems');

            if (cart.length === 0) {
                cartContainer.html(`
                    <div class="empty-cart">
                        <i class="bi bi-cart-x"></i>
                        <h6>السلة فارغة</h6>
                        <p class="small text-muted">اختر المنتجات لإضافتها إلى السلة</p>
                    </div>
                `);
                $('#cartCount').text('0');
                $('#clearBtn, #holdBtn, #checkoutBtn').prop('disabled', true);
            } else {
                let cartHtml = '';
                cart.forEach((item, index) => {
                    cartHtml += `
                        <div class="cart-item">
                            <div class="cart-item-name">${item.name}</div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">${item.price.toFixed(2)} ${currency}</span>
                                <span class="fw-bold">${(item.quantity * item.price).toFixed(2)} ${currency}</span>
                            </div>
                            <div class="cart-item-controls">
                                <div class="quantity-controls">
                                    <button class="quantity-btn" onclick="decreaseQuantity(${index})">
                                        <i class="bi bi-dash"></i>
                                    </button>
                                    <input type="number" class="quantity-input" value="${item.quantity}"
                                           min="1" max="${item.maxQuantity}"
                                           onchange="updateQuantity(${index}, this.value)">
                                    <button class="quantity-btn" onclick="increaseQuantity(${index})">
                                        <i class="bi bi-plus"></i>
                                    </button>
                                </div>
                                <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${index})">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });

                cartContainer.html(cartHtml);
                $('#cartCount').text(cart.length);
                $('#clearBtn, #holdBtn, #checkoutBtn').prop('disabled', false);
            }

            updateTotals();
        }

        function increaseQuantity(index) {
            if (cart[index].quantity < cart[index].maxQuantity) {
                cart[index].quantity++;
                updateCartDisplay();
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'تحذير',
                    text: 'لا يمكن إضافة كمية أكثر من المتوفر في المخزون',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        }

        function decreaseQuantity(index) {
            if (cart[index].quantity > 1) {
                cart[index].quantity--;
                updateCartDisplay();
            }
        }

        function updateQuantity(index, newQuantity) {
            const quantity = parseInt(newQuantity);
            if (quantity >= 1 && quantity <= cart[index].maxQuantity) {
                cart[index].quantity = quantity;
                updateCartDisplay();
            } else {
                updateCartDisplay(); // Reset to previous value
            }
        }

        function removeFromCart(index) {
            cart.splice(index, 1);
            updateCartDisplay();
        }

        function clearCart() {
            Swal.fire({
                title: 'تأكيد المسح',
                text: 'هل أنت متأكد من مسح جميع المنتجات من السلة؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، امسح الكل',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    cart = [];
                    updateCartDisplay();
                }
            });
        }

        function updateTotals() {
            let subtotal = 0;
            cart.forEach(item => {
                subtotal += item.quantity * item.price;
            });

            let discount = parseFloat($('#discountInput').val()) || 0;

            // Apply customer discount if available
            if (currentCustomer && currentCustomer.discount > 0) {
                discount += (subtotal * currentCustomer.discount / 100);
            }

            const tax = (subtotal - discount) * taxRate / 100;
            const total = subtotal - discount + tax;

            $('#subtotal').text(subtotal.toFixed(2) + ' ' + currency);
            $('#discount').text(discount.toFixed(2) + ' ' + currency);
            $('#tax').text(tax.toFixed(2) + ' ' + currency);
            $('#total').text(total.toFixed(2) + ' ' + currency);
            $('#checkoutTotal').text(total.toFixed(2) + ' ' + currency);
        }

        function showCheckoutModal() {
            if (cart.length === 0) return;

            // Set customer in checkout modal
            if (currentCustomer) {
                $('#checkoutCustomer').val(currentCustomer.id);
            }

            $('#checkoutModal').modal('show');
            $('#paidAmount').focus();
        }

        function calculateChange() {
            const total = parseFloat($('#total').text().replace(currency, '').trim());
            const paid = parseFloat($('#paidAmount').val()) || 0;
            const change = paid - total;

            $('#changeAmount').val(change >= 0 ? change.toFixed(2) + ' ' + currency : 'غير كافي');
        }

        function completeSale() {
            if (cart.length === 0) {
                Swal.fire('خطأ', 'السلة فارغة', 'error');
                return;
            }

            const total = parseFloat($('#total').text().replace(currency, '').trim());
            const paid = parseFloat($('#paidAmount').val()) || 0;

            if (paid < total) {
                Swal.fire('خطأ', 'المبلغ المدفوع غير كافي', 'error');
                return;
            }

            // Prepare sale data
            const saleData = {
                action: 'complete_sale',
                cart_items: JSON.stringify(cart),
                customer_id: $('#checkoutCustomer').val() || null,
                discount: parseFloat($('#discountInput').val()) || 0,
                tax: parseFloat($('#tax').text().replace(currency, '').trim()),
                payment_method: $('#paymentMethod').val(),
                notes: $('#saleNotes').val()
            };

            // Add customer discount if applicable
            if (currentCustomer && currentCustomer.discount > 0) {
                const subtotal = parseFloat($('#subtotal').text().replace(currency, '').trim());
                saleData.discount += (subtotal * currentCustomer.discount / 100);
            }

            // Show loading
            Swal.fire({
                title: 'جاري معالجة البيع...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Submit sale
            $.post('pos.php', saleData)
                .done(function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        $('#checkoutModal').modal('hide');
                        showReceipt(result.sale_id);
                        cart = [];
                        currentCustomer = null;
                        $('#customerSelect').val('');
                        $('#discountInput').val('');
                        updateCartDisplay();

                        Swal.fire({
                            icon: 'success',
                            title: 'تم البيع بنجاح!',
                            text: result.message,
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire('خطأ', result.message, 'error');
                    }
                })
                .fail(function() {
                    Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                });
        }

        function showReceipt(saleId) {
            // Load receipt content via AJAX
            $.get('get_receipt.php', { id: saleId })
                .done(function(response) {
                    $('#receiptContent').html(response);
                    $('#receiptModal').modal('show');
                })
                .fail(function() {
                    Swal.fire('خطأ', 'فشل في تحميل الفاتورة', 'error');
                });
        }

        function printReceipt() {
            const printContent = $('#receiptContent').html();
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>فاتورة البيع</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .receipt { max-width: 300px; margin: 0 auto; }
                        .text-center { text-align: center; }
                        .text-end { text-align: right; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { padding: 5px; text-align: right; }
                        .border-top { border-top: 1px solid #000; }
                        .fw-bold { font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="receipt">${printContent}</div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        function newSale() {
            $('#receiptModal').modal('hide');
            $('#barcodeInput').focus();
        }

        function holdSale() {
            // Implementation for holding sale (save to localStorage or server)
            Swal.fire('معلومة', 'ميزة تعليق البيع ستتوفر قريباً', 'info');
        }

        function showSalesHistory() {
            window.open('sales.php', '_blank');
        }

        function showSettings() {
            window.open('settings.php', '_blank');
        }

        // Keyboard shortcuts
        $(document).on('keydown', function(e) {
            if (!$('.modal').hasClass('show')) {
                switch(e.which) {
                    case 113: // F2
                        e.preventDefault();
                        showCheckoutModal();
                        break;
                    case 115: // F4
                        e.preventDefault();
                        clearCart();
                        break;
                    case 27: // Escape
                        e.preventDefault();
                        $('#barcodeInput').focus();
                        break;
                }
            }
        });
    </script>
</body>
</html>
