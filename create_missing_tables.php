<?php
// ملف لإنشاء الجداول المفقودة في قاعدة البيانات
require 'db.php';

try {
    // إنشاء جدول المصروفات إذا لم يكن موجود
    $pdo->exec("CREATE TABLE IF NOT EXISTS expenses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        expense_number VARCHAR(20) UNIQUE NOT NULL,
        expense_date DATE NOT NULL,
        category ENUM('salary', 'rent', 'utilities', 'supplies', 'maintenance', 'marketing', 'other') DEFAULT 'other',
        description TEXT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        payment_method ENUM('cash', 'bank', 'check') DEFAULT 'cash',
        account_id INT,
        journal_entry_id INT,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_date (expense_date),
        INDEX idx_category (category),
        INDEX idx_payment_method (payment_method)
    )");
    
    // إنشاء جدول الرواتب إذا لم يكن موجود
    $pdo->exec("CREATE TABLE IF NOT EXISTS payroll (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_name VARCHAR(100) NOT NULL,
        employee_id VARCHAR(20),
        salary_month DATE NOT NULL,
        basic_salary DECIMAL(10,2) NOT NULL,
        allowances DECIMAL(10,2) DEFAULT 0,
        deductions DECIMAL(10,2) DEFAULT 0,
        net_salary DECIMAL(10,2) NOT NULL,
        payment_date DATE,
        payment_method ENUM('cash', 'bank', 'check') DEFAULT 'bank',
        journal_entry_id INT,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_month (salary_month),
        INDEX idx_employee (employee_id),
        INDEX idx_payment_date (payment_date)
    )");
    
    // إضافة بيانات تجريبية للمصروفات إذا كان الجدول فارغ
    $expensesCount = $pdo->query("SELECT COUNT(*) FROM expenses")->fetchColumn();
    if ($expensesCount == 0) {
        $sampleExpenses = [
            ['EXP20240101001', '2024-01-15', 'rent', 'إيجار المحل - يناير 2024', 5000.00, 'bank', 1],
            ['EXP20240102001', '2024-01-20', 'utilities', 'فاتورة الكهرباء - يناير', 800.00, 'cash', 1],
            ['EXP20240103001', '2024-01-25', 'supplies', 'مستلزمات مكتبية', 300.00, 'cash', 1],
            ['EXP20240104001', '2024-01-30', 'maintenance', 'صيانة المعدات', 1200.00, 'bank', 1],
        ];
        
        $stmt = $pdo->prepare("INSERT INTO expenses (expense_number, expense_date, category, description, amount, payment_method, user_id) VALUES (?, ?, ?, ?, ?, ?, ?)");
        foreach ($sampleExpenses as $expense) {
            $stmt->execute($expense);
        }
    }
    
    // إضافة بيانات تجريبية للرواتب إذا كان الجدول فارغ
    $payrollCount = $pdo->query("SELECT COUNT(*) FROM payroll")->fetchColumn();
    if ($payrollCount == 0) {
        $samplePayroll = [
            ['أحمد محمد', 'EMP001', '2024-01-01', 8000.00, 500.00, 200.00, 8300.00, '2024-01-31', 'bank', 1],
            ['فاطمة علي', 'EMP002', '2024-01-01', 6000.00, 300.00, 150.00, 6150.00, '2024-01-31', 'bank', 1],
            ['محمد سالم', 'EMP003', '2024-01-01', 7000.00, 400.00, 180.00, 7220.00, '2024-01-31', 'bank', 1],
        ];
        
        $stmt = $pdo->prepare("INSERT INTO payroll (employee_name, employee_id, salary_month, basic_salary, allowances, deductions, net_salary, payment_date, payment_method, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($samplePayroll as $payroll) {
            $stmt->execute($payroll);
        }
    }
    
    echo "تم إنشاء الجداول المفقودة وإضافة البيانات التجريبية بنجاح!";
    
} catch (PDOException $e) {
    echo "خطأ في إنشاء الجداول: " . $e->getMessage();
}
?>
