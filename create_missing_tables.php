<?php
// ملف لإنشاء الجداول المفقودة في قاعدة البيانات
require 'db.php';

try {
    // إنشاء جدول المصروفات إذا لم يكن موجود
    $pdo->exec("CREATE TABLE IF NOT EXISTS expenses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        expense_number VARCHAR(20) UNIQUE NOT NULL,
        expense_date DATE NOT NULL,
        category ENUM('salary', 'rent', 'utilities', 'supplies', 'maintenance', 'marketing', 'other') DEFAULT 'other',
        description TEXT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        payment_method ENUM('cash', 'bank', 'check') DEFAULT 'cash',
        account_id INT,
        journal_entry_id INT,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_date (expense_date),
        INDEX idx_category (category),
        INDEX idx_payment_method (payment_method)
    )");
    
    // إنشاء جدول الرواتب إذا لم يكن موجود
    $pdo->exec("CREATE TABLE IF NOT EXISTS payroll (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_name VARCHAR(100) NOT NULL,
        employee_id VARCHAR(20),
        salary_month DATE NOT NULL,
        basic_salary DECIMAL(10,2) NOT NULL,
        allowances DECIMAL(10,2) DEFAULT 0,
        deductions DECIMAL(10,2) DEFAULT 0,
        net_salary DECIMAL(10,2) NOT NULL,
        payment_date DATE,
        payment_method ENUM('cash', 'bank', 'check') DEFAULT 'bank',
        journal_entry_id INT,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_month (salary_month),
        INDEX idx_employee (employee_id),
        INDEX idx_payment_date (payment_date)
    )");
    
    // الجداول جاهزة للاستخدام مع البيانات الحقيقية
    
    echo "تم إنشاء الجداول المفقودة بنجاح! النظام جاهز للعمل مع البيانات الحقيقية.";
    
} catch (PDOException $e) {
    echo "خطأ في إنشاء الجداول: " . $e->getMessage();
}
?>
