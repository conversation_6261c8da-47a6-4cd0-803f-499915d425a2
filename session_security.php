<?php
// ملف حماية الجلسات المحسن
if (session_status() == PHP_SESSION_NONE) {
    // إعدادات الجلسة الآمنة
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 0); // تغيير إلى 1 عند استخدام HTTPS
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', 'Strict');
    ini_set('session.gc_maxlifetime', 3600); // ساعة واحدة
    ini_set('session.cookie_lifetime', 0); // حتى إغلاق المتصفح
    
    session_start();
}

// دالة للتحقق من صحة الجلسة
function validateSession() {
    // التحقق من وجود معرف المستخدم
    if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
        return false;
    }
    
    // التحقق من انتهاء صلاحية الجلسة
    if (isset($_SESSION['last_activity'])) {
        $inactive_time = time() - $_SESSION['last_activity'];
        if ($inactive_time > 3600) { // ساعة واحدة
            return false;
        }
    }
    
    // التحقق من IP Address (اختياري - قد يسبب مشاكل مع بعض الشبكات)
    if (isset($_SESSION['ip_address'])) {
        if ($_SESSION['ip_address'] !== $_SERVER['REMOTE_ADDR']) {
            // يمكن تعطيل هذا التحقق إذا كان يسبب مشاكل
            // return false;
        }
    }
    
    // التحقق من User Agent
    if (isset($_SESSION['user_agent'])) {
        if ($_SESSION['user_agent'] !== $_SERVER['HTTP_USER_AGENT']) {
            return false;
        }
    }
    
    return true;
}

// دالة لتحديث معلومات الجلسة
function updateSessionActivity() {
    $_SESSION['last_activity'] = time();
    
    // تحديث IP Address إذا لم يكن محفوظ
    if (!isset($_SESSION['ip_address'])) {
        $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'];
    }
    
    // تحديث User Agent إذا لم يكن محفوظ
    if (!isset($_SESSION['user_agent'])) {
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
    }
}

// دالة لتسجيل الدخول الآمن
function secureLogin($userId, $username, $pdo = null) {
    // تجديد معرف الجلسة لمنع session fixation
    session_regenerate_id(true);
    
    // حفظ معلومات المستخدم
    $_SESSION['user_id'] = $userId;
    $_SESSION['username'] = $username;
    $_SESSION['login_time'] = time();
    $_SESSION['last_activity'] = time();
    $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'];
    $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
    
    // تسجيل عملية تسجيل الدخول
    if ($pdo) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO user_sessions (user_id, session_id, ip_address, user_agent, login_time) 
                VALUES (?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE 
                session_id = VALUES(session_id),
                ip_address = VALUES(ip_address),
                user_agent = VALUES(user_agent),
                login_time = VALUES(login_time)
            ");
            $stmt->execute([
                $userId, 
                session_id(), 
                $_SERVER['REMOTE_ADDR'], 
                $_SERVER['HTTP_USER_AGENT']
            ]);
        } catch (PDOException $e) {
            // فشل في تسجيل الجلسة - يمكن المتابعة
        }
    }
}

// دالة لتسجيل الخروج الآمن
function secureLogout($pdo = null) {
    $userId = $_SESSION['user_id'] ?? null;
    
    // حذف الجلسة من قاعدة البيانات
    if ($pdo && $userId) {
        try {
            $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE user_id = ? AND session_id = ?");
            $stmt->execute([$userId, session_id()]);
        } catch (PDOException $e) {
            // فشل في حذف الجلسة من قاعدة البيانات
        }
    }
    
    // مسح جميع متغيرات الجلسة
    $_SESSION = array();
    
    // حذف كوكي الجلسة
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // تدمير الجلسة
    session_destroy();
}

// دالة للتحقق من تسجيل الدخول وإعادة التوجيه
function requireLogin($redirectTo = 'login.php') {
    if (!validateSession()) {
        // مسح الجلسة غير الصالحة
        session_unset();
        session_destroy();
        
        // إعادة التوجيه لصفحة تسجيل الدخول
        header("Location: $redirectTo");
        exit;
    }
    
    // تحديث نشاط الجلسة
    updateSessionActivity();
}

// دالة لإنشاء جدول الجلسات
function createUserSessionsTable($pdo) {
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            session_id VARCHAR(128) NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_session (user_id, session_id),
            INDEX idx_user_id (user_id),
            INDEX idx_session_id (session_id),
            INDEX idx_last_activity (last_activity),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )");
    } catch (PDOException $e) {
        // الجدول موجود مسبقاً أو خطأ في الإنشاء
    }
}

// دالة لتنظيف الجلسات المنتهية الصلاحية
function cleanExpiredSessions($pdo) {
    try {
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE last_activity < DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stmt->execute();
    } catch (PDOException $e) {
        // فشل في تنظيف الجلسات المنتهية
    }
}

// دالة للحصول على الجلسات النشطة للمستخدم
function getActiveSessions($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("
            SELECT session_id, ip_address, user_agent, login_time, last_activity 
            FROM user_sessions 
            WHERE user_id = ? AND last_activity > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY last_activity DESC
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

// دالة لإنهاء جلسة معينة
function terminateSession($pdo, $userId, $sessionId) {
    try {
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE user_id = ? AND session_id = ?");
        $stmt->execute([$userId, $sessionId]);
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// دالة لإنهاء جميع الجلسات الأخرى
function terminateOtherSessions($pdo, $userId, $currentSessionId) {
    try {
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE user_id = ? AND session_id != ?");
        $stmt->execute([$userId, $currentSessionId]);
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// دالة للتحقق من محاولات تسجيل الدخول المتعددة
function checkBruteForce($pdo, $ipAddress, $maxAttempts = 5, $timeWindow = 900) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM login_attempts 
            WHERE ip_address = ? AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        $stmt->execute([$ipAddress, $timeWindow]);
        $attempts = $stmt->fetchColumn();
        
        return $attempts >= $maxAttempts;
    } catch (PDOException $e) {
        return false;
    }
}

// دالة لتسجيل محاولة تسجيل دخول فاشلة
function logFailedLogin($pdo, $username, $ipAddress) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO login_attempts (username, ip_address, attempt_time) 
            VALUES (?, ?, NOW())
        ");
        $stmt->execute([$username, $ipAddress]);
    } catch (PDOException $e) {
        // فشل في تسجيل المحاولة
    }
}

// دالة لإنشاء جدول محاولات تسجيل الدخول
function createLoginAttemptsTable($pdo) {
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(100),
            ip_address VARCHAR(45) NOT NULL,
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_ip_time (ip_address, attempt_time),
            INDEX idx_username (username)
        )");
    } catch (PDOException $e) {
        // الجدول موجود مسبقاً أو خطأ في الإنشاء
    }
}

// دالة لتنظيف محاولات تسجيل الدخول القديمة
function cleanOldLoginAttempts($pdo) {
    try {
        $stmt = $pdo->prepare("DELETE FROM login_attempts WHERE attempt_time < DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $stmt->execute();
    } catch (PDOException $e) {
        // فشل في تنظيف المحاولات القديمة
    }
}
?>
