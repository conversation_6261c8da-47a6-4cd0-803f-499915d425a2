<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    exit('غير مصرح');
}

require 'db.php';

try {
    $stmt = $pdo->query("
        SELECT e.*, u.name as user_name
        FROM expenses e
        LEFT JOIN users u ON e.user_id = u.id
        ORDER BY e.expense_date DESC, e.id DESC
    ");
    
    $expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    header('Content-Type: application/json');
    echo json_encode($expenses);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في قاعدة البيانات']);
}
?>
