<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';
require 'check_permissions.php';
require 'apply_permissions.php';
// التحقق من صلاحيات الوصول للصفحة
applyPagePermissions($pdo, $_SESSION['user_id'], 'purchases.php');


// تحديث جداول المشتريات لإضافة الأعمدة المطلوبة
try {
    // التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
    $purchasesColumns = $pdo->query("SHOW COLUMNS FROM purchases")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('invoice_number', $purchasesColumns)) {
        $pdo->exec("ALTER TABLE purchases ADD COLUMN invoice_number VARCHAR(50)");
    }
    if (!in_array('notes', $purchasesColumns)) {
        $pdo->exec("ALTER TABLE purchases ADD COLUMN notes TEXT");
    }
    if (!in_array('status', $purchasesColumns)) {
        $pdo->exec("ALTER TABLE purchases ADD COLUMN status ENUM('pending', 'completed', 'cancelled') DEFAULT 'completed'");
    }
    if (!in_array('payment_status', $purchasesColumns)) {
        $pdo->exec("ALTER TABLE purchases ADD COLUMN payment_status ENUM('paid', 'unpaid', 'partial') DEFAULT 'paid'");
    }
    if (!in_array('discount', $purchasesColumns)) {
        $pdo->exec("ALTER TABLE purchases ADD COLUMN discount DECIMAL(10,2) DEFAULT 0");
    }
    if (!in_array('tax', $purchasesColumns)) {
        $pdo->exec("ALTER TABLE purchases ADD COLUMN tax DECIMAL(10,2) DEFAULT 0");
    }
    
    // تحديث جدول purchase_items
    $itemsColumns = $pdo->query("SHOW COLUMNS FROM purchase_items")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('unit_price', $itemsColumns)) {
        $pdo->exec("ALTER TABLE purchase_items ADD COLUMN unit_price DECIMAL(10,2)");
    }
    if (!in_array('total_price', $itemsColumns)) {
        $pdo->exec("ALTER TABLE purchase_items ADD COLUMN total_price DECIMAL(10,2)");
    }
    
    // تحديث البيانات الموجودة
    $pdo->exec("UPDATE purchase_items SET unit_price = price WHERE unit_price IS NULL");
    $pdo->exec("UPDATE purchase_items SET total_price = price * quantity WHERE total_price IS NULL");
    
} catch (PDOException $e) {
    // في حالة فشل التحديث، إنشاء الجداول من جديد
    $pdo->exec("CREATE TABLE IF NOT EXISTS purchases (
        id INT AUTO_INCREMENT PRIMARY KEY,
        supplier_id INT,
        user_id INT,
        invoice_number VARCHAR(50),
        total DECIMAL(10,2),
        discount DECIMAL(10,2) DEFAULT 0,
        tax DECIMAL(10,2) DEFAULT 0,
        notes TEXT,
        status ENUM('pending', 'completed', 'cancelled') DEFAULT 'completed',
        payment_status ENUM('paid', 'unpaid', 'partial') DEFAULT 'paid',
        purchase_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");
    
    $pdo->exec("CREATE TABLE IF NOT EXISTS purchase_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        purchase_id INT,
        product_id INT,
        quantity INT,
        unit_price DECIMAL(10,2),
        total_price DECIMAL(10,2),
        FOREIGN KEY (purchase_id) REFERENCES purchases(id),
        FOREIGN KEY (product_id) REFERENCES products(id)
    )");
}

// معالجة إضافة/تعديل/حذف المشتريات
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            if ($_POST['action'] === 'add') {
                // التحقق من صحة البيانات
                $errors = [];
                
                if (empty($_POST['supplier_id'])) {
                    $errors[] = 'يجب اختيار المورد';
                }
                
                if (empty($_POST['items']) || !is_array($_POST['items'])) {
                    $errors[] = 'يجب إضافة منتج واحد على الأقل';
                }
                
                if (!empty($errors)) {
                    $message = implode('<br>', $errors);
                    $messageType = 'danger';
                } else {
                    // بدء المعاملة
                    $pdo->beginTransaction();
                    
                    // حساب الإجمالي
                    $subtotal = 0;
                    foreach ($_POST['items'] as $item) {
                        $subtotal += floatval($item['quantity']) * floatval($item['unit_price']);
                    }
                    
                    $discount = floatval($_POST['discount'] ?? 0);
                    $tax = floatval($_POST['tax'] ?? 0);
                    $total = $subtotal - $discount + $tax;
                    
                    // إدراج المشترى
                    $stmt = $pdo->prepare("INSERT INTO purchases (supplier_id, user_id, invoice_number, total, discount, tax, notes, status, payment_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $_POST['supplier_id'],
                        $_SESSION['user_id'],
                        $_POST['invoice_number'] ?? null,
                        $total,
                        $discount,
                        $tax,
                        $_POST['notes'] ?? null,
                        $_POST['status'] ?? 'completed',
                        $_POST['payment_status'] ?? 'paid'
                    ]);
                    
                    $purchaseId = $pdo->lastInsertId();
                    
                    // إدراج عناصر المشترى
                    $itemStmt = $pdo->prepare("INSERT INTO purchase_items (purchase_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
                    
                    foreach ($_POST['items'] as $item) {
                        $quantity = intval($item['quantity']);
                        $unitPrice = floatval($item['unit_price']);
                        $totalPrice = $quantity * $unitPrice;
                        
                        $itemStmt->execute([
                            $purchaseId,
                            $item['product_id'],
                            $quantity,
                            $unitPrice,
                            $totalPrice
                        ]);
                        
                        // تحديث المخزون إذا كانت الحالة مكتملة
                        if (($_POST['status'] ?? 'completed') === 'completed') {
                            $updateStock = $pdo->prepare("UPDATE products SET quantity = quantity + ? WHERE id = ?");
                            $updateStock->execute([$quantity, $item['product_id']]);
                            
                            // إضافة حركة مخزون
                            $stockMovement = $pdo->prepare("INSERT INTO stock_movements (product_id, type, reference_id, quantity) VALUES (?, 'purchase', ?, ?)");
                            $stockMovement->execute([$item['product_id'], $purchaseId, $quantity]);
                        }
                    }
                    
                    $pdo->commit();
                    $message = 'تم إضافة المشترى بنجاح';
                    $messageType = 'success';
                }
            } elseif ($_POST['action'] === 'delete' && isset($_POST['id'])) {
                // التحقق من إمكانية الحذف
                $checkStmt = $pdo->prepare("SELECT status FROM purchases WHERE id = ?");
                $checkStmt->execute([$_POST['id']]);
                $purchase = $checkStmt->fetch();
                
                if ($purchase && $purchase['status'] === 'completed') {
                    $message = 'لا يمكن حذف مشترى مكتمل. يجب إلغاؤه أولاً';
                    $messageType = 'warning';
                } else {
                    $pdo->beginTransaction();
                    
                    // حذف عناصر المشترى
                    $deleteItems = $pdo->prepare("DELETE FROM purchase_items WHERE purchase_id = ?");
                    $deleteItems->execute([$_POST['id']]);
                    
                    // حذف المشترى
                    $deletePurchase = $pdo->prepare("DELETE FROM purchases WHERE id = ?");
                    $deletePurchase->execute([$_POST['id']]);
                    
                    $pdo->commit();
                    $message = 'تم حذف المشترى بنجاح';
                    $messageType = 'success';
                }
            } elseif ($_POST['action'] === 'cancel' && isset($_POST['id'])) {
                $pdo->beginTransaction();
                
                // الحصول على عناصر المشترى لتقليل المخزون
                $itemsStmt = $pdo->prepare("SELECT product_id, quantity FROM purchase_items WHERE purchase_id = ?");
                $itemsStmt->execute([$_POST['id']]);
                $items = $itemsStmt->fetchAll();
                
                // تقليل المخزون
                foreach ($items as $item) {
                    $updateStock = $pdo->prepare("UPDATE products SET quantity = quantity - ? WHERE id = ?");
                    $updateStock->execute([$item['quantity'], $item['product_id']]);
                    
                    // إضافة حركة مخزون سالبة
                    $stockMovement = $pdo->prepare("INSERT INTO stock_movements (product_id, type, reference_id, quantity) VALUES (?, 'purchase_cancel', ?, ?)");
                    $stockMovement->execute([$item['product_id'], $_POST['id'], -$item['quantity']]);
                }
                
                // تحديث حالة المشترى
                $updateStatus = $pdo->prepare("UPDATE purchases SET status = 'cancelled' WHERE id = ?");
                $updateStatus->execute([$_POST['id']]);
                
                $pdo->commit();
                $message = 'تم إلغاء المشترى بنجاح';
                $messageType = 'success';
            }
        }
    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $message = 'حدث خطأ: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// جلب المشتريات مع معلومات إضافية
try {
    $purchasesQuery = "
        SELECT p.*, 
               s.name as supplier_name,
               s.phone as supplier_phone,
               u.name as user_name,
               COUNT(pi.id) as items_count,
               SUM(pi.quantity) as total_quantity
        FROM purchases p 
        LEFT JOIN suppliers s ON p.supplier_id = s.id 
        LEFT JOIN users u ON p.user_id = u.id 
        LEFT JOIN purchase_items pi ON p.id = pi.purchase_id
        GROUP BY p.id 
        ORDER BY p.purchase_date DESC
    ";
    $purchases = $pdo->query($purchasesQuery)->fetchAll();
} catch (PDOException $e) {
    $purchases = [];
}

// جلب الموردين والمنتجات النشطة
try {
    $suppliers = $pdo->query("SELECT * FROM suppliers ORDER BY name")->fetchAll();
} catch (PDOException $e) {
    $suppliers = [];
}

try {
    $products = $pdo->query("SELECT p.*, c.name as category_name, u.name as unit_name FROM products p LEFT JOIN categories c ON p.category_id = c.id LEFT JOIN units u ON p.unit_id = u.id WHERE p.status = 1 ORDER BY p.name")->fetchAll();
} catch (PDOException $e) {
    $products = [];
}

// إحصائيات المشتريات
$totalPurchases = count($purchases);
$completedPurchases = count(array_filter($purchases, function($p) { return $p['status'] === 'completed'; }));
$pendingPurchases = count(array_filter($purchases, function($p) { return $p['status'] === 'pending'; }));
$cancelledPurchases = count(array_filter($purchases, function($p) { return $p['status'] === 'cancelled'; }));

// حساب إجمالي قيمة المشتريات
$totalPurchaseValue = 0;
$totalUnpaidValue = 0;
foreach ($purchases as $purchase) {
    if ($purchase['status'] === 'completed') {
        $totalPurchaseValue += $purchase['total'];
        if ($purchase['payment_status'] === 'unpaid') {
            $totalUnpaidValue += $purchase['total'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشتريات - نظام نقاط البيع</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-left-color: #fff;
            transform: translateX(-5px);
        }
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .page-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .table-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }
        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 20px 20px 0 0;
        }
        .purchase-item-row {
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 10px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }
        .status-badge {
            font-size: 0.8em;
            padding: 4px 8px;
            border-radius: 10px;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            $companyName = getSetting('company_name', 'نظام نقاط البيع');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>
        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link active" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-clipboard-data me-2"></i>إدارة المشتريات</h2>
                    <small class="text-muted">إضافة وتتبع مشتريات المخزون من الموردين</small>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-gradient" data-bs-toggle="modal" data-bs-target="#addPurchaseModal">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة مشترى جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <i class="bi bi-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $totalPurchases; ?></h4>
                            <p class="mb-0 small">إجمالي المشتريات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-clipboard-data"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #2ecc71);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $completedPurchases; ?></h4>
                            <p class="mb-0 small">مشتريات مكتملة</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $pendingPurchases; ?></h4>
                            <p class="mb-0 small">مشتريات معلقة</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalPurchaseValue, false); ?></h4>
                            <p class="mb-0 small">إجمالي القيمة</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Statistics -->
        <div class="row mb-4">
            <div class="col-lg-4 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--danger-color), #c0392b);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $cancelledPurchases; ?></h4>
                            <p class="mb-0 small">مشتريات ملغاة</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-x-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalUnpaidValue, false); ?></h4>
                            <p class="mb-0 small">مستحقات غير مدفوعة</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-credit-card"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, #1abc9c, #16a085);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo count($suppliers); ?></h4>
                            <p class="mb-0 small">عدد الموردين</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-building"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchases Table -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0"><i class="bi bi-table me-2"></i>قائمة المشتريات</h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="exportData()">
                        <i class="bi bi-download me-1"></i>تصدير
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshTable()">
                        <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                    </button>
                </div>
            </div>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="purchasesTable">
                        <thead>
                            <tr>
                                <th width="8%">رقم الفاتورة</th>
                                <th width="15%">المورد</th>
                                <th width="10%">عدد الأصناف</th>
                                <th width="10%">الكمية الإجمالية</th>
                                <th width="12%">المبلغ الإجمالي</th>
                                <th width="10%">حالة الطلب</th>
                                <th width="10%">حالة الدفع</th>
                                <th width="12%">تاريخ المشترى</th>
                                <th width="8%">المستخدم</th>
                                <th width="15%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($purchases as $purchase): ?>
                            <tr>
                                <td>
                                    <?php if (!empty($purchase['invoice_number'])): ?>
                                        <code class="bg-light px-2 py-1 rounded"><?php echo htmlspecialchars($purchase['invoice_number']); ?></code>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($purchase['supplier_name'] ?? 'غير محدد'); ?></strong>
                                        <?php if (!empty($purchase['supplier_phone'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($purchase['supplier_phone']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $purchase['items_count']; ?> صنف</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo $purchase['total_quantity']; ?> قطعة</span>
                                </td>
                                <td>
                                    <strong class="text-primary"><?php echo formatCurrency($purchase['total']); ?></strong>
                                </td>
                                <td>
                                    <?php
                                    $statusClass = 'bg-secondary';
                                    $statusText = $purchase['status'];
                                    $statusIcon = 'question-circle';

                                    switch ($purchase['status']) {
                                        case 'completed':
                                            $statusClass = 'bg-success';
                                            $statusText = 'مكتمل';
                                            $statusIcon = 'check-circle';
                                            break;
                                        case 'pending':
                                            $statusClass = 'bg-warning';
                                            $statusText = 'معلق';
                                            $statusIcon = 'clock';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'bg-danger';
                                            $statusText = 'ملغى';
                                            $statusIcon = 'x-circle';
                                            break;
                                    }
                                    ?>
                                    <span class="status-badge badge <?php echo $statusClass; ?>">
                                        <i class="bi bi-<?php echo $statusIcon; ?> me-1"></i>
                                        <?php echo $statusText; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $paymentClass = 'bg-secondary';
                                    $paymentText = $purchase['payment_status'];
                                    $paymentIcon = 'question-circle';

                                    switch ($purchase['payment_status']) {
                                        case 'paid':
                                            $paymentClass = 'bg-success';
                                            $paymentText = 'مدفوع';
                                            $paymentIcon = 'check-circle';
                                            break;
                                        case 'unpaid':
                                            $paymentClass = 'bg-danger';
                                            $paymentText = 'غير مدفوع';
                                            $paymentIcon = 'x-circle';
                                            break;
                                        case 'partial':
                                            $paymentClass = 'bg-warning';
                                            $paymentText = 'جزئي';
                                            $paymentIcon = 'exclamation-triangle';
                                            break;
                                    }
                                    ?>
                                    <span class="status-badge badge <?php echo $paymentClass; ?>">
                                        <i class="bi bi-<?php echo $paymentIcon; ?> me-1"></i>
                                        <?php echo $paymentText; ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo formatDateTime($purchase['purchase_date']); ?>
                                    </small>
                                </td>
                                <td>
                                    <small><?php echo htmlspecialchars($purchase['user_name'] ?? 'غير محدد'); ?></small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="viewPurchase(<?php echo $purchase['id']; ?>)"
                                                title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <?php if ($purchase['status'] === 'completed'): ?>
                                            <button type="button" class="btn btn-sm btn-outline-warning"
                                                    onclick="cancelPurchase(<?php echo $purchase['id']; ?>)"
                                                    title="إلغاء">
                                                <i class="bi bi-x-circle"></i>
                                            </button>
                                        <?php endif; ?>
                                        <?php if ($purchase['status'] !== 'completed'): ?>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="deletePurchase(<?php echo $purchase['id']; ?>)"
                                                    title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Add Purchase Modal -->
        <div class="modal fade" id="addPurchaseModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-plus-circle me-2"></i>إضافة مشترى جديد</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST" id="addPurchaseForm">
                        <div class="modal-body">
                            <input type="hidden" name="action" value="add">

                            <!-- Purchase Header -->
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <label class="form-label"><i class="bi bi-building me-1"></i>المورد *</label>
                                    <select class="form-select" name="supplier_id" required>
                                        <option value="">اختر المورد</option>
                                        <?php foreach ($suppliers as $supplier): ?>
                                            <option value="<?php echo $supplier['id']; ?>">
                                                <?php echo htmlspecialchars($supplier['name']); ?>
                                                <?php if (!empty($supplier['phone'])): ?>
                                                    - <?php echo htmlspecialchars($supplier['phone']); ?>
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label"><i class="bi bi-receipt me-1"></i>رقم الفاتورة</label>
                                    <input type="text" class="form-control" name="invoice_number" placeholder="رقم الفاتورة (اختياري)">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label"><i class="bi bi-calendar me-1"></i>تاريخ المشترى</label>
                                    <input type="datetime-local" class="form-control" name="purchase_date" value="<?php echo date('Y-m-d\TH:i'); ?>">
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <label class="form-label"><i class="bi bi-check-circle me-1"></i>حالة الطلب</label>
                                    <select class="form-select" name="status">
                                        <option value="completed">مكتمل</option>
                                        <option value="pending">معلق</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label"><i class="bi bi-credit-card me-1"></i>حالة الدفع</label>
                                    <select class="form-select" name="payment_status">
                                        <option value="paid">مدفوع</option>
                                        <option value="unpaid">غير مدفوع</option>
                                        <option value="partial">جزئي</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label"><i class="bi bi-text-paragraph me-1"></i>ملاحظات</label>
                                    <input type="text" class="form-control" name="notes" placeholder="ملاحظات إضافية">
                                </div>
                            </div>

                            <!-- Purchase Items -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6><i class="bi bi-list me-2"></i>أصناف المشترى</h6>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addPurchaseItem()">
                                        <i class="bi bi-plus me-1"></i>إضافة صنف
                                    </button>
                                </div>
                                <div id="purchase-items">
                                    <!-- Purchase items will be added here -->
                                </div>
                            </div>

                            <!-- Purchase Summary -->
                            <div class="row">
                                <div class="col-md-8"></div>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">ملخص المشترى</h6>
                                            <div class="d-flex justify-content-between">
                                                <span>المجموع الفرعي:</span>
                                                <span id="subtotal">0.00</span>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span>الخصم:</span>
                                                <input type="number" class="form-control form-control-sm" name="discount" value="0" step="0.01" onchange="calculateTotal()">
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span>الضريبة:</span>
                                                <input type="number" class="form-control form-control-sm" name="tax" value="0" step="0.01" onchange="calculateTotal()">
                                            </div>
                                            <hr>
                                            <div class="d-flex justify-content-between fw-bold">
                                                <span>الإجمالي:</span>
                                                <span id="total">0.00</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>إلغاء
                            </button>
                            <button type="submit" class="btn btn-gradient">
                                <i class="bi bi-check-circle me-1"></i>حفظ المشترى
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- View Purchase Modal -->
        <div class="modal fade" id="viewPurchaseModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-eye me-2"></i>تفاصيل المشترى</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="purchase-details">
                        <!-- Purchase details will be loaded here -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Buttons -->
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let purchasesTable;
        let itemCounter = 0;
        const products = <?php echo json_encode($products); ?>;

        $(document).ready(function() {
            // Initialize DataTable
            purchasesTable = $('#purchasesTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[7, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [9] }, // Actions column
                    { searchable: false, targets: [9] } // Actions column
                ],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="bi bi-file-earmark-excel me-1"></i>Excel',
                        className: 'btn btn-success btn-sm',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6, 7, 8] // Exclude Actions
                        }
                    }
                ]
            });

            // Form validation
            $('#addPurchaseForm').on('submit', function(e) {
                if (!validatePurchaseForm()) {
                    e.preventDefault();
                    return false;
                }
            });

            // Add first item when modal opens
            $('#addPurchaseModal').on('shown.bs.modal', function() {
                if ($('#purchase-items').children().length === 0) {
                    addPurchaseItem();
                }
            });

            // Clear form when modal is hidden
            $('#addPurchaseModal').on('hidden.bs.modal', function() {
                $('#addPurchaseForm')[0].reset();
                $('#purchase-items').empty();
                itemCounter = 0;
                calculateTotal();
            });
        });

        function validatePurchaseForm() {
            const supplierId = $('select[name="supplier_id"]').val();
            const items = $('#purchase-items .purchase-item-row');

            if (!supplierId) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'يجب اختيار المورد'
                });
                return false;
            }

            if (items.length === 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'يجب إضافة منتج واحد على الأقل'
                });
                return false;
            }

            // Validate each item
            let isValid = true;
            items.each(function() {
                const productId = $(this).find('select[name="items[][product_id]"]').val();
                const quantity = $(this).find('input[name="items[][quantity]"]').val();
                const unitPrice = $(this).find('input[name="items[][unit_price]"]').val();

                if (!productId || !quantity || !unitPrice || quantity <= 0 || unitPrice <= 0) {
                    isValid = false;
                    return false;
                }
            });

            if (!isValid) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في البيانات',
                    text: 'يجب ملء جميع بيانات الأصناف بشكل صحيح'
                });
                return false;
            }

            return true;
        }

        function addPurchaseItem() {
            itemCounter++;
            const itemHtml = `
                <div class="purchase-item-row" id="item-${itemCounter}">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">المنتج</label>
                            <select class="form-select" name="items[${itemCounter}][product_id]" onchange="updateProductInfo(${itemCounter})" required>
                                <option value="">اختر المنتج</option>
                                ${products.map(product => `
                                    <option value="${product.id}" data-cost="${product.cost_price || 0}">
                                        ${product.name} - ${product.category_name || ''} (${product.quantity || 0} متوفر)
                                    </option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الكمية</label>
                            <input type="number" class="form-control" name="items[${itemCounter}][quantity]" min="1" value="1" onchange="calculateItemTotal(${itemCounter})" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">سعر الوحدة</label>
                            <input type="number" class="form-control" name="items[${itemCounter}][unit_price]" step="0.01" min="0" onchange="calculateItemTotal(${itemCounter})" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الإجمالي</label>
                            <input type="text" class="form-control" id="item-total-${itemCounter}" readonly>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-danger btn-sm d-block" onclick="removePurchaseItem(${itemCounter})">
                                <i class="bi bi-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            `;
            $('#purchase-items').append(itemHtml);
        }

        function removePurchaseItem(itemId) {
            $(`#item-${itemId}`).remove();
            calculateTotal();
        }

        function updateProductInfo(itemId) {
            const select = $(`select[name="items[${itemId}][product_id]"]`);
            const selectedOption = select.find('option:selected');
            const costPrice = selectedOption.data('cost') || 0;

            $(`input[name="items[${itemId}][unit_price]"]`).val(costPrice);
            calculateItemTotal(itemId);
        }

        function calculateItemTotal(itemId) {
            const quantity = parseFloat($(`input[name="items[${itemId}][quantity]"]`).val()) || 0;
            const unitPrice = parseFloat($(`input[name="items[${itemId}][unit_price]"]`).val()) || 0;
            const total = quantity * unitPrice;

            $(`#item-total-${itemId}`).val(total.toFixed(2));
            calculateTotal();
        }

        function calculateTotal() {
            let subtotal = 0;

            $('#purchase-items .purchase-item-row').each(function() {
                const quantity = parseFloat($(this).find('input[name*="[quantity]"]').val()) || 0;
                const unitPrice = parseFloat($(this).find('input[name*="[unit_price]"]').val()) || 0;
                subtotal += quantity * unitPrice;
            });

            const discount = parseFloat($('input[name="discount"]').val()) || 0;
            const tax = parseFloat($('input[name="tax"]').val()) || 0;
            const total = subtotal - discount + tax;

            $('#subtotal').text(subtotal.toFixed(2));
            $('#total').text(total.toFixed(2));
        }

        function viewPurchase(purchaseId) {
            // Show loading
            $('#purchase-details').html('<div class="text-center"><i class="bi bi-hourglass-split"></i> جاري التحميل...</div>');
            $('#viewPurchaseModal').modal('show');

            // Load purchase details via AJAX
            $.get('get_purchase_details.php', { id: purchaseId })
                .done(function(data) {
                    $('#purchase-details').html(data);
                })
                .fail(function() {
                    $('#purchase-details').html('<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>');
                });
        }

        function cancelPurchase(purchaseId) {
            Swal.fire({
                title: 'تأكيد الإلغاء',
                text: 'هل أنت متأكد من إلغاء هذا المشترى؟ سيتم تقليل المخزون.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، ألغي',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="action" value="cancel">
                        <input type="hidden" name="id" value="${purchaseId}">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

        function deletePurchase(purchaseId) {
            Swal.fire({
                title: 'تأكيد الحذف',
                text: 'هل أنت متأكد من حذف هذا المشترى؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" value="${purchaseId}">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

        function refreshTable() {
            location.reload();
        }

        function exportData() {
            purchasesTable.button('.buttons-excel').trigger();
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Add loading animation to buttons
        $('form').on('submit', function() {
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            submitBtn.html('<i class="bi bi-hourglass-split me-1"></i>جاري الحفظ...').prop('disabled', true);

            setTimeout(function() {
                submitBtn.html(originalText).prop('disabled', false);
            }, 3000);
        });
    </script>
</body>
</html>
