<?php
require 'db.php';

// دالة للحصول على إعداد معين
function getSetting($key, $default = '') {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetchColumn();
        return $result !== false ? $result : $default;
    } catch (PDOException $e) {
        return $default;
    }
}

// دالة للحصول على جميع الإعدادات
function getAllSettings() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
        $settings = [];
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        return $settings;
    } catch (PDOException $e) {
        return [];
    }
}

// دالة لتنسيق العملة
function formatCurrency($amount, $showSymbol = true) {
    $currency = getSetting('currency', 'ر.س');
    $formattedAmount = number_format($amount, 2);
    return $showSymbol ? $formattedAmount . ' ' . $currency : $formattedAmount;
}

// دالة لتنسيق التاريخ
function formatDate($date, $format = null) {
    if ($format === null) {
        $format = getSetting('date_format', 'Y-m-d');
    }
    return date($format, strtotime($date));
}

// دالة لتنسيق الوقت
function formatTime($time, $format = null) {
    if ($format === null) {
        $format = getSetting('time_format', 'H:i:s');
    }
    return date($format, strtotime($time));
}

// دالة لتنسيق التاريخ والوقت معاً
function formatDateTime($datetime, $dateFormat = null, $timeFormat = null) {
    if ($dateFormat === null) {
        $dateFormat = getSetting('date_format', 'Y-m-d');
    }
    if ($timeFormat === null) {
        $timeFormat = getSetting('time_format', 'H:i:s');
    }
    return date($dateFormat . ' ' . $timeFormat, strtotime($datetime));
}

// دالة للحصول على نسبة الضريبة
function getTaxRate() {
    return (float) getSetting('tax_rate', 15);
}

// دالة لحساب الضريبة
function calculateTax($amount) {
    $taxRate = getTaxRate();
    return ($amount * $taxRate) / 100;
}

// دالة لحساب المبلغ مع الضريبة
function calculateTotalWithTax($amount) {
    return $amount + calculateTax($amount);
}

// دالة للحصول على حد تنبيه المخزون المنخفض
function getLowStockAlert() {
    return (int) getSetting('low_stock_alert', 10);
}

// دالة للحصول على عدد العناصر في الصفحة
function getItemsPerPage() {
    return (int) getSetting('items_per_page', 20);
}

// دالة للحصول على معلومات الشركة
function getCompanyInfo() {
    return [
        'name' => getSetting('company_name', 'شركة نقاط البيع'),
        'phone' => getSetting('company_phone', ''),
        'email' => getSetting('company_email', ''),
        'address' => getSetting('company_address', ''),
        'logo' => getSetting('company_logo', ''),
    ];
}

// دالة للحصول على إعدادات الفاتورة
function getReceiptSettings() {
    return [
        'footer' => getSetting('receipt_footer', 'شكراً لزيارتكم'),
        'show_tax' => getSetting('show_tax_on_receipt', '1') === '1',
        'show_company_info' => getSetting('show_company_info_on_receipt', '1') === '1',
    ];
}

// دالة للحصول على لون النظام
function getThemeColor() {
    return getSetting('theme_color', '#3498db');
}

// دالة لتطبيق إعدادات النظام على الصفحة
function applySystemSettings() {
    $themeColor = getThemeColor();
    echo "<style>
        :root {
            --theme-color: {$themeColor};
            --primary-color: {$themeColor};
        }
        .bg-primary, .btn-primary {
            background-color: {$themeColor} !important;
            border-color: {$themeColor} !important;
        }
        .text-primary {
            color: {$themeColor} !important;
        }
        .border-primary {
            border-color: {$themeColor} !important;
        }
    </style>";
}

// إذا تم استدعاء الملف مباشرة، إرجاع الإعدادات كـ JSON
if (basename($_SERVER['PHP_SELF']) === 'get_settings.php') {
    header('Content-Type: application/json');
    echo json_encode(getAllSettings());
}
?>
