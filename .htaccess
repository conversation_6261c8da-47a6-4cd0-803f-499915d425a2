# تفعيل إعادة الكتابة
RewriteEngine On

# تحديد المجلد الأساسي
RewriteBase /pos3/

# السماح بالوصول للصفحة الرئيسية
DirectoryIndex index.php

# منع الوصول المباشر للملفات الحساسة
<Files "config.php">
    Require all denied
</Files>

<Files "check_permissions.php">
    Require all denied
</Files>

<Files "sidebar.php">
    Require all denied
</Files>

<Files "session_security.php">
    Require all denied
</Files>

<Files "security_check.php">
    Require all denied
</Files>

# حماية ملفات قاعدة البيانات
<FilesMatch "\.(db|sqlite|sql)$">
    Require all denied
</FilesMatch>

# حماية ملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|tmp)$">
    Require all denied
</FilesMatch>

# منع عرض محتويات المجلدات
Options -Indexes

# إعادة توجيه الصفحة الرئيسية إلى index.php
RewriteRule ^$ index.php [L]

# إعادة توجيه الروابط التي تحتوي على .php إلى بدونها
RewriteCond %{THE_REQUEST} \s/+pos3/([^\s]*?)\.php[\s?] [NC]
RewriteRule ^ /pos3/%1 [R=301,L]

# إزالة امتداد .php من الروابط
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^([^\.]+)$ $1.php [L]

# حماية من الهجمات الشائعة
RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} ^.*(globals|encode|localhost|loopback).* [NC,OR]
RewriteCond %{QUERY_STRING} ^.*(request|select|insert|union|declare).* [NC]
RewriteRule ^(.*)$ - [F,L]

# حماية من SQL Injection
RewriteCond %{QUERY_STRING} union.*select.*\( [NC,OR]
RewriteCond %{QUERY_STRING} union.*all.*select.* [NC,OR]
RewriteCond %{QUERY_STRING} concat.*\( [NC,OR]
RewriteCond %{QUERY_STRING} \;.*drop.*table [NC,OR]
RewriteCond %{QUERY_STRING} \;.*insert.*into [NC,OR]
RewriteCond %{QUERY_STRING} \;.*update.*set [NC]
RewriteRule ^(.*)$ - [F,L]

# حماية من XSS
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} javascript\: [NC,OR]
RewriteCond %{QUERY_STRING} vbscript\: [NC,OR]
RewriteCond %{QUERY_STRING} onload\( [NC,OR]
RewriteCond %{QUERY_STRING} onunload\( [NC,OR]
RewriteCond %{QUERY_STRING} onchange\( [NC,OR]
RewriteCond %{QUERY_STRING} onsubmit\( [NC,OR]
RewriteCond %{QUERY_STRING} onreset\( [NC,OR]
RewriteCond %{QUERY_STRING} onselect\( [NC,OR]
RewriteCond %{QUERY_STRING} onblur\( [NC,OR]
RewriteCond %{QUERY_STRING} onfocus\( [NC,OR]
RewriteCond %{QUERY_STRING} onkeypress\( [NC,OR]
RewriteCond %{QUERY_STRING} onkeydown\( [NC,OR]
RewriteCond %{QUERY_STRING} onkeyup\( [NC,OR]
RewriteCond %{QUERY_STRING} onmouseover\( [NC,OR]
RewriteCond %{QUERY_STRING} onmouseout\( [NC,OR]
RewriteCond %{QUERY_STRING} onmousedown\( [NC,OR]
RewriteCond %{QUERY_STRING} onmouseup\( [NC,OR]
RewriteCond %{QUERY_STRING} onmousemove\( [NC]
RewriteRule ^(.*)$ - [F,L]

# تحسين الأداء - ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تحسين الأداء - تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات الأمان الإضافية
<IfModule mod_headers.c>
    # منع clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # تفعيل XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# حماية من الوصول للملفات المخفية
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

# منع الوصول لملفات النسخ الاحتياطي
<FilesMatch "\~$">
    Require all denied
</FilesMatch>
