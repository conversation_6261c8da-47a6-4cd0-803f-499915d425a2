// ملف الرسوم البيانية للوحة التحكم
function initializeDashboardCharts(salesData, labelsData, topProductsData) {
    console.log('Initializing dashboard charts...');
    console.log('Sales Data:', salesData);
    console.log('Labels Data:', labelsData);
    console.log('Top Products Data:', topProductsData);
    
    // التحقق من تحميل Chart.js
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is not loaded');
        return;
    }
    
    // إنشاء رسم المبيعات
    createSalesChart(salesData, labelsData);
    
    // إنشاء رسم أفضل المنتجات
    if (topProductsData && topProductsData.labels && topProductsData.labels.length > 0) {
        createTopProductsChart(topProductsData);
    }
}

function createSalesChart(salesData, labelsData) {
    const canvas = document.getElementById('salesChart');
    if (!canvas) {
        console.error('Sales chart canvas not found');
        return;
    }
    
    try {
        const ctx = canvas.getContext('2d');
        const average = salesData.reduce((a, b) => a + b, 0) / salesData.length;
        
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labelsData,
                datasets: [{
                    label: 'المبيعات اليومية',
                    data: salesData,
                    borderColor: '#3498db',
                    backgroundColor: function(context) {
                        const chart = context.chart;
                        const {ctx, chartArea} = chart;
                        if (!chartArea) {
                            return 'rgba(52, 152, 219, 0.2)';
                        }
                        const gradient = ctx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom);
                        gradient.addColorStop(0, 'rgba(52, 152, 219, 0.4)');
                        gradient.addColorStop(0.5, 'rgba(52, 152, 219, 0.2)');
                        gradient.addColorStop(1, 'rgba(52, 152, 219, 0.05)');
                        return gradient;
                    },
                    borderWidth: 4,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: function(context) {
                        const value = context.parsed.y;
                        if (value > average) return '#27ae60';
                        if (value === 0) return '#95a5a6';
                        return '#e74c3c';
                    },
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 3,
                    pointRadius: 7,
                    pointHoverRadius: 12
                }, {
                    label: 'المتوسط اليومي',
                    data: new Array(salesData.length).fill(average),
                    borderColor: '#f39c12',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    borderDash: [10, 5],
                    fill: false,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                    tension: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        align: 'end',
                        labels: {
                            usePointStyle: true,
                            pointStyle: 'circle',
                            font: {
                                family: 'Segoe UI',
                                size: 12
                            },
                            color: '#7f8c8d',
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(44, 62, 80, 0.95)',
                        titleColor: '#ecf0f1',
                        bodyColor: '#ecf0f1',
                        borderColor: '#3498db',
                        borderWidth: 2,
                        cornerRadius: 12,
                        displayColors: false,
                        titleFont: {
                            size: 14,
                            weight: 'bold',
                            family: 'Segoe UI'
                        },
                        bodyFont: {
                            size: 13,
                            family: 'Segoe UI'
                        },
                        padding: 12,
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const value = context.parsed.y;
                                return 'المبيعات: ' + value.toFixed(2) + ' د.ل';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(52, 152, 219, 0.08)',
                            drawBorder: false,
                            lineWidth: 1
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#7f8c8d',
                            font: {
                                family: 'Segoe UI',
                                size: 12
                            },
                            padding: 10,
                            callback: function(value) {
                                return value.toFixed(0) + ' د.ل';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#7f8c8d',
                            font: {
                                family: 'Segoe UI',
                                size: 11
                            },
                            padding: 10,
                            maxRotation: 0
                        }
                    }
                }
            }
        });
        
        console.log('Sales chart created successfully');
        return chart;
        
    } catch (error) {
        console.error('Error creating sales chart:', error);
        // عرض رسالة خطأ في مكان الرسم البياني
        canvas.parentElement.innerHTML = '<div class="alert alert-warning text-center"><i class="bi bi-exclamation-triangle me-2"></i>خطأ في تحميل الرسم البياني</div>';
    }
}

function createTopProductsChart(topProductsData) {
    const canvas = document.getElementById('topProductsChart');
    if (!canvas) {
        console.error('Top products chart canvas not found');
        return;
    }
    
    try {
        const ctx = canvas.getContext('2d');
        
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: topProductsData.labels,
                datasets: [{
                    data: topProductsData.data,
                    backgroundColor: [
                        '#3498db',
                        '#2ecc71',
                        '#f39c12',
                        '#e74c3c',
                        '#9b59b6'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                family: 'Segoe UI',
                                size: 12
                            },
                            color: '#6c757d'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#3498db',
                        borderWidth: 1,
                        cornerRadius: 10,
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + ' قطعة';
                            }
                        }
                    }
                },
                cutout: '60%',
                elements: {
                    arc: {
                        borderWidth: 2,
                        borderColor: '#fff'
                    }
                }
            }
        });
        
        console.log('Top products chart created successfully');
        return chart;
        
    } catch (error) {
        console.error('Error creating top products chart:', error);
        canvas.parentElement.innerHTML = '<div class="alert alert-warning text-center"><i class="bi bi-exclamation-triangle me-2"></i>خطأ في تحميل الرسم البياني</div>';
    }
}
