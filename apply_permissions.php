<?php
// ملف لتطبيق الصلاحيات على صفحات النظام
require_once 'check_permissions.php';

// تطبيق الصلاحيات على الصفحات المختلفة
function applyPagePermissions($pdo, $userId, $currentPage) {
    // إنشاء جدول سجل العمليات
    createUserLogsTable($pdo);
    
    // تسجيل دخول المستخدم للصفحة
    logUserAction($pdo, $userId, 'page_access', $currentPage);
    
    // تحديد الصلاحيات المطلوبة لكل صفحة
    $pagePermissions = [
        'products.php' => ['products.view'],
        'categories.php' => ['products.view'],
        'units.php' => ['products.view'],
        'pos.php' => ['pos.access'],
        'sales.php' => ['sales.view'],
        'purchases.php' => ['inventory.view'],
        'inventory.php' => ['inventory.view'],
        'customers.php' => ['customers.view'],
        'suppliers.php' => ['suppliers.view'],
        'accounting.php' => ['accounting.view'],
        'reports.php' => ['reports.view'],
        'settings.php' => ['settings.view']
    ];
    
    // التحقق من الصلاحيات
    if (isset($pagePermissions[$currentPage])) {
        $requiredPermissions = $pagePermissions[$currentPage];
        
        if (!canAccess($pdo, $userId, $requiredPermissions)) {
            // إعادة توجيه إلى صفحة عدم وجود صلاحية
            header('Location: no_permission.php?page=' . urlencode($currentPage));
            exit;
        }
    }
}

// دالة لإنشاء شريط جانبي مخصص حسب الصلاحيات
function generatePermissionBasedSidebar($pdo, $userId, $currentPage = '') {
    $allowedPages = getAllowedPages($pdo, $userId);
    $companyName = getSetting('company_name', 'نظام نقاط البيع');
    $companyLogo = getSetting('company_logo');
    
    $html = '<div class="sidebar">';
    $html .= '<div class="logo">';
    
    if (!empty($companyLogo) && file_exists($companyLogo)) {
        $html .= '<img src="' . $companyLogo . '" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">';
        $html .= '<h5 class="mb-0">' . htmlspecialchars($companyName) . '</h5>';
    } else {
        $html .= '<h4 class="mb-0"><i class="bi bi-shop"></i> ' . htmlspecialchars($companyName) . '</h4>';
    }
    
    $html .= '<small class="text-light">مرحباً، ' . $_SESSION['username'] . '</small>';
    $html .= '</div>';
    
    $html .= '<nav class="nav flex-column mt-3">';
    
    foreach ($allowedPages as $page) {
        $activeClass = ($currentPage === $page['url']) ? 'active' : '';
        $html .= '<a class="nav-link ' . $activeClass . '" href="' . $page['url'] . '">';
        $html .= '<i class="' . $page['icon'] . ' me-2"></i> ' . $page['name'];
        $html .= '</a>';
    }
    
    $html .= '<hr class="text-light">';
    $html .= '<a class="nav-link text-warning" href="logout.php">';
    $html .= '<i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج';
    $html .= '</a>';
    $html .= '</nav>';
    $html .= '</div>';
    
    return $html;
}

// دالة لإنشاء أزرار مشروطة بالصلاحيات
function createPermissionButton($pdo, $userId, $permission, $buttonText, $buttonClass = 'btn-primary', $icon = '', $onclick = '') {
    if (hasPermission($pdo, $userId, $permission) || isAdmin($pdo, $userId)) {
        $iconHtml = $icon ? '<i class="' . $icon . ' me-2"></i>' : '';
        $onclickAttr = $onclick ? 'onclick="' . $onclick . '"' : '';
        return '<button type="button" class="btn ' . $buttonClass . '" ' . $onclickAttr . '>' . $iconHtml . $buttonText . '</button>';
    } else {
        return '<button type="button" class="btn btn-secondary" disabled title="ليس لديك صلاحية">غير مصرح</button>';
    }
}

// دالة لإخفاء/إظهار أعمدة الجدول حسب الصلاحيات
function getTableColumns($pdo, $userId, $tableType) {
    $columns = [];
    
    switch ($tableType) {
        case 'products':
            $columns[] = ['data' => 'name', 'title' => 'اسم المنتج'];
            $columns[] = ['data' => 'barcode', 'title' => 'الباركود'];
            $columns[] = ['data' => 'price', 'title' => 'السعر'];
            $columns[] = ['data' => 'quantity', 'title' => 'الكمية'];
            
            if (hasPermission($pdo, $userId, 'products.edit') || isAdmin($pdo, $userId)) {
                $columns[] = ['data' => 'actions', 'title' => 'الإجراءات', 'orderable' => false];
            }
            break;
            
        case 'sales':
            $columns[] = ['data' => 'sale_number', 'title' => 'رقم البيع'];
            $columns[] = ['data' => 'customer', 'title' => 'العميل'];
            $columns[] = ['data' => 'total', 'title' => 'المجموع'];
            $columns[] = ['data' => 'date', 'title' => 'التاريخ'];
            
            if (hasPermission($pdo, $userId, 'sales.edit') || isAdmin($pdo, $userId)) {
                $columns[] = ['data' => 'actions', 'title' => 'الإجراءات', 'orderable' => false];
            }
            break;
            
        case 'users':
            if (hasPermission($pdo, $userId, 'users.view') || isAdmin($pdo, $userId)) {
                $columns[] = ['data' => 'username', 'title' => 'اسم المستخدم'];
                $columns[] = ['data' => 'email', 'title' => 'البريد الإلكتروني'];
                $columns[] = ['data' => 'role', 'title' => 'الدور'];
                $columns[] = ['data' => 'status', 'title' => 'الحالة'];
                
                if (hasPermission($pdo, $userId, 'users.edit') || isAdmin($pdo, $userId)) {
                    $columns[] = ['data' => 'actions', 'title' => 'الإجراءات', 'orderable' => false];
                }
            }
            break;
    }
    
    return $columns;
}

// دالة لإنشاء قائمة منسدلة للإجراءات حسب الصلاحيات
function createActionDropdown($pdo, $userId, $itemId, $itemType) {
    $actions = [];
    
    switch ($itemType) {
        case 'product':
            if (hasPermission($pdo, $userId, 'products.edit') || isAdmin($pdo, $userId)) {
                $actions[] = ['text' => 'تعديل', 'icon' => 'bi-pencil', 'class' => 'text-primary', 'onclick' => 'editProduct(' . $itemId . ')'];
            }
            if (hasPermission($pdo, $userId, 'products.delete') || isAdmin($pdo, $userId)) {
                $actions[] = ['text' => 'حذف', 'icon' => 'bi-trash', 'class' => 'text-danger', 'onclick' => 'deleteProduct(' . $itemId . ')'];
            }
            break;
            
        case 'sale':
            if (hasPermission($pdo, $userId, 'sales.view') || isAdmin($pdo, $userId)) {
                $actions[] = ['text' => 'عرض', 'icon' => 'bi-eye', 'class' => 'text-info', 'onclick' => 'viewSale(' . $itemId . ')'];
            }
            if (hasPermission($pdo, $userId, 'pos.refund') || isAdmin($pdo, $userId)) {
                $actions[] = ['text' => 'مرتجع', 'icon' => 'bi-arrow-return-left', 'class' => 'text-warning', 'onclick' => 'refundSale(' . $itemId . ')'];
            }
            if (hasPermission($pdo, $userId, 'sales.delete') || isAdmin($pdo, $userId)) {
                $actions[] = ['text' => 'حذف', 'icon' => 'bi-trash', 'class' => 'text-danger', 'onclick' => 'deleteSale(' . $itemId . ')'];
            }
            break;
            
        case 'user':
            if (hasPermission($pdo, $userId, 'users.edit') || isAdmin($pdo, $userId)) {
                $actions[] = ['text' => 'تعديل', 'icon' => 'bi-pencil', 'class' => 'text-primary', 'onclick' => 'editUser(' . $itemId . ')'];
            }
            if (hasPermission($pdo, $userId, 'users.permissions') || isAdmin($pdo, $userId)) {
                $actions[] = ['text' => 'الصلاحيات', 'icon' => 'bi-shield-check', 'class' => 'text-info', 'onclick' => 'manageUserPermissions(' . $itemId . ')'];
            }
            if (hasPermission($pdo, $userId, 'users.delete') || isAdmin($pdo, $userId)) {
                $actions[] = ['text' => 'حذف', 'icon' => 'bi-trash', 'class' => 'text-danger', 'onclick' => 'deleteUser(' . $itemId . ')'];
            }
            break;
    }
    
    if (empty($actions)) {
        return '<span class="text-muted">لا توجد إجراءات</span>';
    }
    
    $html = '<div class="dropdown">';
    $html .= '<button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">';
    $html .= '<i class="bi bi-three-dots"></i>';
    $html .= '</button>';
    $html .= '<ul class="dropdown-menu">';
    
    foreach ($actions as $action) {
        $html .= '<li>';
        $html .= '<a class="dropdown-item ' . $action['class'] . '" href="#" onclick="' . $action['onclick'] . '">';
        $html .= '<i class="' . $action['icon'] . ' me-2"></i>' . $action['text'];
        $html .= '</a>';
        $html .= '</li>';
    }
    
    $html .= '</ul>';
    $html .= '</div>';
    
    return $html;
}

// دالة لإنشاء تنبيهات الصلاحيات
function createPermissionAlert($message, $type = 'warning') {
    $icons = [
        'warning' => 'bi-exclamation-triangle',
        'danger' => 'bi-shield-exclamation',
        'info' => 'bi-info-circle'
    ];
    
    $icon = $icons[$type] ?? 'bi-info-circle';
    
    $html = '<div class="alert alert-' . $type . ' text-center">';
    $html .= '<i class="' . $icon . ' fs-1 mb-3"></i>';
    $html .= '<h5>تنبيه صلاحيات</h5>';
    $html .= '<p>' . htmlspecialchars($message) . '</p>';
    $html .= '<a href="dashboard.php" class="btn btn-primary">العودة للوحة التحكم</a>';
    $html .= '</div>';
    
    return $html;
}

// دالة لإنشاء JavaScript للصلاحيات
function generatePermissionJS($pdo, $userId) {
    $permissions = getUserPermissions($pdo, $userId);
    $permissionsList = array_column($permissions, 'name');
    $isAdminUser = isAdmin($pdo, $userId);
    $userRole = getUserRole($pdo, $userId);
    
    echo '<script>';
    echo 'window.userPermissions = ' . json_encode($permissionsList) . ';';
    echo 'window.isAdmin = ' . ($isAdminUser ? 'true' : 'false') . ';';
    echo 'window.userRole = ' . json_encode($userRole) . ';';
    
    echo 'function hasPermission(permission) {';
    echo '    return window.isAdmin || window.userPermissions.includes(permission);';
    echo '}';
    
    echo 'function checkPermissionAndExecute(permission, callback, errorMessage) {';
    echo '    if (hasPermission(permission)) {';
    echo '        callback();';
    echo '    } else {';
    echo '        Swal.fire({';
    echo '            icon: "error",';
    echo '            title: "غير مصرح",';
    echo '            text: errorMessage || "ليس لديك صلاحية لتنفيذ هذا الإجراء"';
    echo '        });';
    echo '    }';
    echo '}';
    
    echo 'function hideElementsWithoutPermission() {';
    echo '    document.querySelectorAll("[data-permission]").forEach(function(element) {';
    echo '        const requiredPermission = element.getAttribute("data-permission");';
    echo '        if (!hasPermission(requiredPermission)) {';
    echo '            element.style.display = "none";';
    echo '        }';
    echo '    });';
    echo '}';
    
    echo 'document.addEventListener("DOMContentLoaded", hideElementsWithoutPermission);';
    echo '</script>';
}

// دالة للحصول على الصفحات المتاحة للمستخدم
function getAvailablePages($pdo, $userId) {
    $allPages = [
        'dashboard.php' => ['title' => 'لوحة التحكم', 'permission' => '', 'icon' => 'bi-house-door'],
        'pos.php' => ['title' => 'نقطة البيع', 'permission' => 'pos.access', 'icon' => 'bi-cart'],
        'sales.php' => ['title' => 'سجل المبيعات', 'permission' => 'sales.view', 'icon' => 'bi-graph-up'],
        'products.php' => ['title' => 'إدارة المنتجات', 'permission' => 'products.view', 'icon' => 'bi-box'],
        'categories.php' => ['title' => 'إدارة الفئات', 'permission' => 'products.view', 'icon' => 'bi-folder'],
        'units.php' => ['title' => 'إدارة الوحدات', 'permission' => 'products.view', 'icon' => 'bi-rulers'],
        'inventory.php' => ['title' => 'إدارة المخزون', 'permission' => 'inventory.view', 'icon' => 'bi-boxes'],
        'purchases.php' => ['title' => 'سجل المشتريات', 'permission' => 'inventory.view', 'icon' => 'bi-clipboard-data'],
        'customers.php' => ['title' => 'إدارة العملاء', 'permission' => 'customers.view', 'icon' => 'bi-people'],
        'suppliers.php' => ['title' => 'إدارة الموردين', 'permission' => 'suppliers.view', 'icon' => 'bi-building'],
        'accounting.php' => ['title' => 'النظام المحاسبي', 'permission' => 'accounting.view', 'icon' => 'bi-currency-dollar'],
        'reports.php' => ['title' => 'التقارير والإحصائيات', 'permission' => 'reports.view', 'icon' => 'bi-bar-chart'],
        'settings.php' => ['title' => 'إعدادات النظام', 'permission' => 'settings.view', 'icon' => 'bi-gear']
    ];

    $availablePages = [];

    foreach ($allPages as $page => $info) {
        if (empty($info['permission']) || hasPermission($pdo, $userId, $info['permission']) || isAdmin($pdo, $userId)) {
            $availablePages[$page] = $info;
        }
    }

    return $availablePages;
}

// دالة للحصول على أول صفحة متاحة للمستخدم
function getFirstAvailablePage($pdo, $userId) {
    $availablePages = getAvailablePages($pdo, $userId);

    // ترتيب الأولوية
    $priorityOrder = ['dashboard.php', 'pos.php', 'sales.php', 'products.php', 'customers.php'];

    foreach ($priorityOrder as $page) {
        if (isset($availablePages[$page])) {
            return $page;
        }
    }

    // إذا لم توجد صفحة من الأولوية، إرجاع أول صفحة متاحة
    return array_key_first($availablePages) ?: 'dashboard.php';
}

// دالة لإعادة التوجيه التلقائي للصفحة المناسبة
function redirectToAvailablePage($pdo, $userId) {
    $firstPage = getFirstAvailablePage($pdo, $userId);
    header('Location: ' . $firstPage);
    exit;
}

// دالة للتحقق من إمكانية الوصول لصفحة معينة
function canAccessPage($pdo, $userId, $page) {
    $availablePages = getAvailablePages($pdo, $userId);
    return isset($availablePages[$page]);
}
?>
