-- إدراج بيانات تجريبية لنظام نقاط البيع

-- إدراج مستخدم تجريبي (كلمة المرور: admin123)
INSERT INTO users (username, password, name, role) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المدير العام', 'admin');

-- إدراج فئات المنتجات
INSERT INTO categories (name) VALUES 
('مشروبات'),
('حلويات'),
('مواد غذائية'),
('منظفات'),
('أدوات مكتبية');

-- إدراج وحدات القياس
INSERT INTO units (name) VALUES 
('قطعة'),
('كيلو'),
('لتر'),
('علبة'),
('كرتون');

-- إدراج موردين
INSERT INTO suppliers (name, phone, address) VALUES 
('شركة المشروبات الوطنية', '0501234567', 'الرياض - حي الملز'),
('مصنع الحلويات الذهبية', '0509876543', 'جدة - حي الروضة'),
('مؤسسة المواد الغذائية', '0551122334', 'الدمام - حي الفيصلية'),
('شركة المنظفات الحديثة', '0544556677', 'الرياض - حي النخيل');

-- إدراج عملاء
INSERT INTO customers (name, phone, address) VALUES 
('أحمد محمد', '0501111111', 'الرياض - حي العليا'),
('فاطمة علي', '0502222222', 'جدة - حي الزهراء'),
('محمد سعد', '0503333333', 'الدمام - حي الشاطئ'),
('نورا خالد', '0504444444', 'الرياض - حي الملقا'),
('عبدالله أحمد', '0505555555', 'مكة - حي العزيزية');

-- إدراج منتجات
INSERT INTO products (name, category_id, unit_id, supplier_id, price, stock) VALUES 
('كوكا كولا 330مل', 1, 1, 1, 2.50, 100),
('بيبسي 330مل', 1, 1, 1, 2.50, 80),
('ماء صافي 1.5لتر', 1, 1, 1, 1.00, 200),
('شوكولاتة كيت كات', 2, 1, 2, 5.00, 50),
('بسكويت أوريو', 2, 1, 2, 3.50, 75),
('أرز بسمتي', 3, 2, 3, 15.00, 30),
('زيت الطبخ', 3, 1, 3, 12.00, 25),
('صابون الغسيل', 4, 1, 4, 8.00, 40),
('منظف الأرضيات', 4, 1, 4, 6.50, 35),
('أقلام حبر', 5, 1, 4, 2.00, 60);

-- إدراج مبيعات تجريبية
INSERT INTO sales (customer_id, user_id, total, sale_date) VALUES 
(1, 1, 25.50, '2024-01-15 10:30:00'),
(2, 1, 18.00, '2024-01-15 14:20:00'),
(3, 1, 45.00, '2024-01-16 09:15:00'),
(1, 1, 12.50, '2024-01-16 16:45:00'),
(4, 1, 33.00, '2024-01-17 11:30:00'),
(5, 1, 28.50, '2024-01-17 15:20:00'),
(2, 1, 15.00, '2024-01-18 10:10:00'),
(3, 1, 22.00, '2024-01-18 13:40:00'),
(1, 1, 35.50, '2024-01-19 09:30:00'),
(4, 1, 19.00, '2024-01-19 17:15:00');

-- إدراج تفاصيل المبيعات
INSERT INTO sale_items (sale_id, product_id, quantity, price) VALUES 
-- البيع الأول
(1, 1, 5, 2.50),
(1, 4, 3, 5.00),
-- البيع الثاني
(2, 2, 4, 2.50),
(2, 5, 2, 3.50),
-- البيع الثالث
(3, 6, 2, 15.00),
(3, 7, 1, 12.00),
(3, 10, 1, 2.00),
-- البيع الرابع
(4, 1, 3, 2.50),
(4, 3, 5, 1.00),
-- البيع الخامس
(5, 8, 2, 8.00),
(5, 9, 1, 6.50),
(5, 4, 2, 5.00),
-- البيع السادس
(6, 2, 6, 2.50),
(6, 5, 4, 3.50),
-- البيع السابع
(7, 6, 1, 15.00),
-- البيع الثامن
(8, 1, 4, 2.50),
(8, 4, 2, 5.00),
-- البيع التاسع
(9, 7, 2, 12.00),
(9, 8, 1, 8.00),
(9, 10, 2, 2.00),
-- البيع العاشر
(10, 3, 10, 1.00),
(10, 5, 2, 3.50);

-- إدراج مشتريات تجريبية
INSERT INTO purchases (supplier_id, user_id, total, purchase_date) VALUES 
(1, 1, 500.00, '2024-01-10 09:00:00'),
(2, 1, 350.00, '2024-01-12 11:30:00'),
(3, 1, 800.00, '2024-01-14 14:15:00'),
(4, 1, 250.00, '2024-01-16 10:45:00');

-- إدراج تفاصيل المشتريات
INSERT INTO purchase_items (purchase_id, product_id, quantity, price) VALUES 
-- المشترى الأول
(1, 1, 100, 2.00),
(1, 2, 80, 2.00),
(1, 3, 200, 0.50),
-- المشترى الثاني
(2, 4, 50, 4.00),
(2, 5, 75, 2.50),
-- المشترى الثالث
(3, 6, 30, 12.00),
(3, 7, 25, 10.00),
-- المشترى الرابع
(4, 8, 40, 6.00),
(4, 9, 35, 5.00);

-- إدراج مصروفات تجريبية
INSERT INTO expenses (amount, description, user_id, expense_date) VALUES 
(500.00, 'فاتورة كهرباء', 1, '2024-01-15 10:00:00'),
(200.00, 'صيانة أجهزة', 1, '2024-01-16 14:30:00'),
(150.00, 'مواد تنظيف', 1, '2024-01-17 09:15:00'),
(300.00, 'راتب موظف', 1, '2024-01-18 16:00:00');

-- إدراج إيداعات تجريبية
INSERT INTO deposits (amount, description, user_id, deposit_date) VALUES 
(1000.00, 'رأس مال إضافي', 1, '2024-01-10 08:00:00'),
(500.00, 'أرباح استثمار', 1, '2024-01-15 12:00:00');

-- تحديث المخزون بناءً على المبيعات والمشتريات
UPDATE products SET stock = stock - 
    (SELECT IFNULL(SUM(si.quantity), 0) FROM sale_items si WHERE si.product_id = products.id);

-- إدراج حركات المخزون
INSERT INTO stock_movements (product_id, type, reference_id, quantity, movement_date)
SELECT si.product_id, 'sale', si.sale_id, -si.quantity, s.sale_date
FROM sale_items si
JOIN sales s ON si.sale_id = s.id;

INSERT INTO stock_movements (product_id, type, reference_id, quantity, movement_date)
SELECT pi.product_id, 'purchase', pi.purchase_id, pi.quantity, p.purchase_date
FROM purchase_items pi
JOIN purchases p ON pi.purchase_id = p.id;
