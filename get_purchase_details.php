<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    exit('غير مصرح');
}

require 'db.php';
require 'get_settings.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    exit('معرف غير صحيح');
}

$purchaseId = intval($_GET['id']);

try {
    // جلب تفاصيل المشترى
    $purchaseStmt = $pdo->prepare("
        SELECT p.*, 
               s.name as supplier_name,
               s.phone as supplier_phone,
               s.address as supplier_address,
               u.name as user_name
        FROM purchases p 
        LEFT JOIN suppliers s ON p.supplier_id = s.id 
        LEFT JOIN users u ON p.user_id = u.id 
        WHERE p.id = ?
    ");
    $purchaseStmt->execute([$purchaseId]);
    $purchase = $purchaseStmt->fetch();

    if (!$purchase) {
        http_response_code(404);
        exit('المشترى غير موجود');
    }

    // جلب عناصر المشترى
    $itemsStmt = $pdo->prepare("
        SELECT pi.*, 
               pr.name as product_name,
               pr.code as product_code,
               c.name as category_name,
               u.name as unit_name
        FROM purchase_items pi 
        LEFT JOIN products pr ON pi.product_id = pr.id 
        LEFT JOIN categories c ON pr.category_id = c.id 
        LEFT JOIN units u ON pr.unit_id = u.id 
        WHERE pi.purchase_id = ?
        ORDER BY pr.name
    ");
    $itemsStmt->execute([$purchaseId]);
    $items = $itemsStmt->fetchAll();

} catch (PDOException $e) {
    http_response_code(500);
    exit('خطأ في قاعدة البيانات');
}
?>

<div class="row">
    <div class="col-md-6">
        <h6><i class="bi bi-info-circle me-2"></i>معلومات المشترى</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>رقم الفاتورة:</strong></td>
                <td><?php echo !empty($purchase['invoice_number']) ? htmlspecialchars($purchase['invoice_number']) : 'غير محدد'; ?></td>
            </tr>
            <tr>
                <td><strong>المورد:</strong></td>
                <td>
                    <?php echo htmlspecialchars($purchase['supplier_name'] ?? 'غير محدد'); ?>
                    <?php if (!empty($purchase['supplier_phone'])): ?>
                        <br><small class="text-muted"><?php echo htmlspecialchars($purchase['supplier_phone']); ?></small>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td><strong>تاريخ المشترى:</strong></td>
                <td><?php echo formatDateTime($purchase['purchase_date']); ?></td>
            </tr>
            <tr>
                <td><strong>المستخدم:</strong></td>
                <td><?php echo htmlspecialchars($purchase['user_name'] ?? 'غير محدد'); ?></td>
            </tr>
            <tr>
                <td><strong>حالة الطلب:</strong></td>
                <td>
                    <?php
                    $statusClass = 'bg-secondary';
                    $statusText = $purchase['status'];
                    
                    switch ($purchase['status']) {
                        case 'completed':
                            $statusClass = 'bg-success';
                            $statusText = 'مكتمل';
                            break;
                        case 'pending':
                            $statusClass = 'bg-warning';
                            $statusText = 'معلق';
                            break;
                        case 'cancelled':
                            $statusClass = 'bg-danger';
                            $statusText = 'ملغى';
                            break;
                    }
                    ?>
                    <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                </td>
            </tr>
            <tr>
                <td><strong>حالة الدفع:</strong></td>
                <td>
                    <?php
                    $paymentClass = 'bg-secondary';
                    $paymentText = $purchase['payment_status'];
                    
                    switch ($purchase['payment_status']) {
                        case 'paid':
                            $paymentClass = 'bg-success';
                            $paymentText = 'مدفوع';
                            break;
                        case 'unpaid':
                            $paymentClass = 'bg-danger';
                            $paymentText = 'غير مدفوع';
                            break;
                        case 'partial':
                            $paymentClass = 'bg-warning';
                            $paymentText = 'جزئي';
                            break;
                    }
                    ?>
                    <span class="badge <?php echo $paymentClass; ?>"><?php echo $paymentText; ?></span>
                </td>
            </tr>
            <?php if (!empty($purchase['notes'])): ?>
            <tr>
                <td><strong>ملاحظات:</strong></td>
                <td><?php echo htmlspecialchars($purchase['notes']); ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
    <div class="col-md-6">
        <h6><i class="bi bi-calculator me-2"></i>ملخص المبالغ</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>المجموع الفرعي:</strong></td>
                <td><?php echo formatCurrency($purchase['total'] + $purchase['discount'] - $purchase['tax']); ?></td>
            </tr>
            <?php if ($purchase['discount'] > 0): ?>
            <tr>
                <td><strong>الخصم:</strong></td>
                <td class="text-danger">- <?php echo formatCurrency($purchase['discount']); ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($purchase['tax'] > 0): ?>
            <tr>
                <td><strong>الضريبة:</strong></td>
                <td class="text-info">+ <?php echo formatCurrency($purchase['tax']); ?></td>
            </tr>
            <?php endif; ?>
            <tr class="table-primary">
                <td><strong>الإجمالي:</strong></td>
                <td><strong><?php echo formatCurrency($purchase['total']); ?></strong></td>
            </tr>
        </table>
    </div>
</div>

<hr>

<h6><i class="bi bi-list me-2"></i>أصناف المشترى</h6>
<div class="table-responsive">
    <table class="table table-sm table-striped">
        <thead>
            <tr>
                <th>المنتج</th>
                <th>الفئة</th>
                <th>الوحدة</th>
                <th>الكمية</th>
                <th>سعر الوحدة</th>
                <th>الإجمالي</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            $totalQuantity = 0;
            $totalAmount = 0;
            foreach ($items as $item): 
                $totalQuantity += $item['quantity'];
                $totalAmount += $item['total_price'] ?? ($item['quantity'] * $item['unit_price']);
            ?>
            <tr>
                <td>
                    <strong><?php echo htmlspecialchars($item['product_name'] ?? 'منتج محذوف'); ?></strong>
                    <?php if (!empty($item['product_code'])): ?>
                        <br><small class="text-muted">كود: <?php echo htmlspecialchars($item['product_code']); ?></small>
                    <?php endif; ?>
                </td>
                <td><?php echo htmlspecialchars($item['category_name'] ?? '-'); ?></td>
                <td><?php echo htmlspecialchars($item['unit_name'] ?? '-'); ?></td>
                <td>
                    <span class="badge bg-info"><?php echo $item['quantity']; ?></span>
                </td>
                <td><?php echo formatCurrency($item['unit_price'] ?? $item['price']); ?></td>
                <td>
                    <strong><?php echo formatCurrency($item['total_price'] ?? ($item['quantity'] * ($item['unit_price'] ?? $item['price']))); ?></strong>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
        <tfoot>
            <tr class="table-secondary">
                <th colspan="3">الإجمالي</th>
                <th><span class="badge bg-primary"><?php echo $totalQuantity; ?></span></th>
                <th>-</th>
                <th><strong><?php echo formatCurrency($totalAmount); ?></strong></th>
            </tr>
        </tfoot>
    </table>
</div>

<?php if (empty($items)): ?>
<div class="alert alert-warning">
    <i class="bi bi-exclamation-triangle me-2"></i>
    لا توجد أصناف في هذا المشترى
</div>
<?php endif; ?>
