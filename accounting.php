<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';
require 'check_permissions.php';
require 'apply_permissions.php';
// التحقق من صلاحيات الوصول للصفحة
applyPagePermissions($pdo, $_SESSION['user_id'], 'accounting.php');


// إنشاء جداول النظام المحاسبي إذا لم تكن موجودة
try {
    // جدول دليل الحسابات
    $pdo->exec("CREATE TABLE IF NOT EXISTS chart_of_accounts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        account_code VARCHAR(20) UNIQUE NOT NULL,
        account_name VARCHAR(100) NOT NULL,
        account_type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL,
        parent_id INT NULL,
        is_active TINYINT(1) DEFAULT 1,
        balance DECIMAL(15,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES chart_of_accounts(id),
        INDEX idx_type (account_type),
        INDEX idx_code (account_code)
    )");
    
    // جدول القيود المحاسبية
    $pdo->exec("CREATE TABLE IF NOT EXISTS journal_entries (
        id INT AUTO_INCREMENT PRIMARY KEY,
        entry_number VARCHAR(20) UNIQUE NOT NULL,
        entry_date DATE NOT NULL,
        description TEXT NOT NULL,
        reference_type ENUM('sale', 'purchase', 'payment', 'receipt', 'adjustment', 'manual') DEFAULT 'manual',
        reference_id INT NULL,
        total_debit DECIMAL(15,2) NOT NULL,
        total_credit DECIMAL(15,2) NOT NULL,
        status ENUM('draft', 'posted', 'cancelled') DEFAULT 'draft',
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_date (entry_date),
        INDEX idx_reference (reference_type, reference_id),
        INDEX idx_status (status)
    )");
    
    // جدول تفاصيل القيود المحاسبية
    $pdo->exec("CREATE TABLE IF NOT EXISTS journal_entry_details (
        id INT AUTO_INCREMENT PRIMARY KEY,
        journal_entry_id INT NOT NULL,
        account_id INT NOT NULL,
        debit DECIMAL(15,2) DEFAULT 0,
        credit DECIMAL(15,2) DEFAULT 0,
        description TEXT,
        FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
        FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
        INDEX idx_entry (journal_entry_id),
        INDEX idx_account (account_id)
    )");
    
    // إدراج الحسابات الأساسية إذا لم تكن موجودة
    $accountsCount = $pdo->query("SELECT COUNT(*) FROM chart_of_accounts")->fetchColumn();
    if ($accountsCount == 0) {
        $basicAccounts = [
            // الأصول
            ['1000', 'الأصول', 'asset', null],
            ['1100', 'الأصول المتداولة', 'asset', 1],
            ['1110', 'النقدية', 'asset', 2],
            ['1111', 'الصندوق', 'asset', 3],
            ['1112', 'البنك', 'asset', 3],
            ['1120', 'المخزون', 'asset', 2],
            ['1130', 'العملاء', 'asset', 2],
            
            // الخصوم
            ['2000', 'الخصوم', 'liability', null],
            ['2100', 'الخصوم المتداولة', 'liability', 8],
            ['2110', 'الموردين', 'liability', 9],
            ['2120', 'الضرائب المستحقة', 'liability', 9],
            
            // حقوق الملكية
            ['3000', 'حقوق الملكية', 'equity', null],
            ['3100', 'رأس المال', 'equity', 12],
            ['3200', 'الأرباح المحتجزة', 'equity', 12],
            
            // الإيرادات
            ['4000', 'الإيرادات', 'revenue', null],
            ['4100', 'إيرادات المبيعات', 'revenue', 15],
            ['4200', 'إيرادات أخرى', 'revenue', 15],
            
            // المصروفات
            ['5000', 'المصروفات', 'expense', null],
            ['5100', 'تكلفة البضاعة المباعة', 'expense', 18],
            ['5200', 'مصروفات التشغيل', 'expense', 18],
            ['5210', 'مصروفات الرواتب', 'expense', 20],
            ['5220', 'مصروفات الإيجار', 'expense', 20],
            ['5230', 'مصروفات الكهرباء', 'expense', 20],
            ['5240', 'مصروفات أخرى', 'expense', 20]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO chart_of_accounts (account_code, account_name, account_type, parent_id) VALUES (?, ?, ?, ?)");
        foreach ($basicAccounts as $index => $account) {
            $parentId = $account[3] ? $account[3] : null;
            $stmt->execute([$account[0], $account[1], $account[2], $parentId]);
        }
    }
    
    // إنشاء جدول المصروفات
    $pdo->exec("CREATE TABLE IF NOT EXISTS expenses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        expense_number VARCHAR(20) UNIQUE NOT NULL,
        expense_date DATE NOT NULL,
        category ENUM('salary', 'rent', 'utilities', 'supplies', 'maintenance', 'marketing', 'other') NOT NULL,
        description TEXT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        payment_method ENUM('cash', 'bank', 'check') DEFAULT 'cash',
        account_id INT,
        journal_entry_id INT,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
        FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
        INDEX idx_date (expense_date),
        INDEX idx_category (category)
    )");

    // إنشاء جدول الرواتب
    $pdo->exec("CREATE TABLE IF NOT EXISTS payroll (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_name VARCHAR(100) NOT NULL,
        employee_id VARCHAR(20),
        salary_month DATE NOT NULL,
        basic_salary DECIMAL(10,2) NOT NULL,
        allowances DECIMAL(10,2) DEFAULT 0,
        deductions DECIMAL(10,2) DEFAULT 0,
        net_salary DECIMAL(10,2) NOT NULL,
        payment_date DATE,
        payment_method ENUM('cash', 'bank', 'check') DEFAULT 'bank',
        journal_entry_id INT,
        user_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
        INDEX idx_month (salary_month),
        INDEX idx_employee (employee_id)
    )");

} catch (PDOException $e) {
    // في حالة فشل إنشاء الجداول، المتابعة بدون النظام المحاسبي
}

// وظائف التكامل المحاسبي
function createSaleJournalEntry($pdo, $saleId, $saleData, $userId) {
    try {
        $pdo->beginTransaction();

        // إنشاء رقم القيد
        $entryNumber = 'SALE' . date('Ymd') . str_pad($saleId, 4, '0', STR_PAD_LEFT);

        // إدراج القيد الرئيسي
        $entryStmt = $pdo->prepare("
            INSERT INTO journal_entries (entry_number, entry_date, description, reference_type, reference_id, total_debit, total_credit, status, user_id)
            VALUES (?, ?, ?, 'sale', ?, ?, ?, 'posted', ?)
        ");

        $description = "قيد بيع رقم " . str_pad($saleId, 6, '0', STR_PAD_LEFT);
        $total = $saleData['total'];

        $entryStmt->execute([
            $entryNumber,
            date('Y-m-d'),
            $description,
            $saleId,
            $total,
            $total,
            $userId
        ]);

        $journalEntryId = $pdo->lastInsertId();

        // الحصول على أرقام الحسابات
        $cashAccountId = getAccountByCode($pdo, '1111'); // الصندوق
        $salesAccountId = getAccountByCode($pdo, '4100'); // إيرادات المبيعات
        $taxAccountId = getAccountByCode($pdo, '2120'); // الضرائب المستحقة
        $inventoryAccountId = getAccountByCode($pdo, '1120'); // المخزون
        $cogsAccountId = getAccountByCode($pdo, '5100'); // تكلفة البضاعة المباعة

        $detailStmt = $pdo->prepare("
            INSERT INTO journal_entry_details (journal_entry_id, account_id, debit, credit, description)
            VALUES (?, ?, ?, ?, ?)
        ");

        // مدين: النقدية (أو العملاء حسب طريقة الدفع)
        if ($saleData['payment_method'] === 'cash') {
            $detailStmt->execute([$journalEntryId, $cashAccountId, $total, 0, 'نقدية من البيع']);
        } else {
            $customersAccountId = getAccountByCode($pdo, '1130'); // العملاء
            $detailStmt->execute([$journalEntryId, $customersAccountId, $total, 0, 'مبيعات آجلة']);
        }

        // دائن: إيرادات المبيعات
        $salesAmount = $saleData['total'] - $saleData['tax'];
        $detailStmt->execute([$journalEntryId, $salesAccountId, 0, $salesAmount, 'إيرادات المبيعات']);

        // دائن: الضرائب (إذا وجدت)
        if ($saleData['tax'] > 0) {
            $detailStmt->execute([$journalEntryId, $taxAccountId, 0, $saleData['tax'], 'ضريبة القيمة المضافة']);
        }

        // قيد تكلفة البضاعة المباعة
        $itemsStmt = $pdo->prepare("
            SELECT si.quantity, p.cost_price
            FROM sale_items si
            JOIN products p ON si.product_id = p.id
            WHERE si.sale_id = ?
        ");
        $itemsStmt->execute([$saleId]);
        $items = $itemsStmt->fetchAll();

        $totalCost = 0;
        foreach ($items as $item) {
            $totalCost += $item['quantity'] * ($item['cost_price'] ?? 0);
        }

        if ($totalCost > 0) {
            // مدين: تكلفة البضاعة المباعة
            $detailStmt->execute([$journalEntryId, $cogsAccountId, $totalCost, 0, 'تكلفة البضاعة المباعة']);
            // دائن: المخزون
            $detailStmt->execute([$journalEntryId, $inventoryAccountId, 0, $totalCost, 'خروج بضاعة من المخزون']);
        }

        // تحديث أرصدة الحسابات
        updateAccountBalances($pdo, $journalEntryId);

        $pdo->commit();
        return $journalEntryId;

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

function createPurchaseJournalEntry($pdo, $purchaseId, $purchaseData, $userId) {
    try {
        $pdo->beginTransaction();

        $entryNumber = 'PURCH' . date('Ymd') . str_pad($purchaseId, 4, '0', STR_PAD_LEFT);

        $entryStmt = $pdo->prepare("
            INSERT INTO journal_entries (entry_number, entry_date, description, reference_type, reference_id, total_debit, total_credit, status, user_id)
            VALUES (?, ?, ?, 'purchase', ?, ?, ?, 'posted', ?)
        ");

        $description = "قيد شراء رقم " . str_pad($purchaseId, 6, '0', STR_PAD_LEFT);
        $total = $purchaseData['total'];

        $entryStmt->execute([
            $entryNumber,
            date('Y-m-d'),
            $description,
            $purchaseId,
            $total,
            $total,
            $userId
        ]);

        $journalEntryId = $pdo->lastInsertId();

        // الحصول على أرقام الحسابات
        $inventoryAccountId = getAccountByCode($pdo, '1120'); // المخزون
        $suppliersAccountId = getAccountByCode($pdo, '2110'); // الموردين
        $cashAccountId = getAccountByCode($pdo, '1111'); // الصندوق

        $detailStmt = $pdo->prepare("
            INSERT INTO journal_entry_details (journal_entry_id, account_id, debit, credit, description)
            VALUES (?, ?, ?, ?, ?)
        ");

        // مدين: المخزون
        $detailStmt->execute([$journalEntryId, $inventoryAccountId, $total, 0, 'شراء بضاعة']);

        // دائن: الموردين أو النقدية
        if ($purchaseData['payment_method'] === 'cash') {
            $detailStmt->execute([$journalEntryId, $cashAccountId, 0, $total, 'دفع نقدي للمورد']);
        } else {
            $detailStmt->execute([$journalEntryId, $suppliersAccountId, 0, $total, 'شراء آجل من المورد']);
        }

        updateAccountBalances($pdo, $journalEntryId);

        $pdo->commit();
        return $journalEntryId;

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

function createExpenseJournalEntry($pdo, $expenseId, $expenseData, $userId) {
    try {
        $pdo->beginTransaction();

        $entryNumber = 'EXP' . date('Ymd') . str_pad($expenseId, 4, '0', STR_PAD_LEFT);

        $entryStmt = $pdo->prepare("
            INSERT INTO journal_entries (entry_number, entry_date, description, reference_type, reference_id, total_debit, total_credit, status, user_id)
            VALUES (?, ?, ?, 'expense', ?, ?, ?, 'posted', ?)
        ");

        $description = "مصروف: " . $expenseData['description'];
        $amount = $expenseData['amount'];

        $entryStmt->execute([
            $entryNumber,
            $expenseData['expense_date'],
            $description,
            $expenseId,
            $amount,
            $amount,
            $userId
        ]);

        $journalEntryId = $pdo->lastInsertId();

        // تحديد حساب المصروف حسب الفئة
        $expenseAccountId = getExpenseAccountByCategory($pdo, $expenseData['category']);
        $cashAccountId = getAccountByCode($pdo, '1111'); // الصندوق
        $bankAccountId = getAccountByCode($pdo, '1112'); // البنك

        $detailStmt = $pdo->prepare("
            INSERT INTO journal_entry_details (journal_entry_id, account_id, debit, credit, description)
            VALUES (?, ?, ?, ?, ?)
        ");

        // مدين: حساب المصروف
        $detailStmt->execute([$journalEntryId, $expenseAccountId, $amount, 0, $description]);

        // دائن: النقدية أو البنك
        if ($expenseData['payment_method'] === 'cash') {
            $detailStmt->execute([$journalEntryId, $cashAccountId, 0, $amount, 'دفع نقدي']);
        } else {
            $detailStmt->execute([$journalEntryId, $bankAccountId, 0, $amount, 'دفع بنكي']);
        }

        updateAccountBalances($pdo, $journalEntryId);

        $pdo->commit();
        return $journalEntryId;

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

function createPayrollJournalEntry($pdo, $payrollId, $payrollData, $userId) {
    try {
        $pdo->beginTransaction();

        $entryNumber = 'PAY' . date('Ymd') . str_pad($payrollId, 4, '0', STR_PAD_LEFT);

        $entryStmt = $pdo->prepare("
            INSERT INTO journal_entries (entry_number, entry_date, description, reference_type, reference_id, total_debit, total_credit, status, user_id)
            VALUES (?, ?, ?, 'payroll', ?, ?, ?, 'posted', ?)
        ");

        $description = "راتب الموظف: " . $payrollData['employee_name'];
        $netSalary = $payrollData['net_salary'];

        $entryStmt->execute([
            $entryNumber,
            $payrollData['payment_date'],
            $description,
            $payrollId,
            $netSalary,
            $netSalary,
            $userId
        ]);

        $journalEntryId = $pdo->lastInsertId();

        $salaryAccountId = getAccountByCode($pdo, '5210'); // مصروفات الرواتب
        $bankAccountId = getAccountByCode($pdo, '1112'); // البنك

        $detailStmt = $pdo->prepare("
            INSERT INTO journal_entry_details (journal_entry_id, account_id, debit, credit, description)
            VALUES (?, ?, ?, ?, ?)
        ");

        // مدين: مصروفات الرواتب
        $detailStmt->execute([$journalEntryId, $salaryAccountId, $netSalary, 0, $description]);

        // دائن: البنك
        $detailStmt->execute([$journalEntryId, $bankAccountId, 0, $netSalary, 'دفع راتب']);

        updateAccountBalances($pdo, $journalEntryId);

        $pdo->commit();
        return $journalEntryId;

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

function getAccountByCode($pdo, $code) {
    $stmt = $pdo->prepare("SELECT id FROM chart_of_accounts WHERE account_code = ? AND is_active = 1");
    $stmt->execute([$code]);
    $result = $stmt->fetchColumn();
    return $result ?: null;
}

function getExpenseAccountByCategory($pdo, $category) {
    $accountCodes = [
        'salary' => '5210',
        'rent' => '5220',
        'utilities' => '5230',
        'supplies' => '5240',
        'maintenance' => '5240',
        'marketing' => '5240',
        'other' => '5240'
    ];

    $code = $accountCodes[$category] ?? '5240';
    return getAccountByCode($pdo, $code);
}

function updateAccountBalances($pdo, $journalEntryId) {
    // تحديث أرصدة الحسابات بناءً على القيد
    $stmt = $pdo->prepare("
        SELECT account_id, SUM(debit) as total_debit, SUM(credit) as total_credit
        FROM journal_entry_details
        WHERE journal_entry_id = ?
        GROUP BY account_id
    ");
    $stmt->execute([$journalEntryId]);
    $details = $stmt->fetchAll();

    $updateStmt = $pdo->prepare("
        UPDATE chart_of_accounts
        SET balance = balance + ?
        WHERE id = ?
    ");

    foreach ($details as $detail) {
        $balanceChange = $detail['total_debit'] - $detail['total_credit'];
        $updateStmt->execute([$balanceChange, $detail['account_id']]);
    }
}

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'add_account') {
            $code = trim($_POST['account_code']);
            $name = trim($_POST['account_name']);
            $type = $_POST['account_type'];
            $parentId = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
            
            // التحقق من عدم تكرار الكود
            $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM chart_of_accounts WHERE account_code = ?");
            $checkStmt->execute([$code]);
            if ($checkStmt->fetchColumn() > 0) {
                throw new Exception('كود الحساب موجود مسبقاً');
            }
            
            $stmt = $pdo->prepare("INSERT INTO chart_of_accounts (account_code, account_name, account_type, parent_id) VALUES (?, ?, ?, ?)");
            $stmt->execute([$code, $name, $type, $parentId]);
            
            echo json_encode(['success' => true, 'message' => 'تم إضافة الحساب بنجاح']);
            exit;
            
        } elseif ($_POST['action'] === 'add_journal_entry') {
            $entryDate = $_POST['entry_date'];
            $description = trim($_POST['description']);
            $accounts = json_decode($_POST['accounts'], true);
            
            if (empty($accounts) || count($accounts) < 2) {
                throw new Exception('يجب أن يحتوي القيد على حسابين على الأقل');
            }
            
            // حساب المجاميع
            $totalDebit = 0;
            $totalCredit = 0;
            foreach ($accounts as $account) {
                $totalDebit += floatval($account['debit']);
                $totalCredit += floatval($account['credit']);
            }
            
            if (abs($totalDebit - $totalCredit) > 0.01) {
                throw new Exception('مجموع المدين يجب أن يساوي مجموع الدائن');
            }
            
            $pdo->beginTransaction();
            
            // إنشاء رقم القيد
            $entryNumber = 'JE' . date('Ymd') . str_pad($pdo->query("SELECT COUNT(*) + 1 FROM journal_entries WHERE DATE(entry_date) = '$entryDate'")->fetchColumn(), 3, '0', STR_PAD_LEFT);
            
            // إدراج القيد
            $entryStmt = $pdo->prepare("INSERT INTO journal_entries (entry_number, entry_date, description, total_debit, total_credit, user_id) VALUES (?, ?, ?, ?, ?, ?)");
            $entryStmt->execute([$entryNumber, $entryDate, $description, $totalDebit, $totalCredit, $_SESSION['user_id']]);
            
            $entryId = $pdo->lastInsertId();
            
            // إدراج تفاصيل القيد
            $detailStmt = $pdo->prepare("INSERT INTO journal_entry_details (journal_entry_id, account_id, debit, credit, description) VALUES (?, ?, ?, ?, ?)");
            foreach ($accounts as $account) {
                if ($account['debit'] > 0 || $account['credit'] > 0) {
                    $detailStmt->execute([
                        $entryId,
                        $account['account_id'],
                        floatval($account['debit']),
                        floatval($account['credit']),
                        $account['description'] ?? ''
                    ]);
                }
            }
            
            $pdo->commit();
            
            echo json_encode(['success' => true, 'message' => 'تم إضافة القيد المحاسبي بنجاح', 'entry_number' => $entryNumber]);
            exit;
            
        } elseif ($_POST['action'] === 'add_expense') {
            $expenseDate = $_POST['expense_date'];
            $category = $_POST['category'];
            $description = trim($_POST['description']);
            $amount = floatval($_POST['amount']);
            $paymentMethod = $_POST['payment_method'];

            if ($amount <= 0) {
                throw new Exception('مبلغ المصروف يجب أن يكون أكبر من صفر');
            }

            $pdo->beginTransaction();

            // إنشاء رقم المصروف
            $expenseNumber = 'EXP' . date('Ymd') . str_pad($pdo->query("SELECT COUNT(*) + 1 FROM expenses WHERE DATE(expense_date) = '$expenseDate'")->fetchColumn(), 3, '0', STR_PAD_LEFT);

            // إدراج المصروف
            $expenseStmt = $pdo->prepare("INSERT INTO expenses (expense_number, expense_date, category, description, amount, payment_method, user_id) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $expenseStmt->execute([$expenseNumber, $expenseDate, $category, $description, $amount, $paymentMethod, $_SESSION['user_id']]);

            $expenseId = $pdo->lastInsertId();

            // إنشاء القيد المحاسبي
            $expenseData = [
                'expense_date' => $expenseDate,
                'category' => $category,
                'description' => $description,
                'amount' => $amount,
                'payment_method' => $paymentMethod
            ];

            $journalEntryId = createExpenseJournalEntry($pdo, $expenseId, $expenseData, $_SESSION['user_id']);

            // ربط المصروف بالقيد
            $pdo->prepare("UPDATE expenses SET journal_entry_id = ? WHERE id = ?")->execute([$journalEntryId, $expenseId]);

            $pdo->commit();

            echo json_encode(['success' => true, 'message' => 'تم إضافة المصروف وإنشاء القيد المحاسبي بنجاح', 'expense_number' => $expenseNumber]);
            exit;

        } elseif ($_POST['action'] === 'add_payroll') {
            $employeeName = trim($_POST['employee_name']);
            $employeeId = trim($_POST['employee_id']);
            $salaryMonth = $_POST['salary_month'];
            $basicSalary = floatval($_POST['basic_salary']);
            $allowances = floatval($_POST['allowances']);
            $deductions = floatval($_POST['deductions']);
            $paymentDate = $_POST['payment_date'];
            $paymentMethod = $_POST['payment_method'];

            $netSalary = $basicSalary + $allowances - $deductions;

            if ($netSalary <= 0) {
                throw new Exception('صافي الراتب يجب أن يكون أكبر من صفر');
            }

            $pdo->beginTransaction();

            // إدراج الراتب
            $payrollStmt = $pdo->prepare("INSERT INTO payroll (employee_name, employee_id, salary_month, basic_salary, allowances, deductions, net_salary, payment_date, payment_method, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $payrollStmt->execute([$employeeName, $employeeId, $salaryMonth, $basicSalary, $allowances, $deductions, $netSalary, $paymentDate, $paymentMethod, $_SESSION['user_id']]);

            $payrollId = $pdo->lastInsertId();

            // إنشاء القيد المحاسبي
            $payrollData = [
                'employee_name' => $employeeName,
                'net_salary' => $netSalary,
                'payment_date' => $paymentDate
            ];

            $journalEntryId = createPayrollJournalEntry($pdo, $payrollId, $payrollData, $_SESSION['user_id']);

            // ربط الراتب بالقيد
            $pdo->prepare("UPDATE payroll SET journal_entry_id = ? WHERE id = ?")->execute([$journalEntryId, $payrollId]);

            $pdo->commit();

            echo json_encode(['success' => true, 'message' => 'تم إضافة الراتب وإنشاء القيد المحاسبي بنجاح']);
            exit;

        } elseif ($_POST['action'] === 'get_account_balance') {
            $accountId = intval($_POST['account_id']);
            $dateFrom = $_POST['date_from'] ?? '';
            $dateTo = $_POST['date_to'] ?? '';
            
            // حساب الرصيد
            $balanceQuery = "
                SELECT 
                    COALESCE(SUM(jed.debit), 0) as total_debit,
                    COALESCE(SUM(jed.credit), 0) as total_credit
                FROM journal_entry_details jed
                JOIN journal_entries je ON jed.journal_entry_id = je.id
                WHERE jed.account_id = ? AND je.status = 'posted'
            ";
            
            $params = [$accountId];
            
            if (!empty($dateFrom)) {
                $balanceQuery .= " AND je.entry_date >= ?";
                $params[] = $dateFrom;
            }
            
            if (!empty($dateTo)) {
                $balanceQuery .= " AND je.entry_date <= ?";
                $params[] = $dateTo;
            }
            
            $balanceStmt = $pdo->prepare($balanceQuery);
            $balanceStmt->execute($params);
            $balance = $balanceStmt->fetch();
            
            $netBalance = $balance['total_debit'] - $balance['total_credit'];
            
            echo json_encode([
                'success' => true,
                'debit' => $balance['total_debit'],
                'credit' => $balance['total_credit'],
                'balance' => $netBalance
            ]);
            exit;
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

// جلب دليل الحسابات
try {
    $accounts = $pdo->query("
        SELECT * FROM chart_of_accounts 
        WHERE is_active = 1 
        ORDER BY account_code
    ")->fetchAll();
} catch (PDOException $e) {
    $accounts = [];
}

// جلب القيود المحاسبية الحديثة
try {
    $journalEntries = $pdo->query("
        SELECT je.*, u.name as user_name,
               COUNT(jed.id) as details_count
        FROM journal_entries je
        LEFT JOIN users u ON je.user_id = u.id
        LEFT JOIN journal_entry_details jed ON je.id = jed.journal_entry_id
        GROUP BY je.id
        ORDER BY je.entry_date DESC, je.id DESC
        LIMIT 20
    ")->fetchAll();
} catch (PDOException $e) {
    $journalEntries = [];
}

// حساب الإحصائيات
$totalAssets = 0;
$totalLiabilities = 0;
$totalEquity = 0;
$totalRevenue = 0;
$totalExpenses = 0;

foreach ($accounts as $account) {
    switch ($account['account_type']) {
        case 'asset':
            $totalAssets += $account['balance'];
            break;
        case 'liability':
            $totalLiabilities += $account['balance'];
            break;
        case 'equity':
            $totalEquity += $account['balance'];
            break;
        case 'revenue':
            $totalRevenue += $account['balance'];
            break;
        case 'expense':
            $totalExpenses += $account['balance'];
            break;
    }
}

// إعدادات النظام
$currency = getSetting('currency', 'ر.س');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام المحاسبي - نظام نقاط البيع</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-left-color: #fff;
            transform: translateX(-5px);
        }
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .page-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .table-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }
        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 20px 20px 0 0;
        }
        .account-tree {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
        }
        .account-item {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .account-item:hover {
            background: #f8f9fa;
        }
        .account-item.selected {
            background: var(--primary-color);
            color: white;
        }
        .journal-entry-row {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
        }
        .debit-credit-input {
            text-align: right;
            font-weight: bold;
        }
        .balance-positive {
            color: var(--success-color);
        }
        .balance-negative {
            color: var(--danger-color);
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            $companyName = getSetting('company_name', 'نظام نقاط البيع');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>
        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link active" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-currency-dollar me-2"></i>النظام المحاسبي</h2>
                    <small class="text-muted">إدارة دليل الحسابات والقيود المحاسبية والتقارير المالية</small>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-gradient" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                            <i class="bi bi-plus-circle me-2"></i>حساب جديد
                        </button>
                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addJournalEntryModal">
                            <i class="bi bi-journal-plus me-2"></i>قيد محاسبي
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Summary Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalAssets, false); ?></h4>
                            <p class="mb-0 small">إجمالي الأصول</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-building"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--danger-color), #c0392b);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalLiabilities, false); ?></h4>
                            <p class="mb-0 small">إجمالي الخصوم</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-credit-card"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #2ecc71);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalRevenue, false); ?></h4>
                            <p class="mb-0 small">إجمالي الإيرادات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-arrow-up-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalExpenses, false); ?></h4>
                            <p class="mb-0 small">إجمالي المصروفات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-arrow-down-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-pie-chart me-2"></i>توزيع الأصول</h5>
                    <div class="chart-container">
                        <canvas id="assetsChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-bar-chart me-2"></i>الإيرادات مقابل المصروفات</h5>
                    <div class="chart-container">
                        <canvas id="incomeExpenseChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Tabs -->
        <div class="content-card">
            <ul class="nav nav-tabs" id="accountingTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="accounts-tab" data-bs-toggle="tab" data-bs-target="#accounts" type="button" role="tab">
                        <i class="bi bi-list-ul me-2"></i>دليل الحسابات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="journal-tab" data-bs-toggle="tab" data-bs-target="#journal" type="button" role="tab">
                        <i class="bi bi-journal-text me-2"></i>القيود المحاسبية
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="trial-balance-tab" data-bs-toggle="tab" data-bs-target="#trial-balance" type="button" role="tab">
                        <i class="bi bi-calculator me-2"></i>ميزان المراجعة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="expenses-tab" data-bs-toggle="tab" data-bs-target="#expenses" type="button" role="tab">
                        <i class="bi bi-receipt me-2"></i>المصروفات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="payroll-tab" data-bs-toggle="tab" data-bs-target="#payroll" type="button" role="tab">
                        <i class="bi bi-people me-2"></i>الرواتب
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports" type="button" role="tab">
                        <i class="bi bi-file-earmark-text me-2"></i>التقارير المالية
                    </button>
                </li>
            </ul>

            <div class="tab-content mt-4" id="accountingTabsContent">
                <!-- Chart of Accounts Tab -->
                <div class="tab-pane fade show active" id="accounts" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">دليل الحسابات</h5>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                            <i class="bi bi-plus me-1"></i>إضافة حساب
                        </button>
                    </div>

                    <div class="table-container">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="accountsTable">
                                <thead>
                                    <tr>
                                        <th width="15%">كود الحساب</th>
                                        <th width="30%">اسم الحساب</th>
                                        <th width="15%">نوع الحساب</th>
                                        <th width="15%">الرصيد</th>
                                        <th width="10%">الحالة</th>
                                        <th width="15%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($accounts as $account): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary"><?php echo htmlspecialchars($account['account_code']); ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($account['account_name']); ?></strong>
                                        </td>
                                        <td>
                                            <?php
                                            $typeClass = '';
                                            $typeText = '';
                                            switch ($account['account_type']) {
                                                case 'asset':
                                                    $typeClass = 'bg-success';
                                                    $typeText = 'أصول';
                                                    break;
                                                case 'liability':
                                                    $typeClass = 'bg-danger';
                                                    $typeText = 'خصوم';
                                                    break;
                                                case 'equity':
                                                    $typeClass = 'bg-info';
                                                    $typeText = 'حقوق ملكية';
                                                    break;
                                                case 'revenue':
                                                    $typeClass = 'bg-success';
                                                    $typeText = 'إيرادات';
                                                    break;
                                                case 'expense':
                                                    $typeClass = 'bg-warning text-dark';
                                                    $typeText = 'مصروفات';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $typeClass; ?>"><?php echo $typeText; ?></span>
                                        </td>
                                        <td>
                                            <span class="<?php echo $account['balance'] >= 0 ? 'balance-positive' : 'balance-negative'; ?>">
                                                <strong><?php echo formatCurrency($account['balance']); ?></strong>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $account['is_active'] ? 'bg-success' : 'bg-secondary'; ?>">
                                                <?php echo $account['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="viewAccountDetails(<?php echo $account['id']; ?>)"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-success"
                                                        onclick="editAccount(<?php echo htmlspecialchars(json_encode($account)); ?>)"
                                                        title="تعديل">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-info"
                                                        onclick="getAccountBalance(<?php echo $account['id']; ?>)"
                                                        title="حساب الرصيد">
                                                    <i class="bi bi-calculator"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Journal Entries Tab -->
                <div class="tab-pane fade" id="journal" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">القيود المحاسبية</h5>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addJournalEntryModal">
                            <i class="bi bi-plus me-1"></i>قيد جديد
                        </button>
                    </div>

                    <div class="table-container">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="journalTable">
                                <thead>
                                    <tr>
                                        <th width="12%">رقم القيد</th>
                                        <th width="12%">التاريخ</th>
                                        <th width="30%">الوصف</th>
                                        <th width="12%">المدين</th>
                                        <th width="12%">الدائن</th>
                                        <th width="10%">الحالة</th>
                                        <th width="12%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($journalEntries as $entry): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary"><?php echo htmlspecialchars($entry['entry_number']); ?></span>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo date('Y-m-d', strtotime($entry['entry_date'])); ?></strong>
                                                <br><small class="text-muted"><?php echo date('H:i', strtotime($entry['created_at'])); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <?php echo htmlspecialchars($entry['description']); ?>
                                                <br><small class="text-muted">بواسطة: <?php echo htmlspecialchars($entry['user_name'] ?? 'غير محدد'); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <strong class="text-success"><?php echo formatCurrency($entry['total_debit']); ?></strong>
                                        </td>
                                        <td>
                                            <strong class="text-info"><?php echo formatCurrency($entry['total_credit']); ?></strong>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = '';
                                            $statusText = '';
                                            switch ($entry['status']) {
                                                case 'draft':
                                                    $statusClass = 'bg-warning text-dark';
                                                    $statusText = 'مسودة';
                                                    break;
                                                case 'posted':
                                                    $statusClass = 'bg-success';
                                                    $statusText = 'مرحل';
                                                    break;
                                                case 'cancelled':
                                                    $statusClass = 'bg-danger';
                                                    $statusText = 'ملغي';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="viewJournalEntry(<?php echo $entry['id']; ?>)"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <?php if ($entry['status'] === 'draft'): ?>
                                                <button type="button" class="btn btn-sm btn-outline-success"
                                                        onclick="postJournalEntry(<?php echo $entry['id']; ?>)"
                                                        title="ترحيل">
                                                    <i class="bi bi-check-circle"></i>
                                                </button>
                                                <?php endif; ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteJournalEntry(<?php echo $entry['id']; ?>)"
                                                        title="حذف">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Trial Balance Tab -->
                <div class="tab-pane fade" id="trial-balance" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">ميزان المراجعة</h5>
                        <div class="d-flex gap-2">
                            <input type="date" class="form-control" id="trialBalanceDate" value="<?php echo date('Y-m-d'); ?>">
                            <button type="button" class="btn btn-primary" onclick="generateTrialBalance()">
                                <i class="bi bi-calculator me-1"></i>إنشاء
                            </button>
                        </div>
                    </div>

                    <div id="trialBalanceContent">
                        <div class="text-center py-5">
                            <i class="bi bi-calculator display-1 text-muted"></i>
                            <h5 class="text-muted mt-3">ميزان المراجعة</h5>
                            <p class="text-muted">اختر التاريخ واضغط إنشاء لعرض ميزان المراجعة</p>
                        </div>
                    </div>
                </div>

                <!-- Expenses Tab -->
                <div class="tab-pane fade" id="expenses" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">إدارة المصروفات</h5>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                            <i class="bi bi-plus me-1"></i>مصروف جديد
                        </button>
                    </div>

                    <div class="table-container">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="expensesTable">
                                <thead>
                                    <tr>
                                        <th width="12%">رقم المصروف</th>
                                        <th width="12%">التاريخ</th>
                                        <th width="15%">الفئة</th>
                                        <th width="25%">الوصف</th>
                                        <th width="12%">المبلغ</th>
                                        <th width="12%">طريقة الدفع</th>
                                        <th width="12%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم تحميل البيانات عبر AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Payroll Tab -->
                <div class="tab-pane fade" id="payroll" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">إدارة الرواتب</h5>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPayrollModal">
                            <i class="bi bi-plus me-1"></i>راتب جديد
                        </button>
                    </div>

                    <div class="table-container">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="payrollTable">
                                <thead>
                                    <tr>
                                        <th width="15%">اسم الموظف</th>
                                        <th width="10%">رقم الموظف</th>
                                        <th width="12%">شهر الراتب</th>
                                        <th width="12%">الراتب الأساسي</th>
                                        <th width="10%">البدلات</th>
                                        <th width="10%">الخصومات</th>
                                        <th width="12%">صافي الراتب</th>
                                        <th width="12%">تاريخ الدفع</th>
                                        <th width="7%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم تحميل البيانات عبر AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Financial Reports Tab -->
                <div class="tab-pane fade" id="reports" role="tabpanel">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="bi bi-file-earmark-text display-4 text-primary mb-3"></i>
                                    <h5>قائمة الدخل</h5>
                                    <p class="text-muted">عرض الإيرادات والمصروفات وصافي الربح</p>
                                    <button class="btn btn-primary" onclick="generateIncomeStatement()">
                                        <i class="bi bi-file-earmark-text me-1"></i>إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="bi bi-building display-4 text-success mb-3"></i>
                                    <h5>الميزانية العمومية</h5>
                                    <p class="text-muted">عرض الأصول والخصوم وحقوق الملكية</p>
                                    <button class="btn btn-success" onclick="generateBalanceSheet()">
                                        <i class="bi bi-building me-1"></i>إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="bi bi-cash-stack display-4 text-info mb-3"></i>
                                    <h5>قائمة التدفقات النقدية</h5>
                                    <p class="text-muted">عرض التدفقات النقدية الداخلة والخارجة</p>
                                    <button class="btn btn-info" onclick="generateCashFlow()">
                                        <i class="bi bi-cash-stack me-1"></i>إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Account Modal -->
    <div class="modal fade" id="addAccountModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>إضافة حساب جديد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="addAccountForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">كود الحساب *</label>
                            <input type="text" class="form-control" name="account_code" required
                                   placeholder="مثال: 1111" maxlength="20">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم الحساب *</label>
                            <input type="text" class="form-control" name="account_name" required
                                   placeholder="مثال: النقدية في الصندوق" maxlength="100">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع الحساب *</label>
                            <select class="form-select" name="account_type" required>
                                <option value="">اختر نوع الحساب</option>
                                <option value="asset">أصول</option>
                                <option value="liability">خصوم</option>
                                <option value="equity">حقوق ملكية</option>
                                <option value="revenue">إيرادات</option>
                                <option value="expense">مصروفات</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الحساب الأب (اختياري)</label>
                            <select class="form-select" name="parent_id">
                                <option value="">لا يوجد</option>
                                <?php foreach ($accounts as $account): ?>
                                <option value="<?php echo $account['id']; ?>">
                                    <?php echo htmlspecialchars($account['account_code'] . ' - ' . $account['account_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i>إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>حفظ الحساب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Journal Entry Modal -->
    <div class="modal fade" id="addJournalEntryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-journal-plus me-2"></i>إضافة قيد محاسبي
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="addJournalEntryForm">
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">تاريخ القيد *</label>
                                <input type="date" class="form-control" name="entry_date"
                                       value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">رقم القيد</label>
                                <input type="text" class="form-control" readonly
                                       placeholder="سيتم إنشاؤه تلقائياً">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف القيد *</label>
                            <textarea class="form-control" name="description" rows="2" required
                                      placeholder="وصف مختصر للقيد المحاسبي"></textarea>
                        </div>

                        <h6 class="mb-3">تفاصيل القيد</h6>
                        <div id="journalEntryDetails">
                            <!-- Journal entry rows will be added here -->
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addJournalEntryRow()">
                                <i class="bi bi-plus me-1"></i>إضافة سطر
                            </button>
                            <div class="d-flex gap-3">
                                <span>إجمالي المدين: <strong id="totalDebit">0.00</strong></span>
                                <span>إجمالي الدائن: <strong id="totalCredit">0.00</strong></span>
                                <span id="balanceStatus" class="badge bg-secondary">غير متوازن</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i>إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary" id="saveJournalEntry" disabled>
                            <i class="bi bi-check-circle me-1"></i>حفظ القيد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Expense Modal -->
    <div class="modal fade" id="addExpenseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-receipt me-2"></i>إضافة مصروف جديد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="addExpenseForm">
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">تاريخ المصروف *</label>
                                <input type="date" class="form-control" name="expense_date"
                                       value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">فئة المصروف *</label>
                                <select class="form-select" name="category" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="salary">رواتب</option>
                                    <option value="rent">إيجار</option>
                                    <option value="utilities">مرافق (كهرباء، ماء، هاتف)</option>
                                    <option value="supplies">مستلزمات مكتبية</option>
                                    <option value="maintenance">صيانة</option>
                                    <option value="marketing">تسويق وإعلان</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف المصروف *</label>
                            <textarea class="form-control" name="description" rows="2" required
                                      placeholder="وصف تفصيلي للمصروف"></textarea>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">المبلغ *</label>
                                <input type="number" class="form-control" name="amount" required
                                       placeholder="0.00" step="0.01" min="0.01">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">طريقة الدفع *</label>
                                <select class="form-select" name="payment_method" required>
                                    <option value="cash">نقدي</option>
                                    <option value="bank">بنكي</option>
                                    <option value="check">شيك</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i>إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>حفظ المصروف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Payroll Modal -->
    <div class="modal fade" id="addPayrollModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-people me-2"></i>إضافة راتب موظف
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="addPayrollForm">
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم الموظف *</label>
                                <input type="text" class="form-control" name="employee_name" required
                                       placeholder="الاسم الكامل للموظف" maxlength="100">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">رقم الموظف</label>
                                <input type="text" class="form-control" name="employee_id"
                                       placeholder="رقم الموظف (اختياري)" maxlength="20">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">شهر الراتب *</label>
                                <input type="month" class="form-control" name="salary_month"
                                       value="<?php echo date('Y-m'); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">تاريخ الدفع *</label>
                                <input type="date" class="form-control" name="payment_date"
                                       value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">الراتب الأساسي *</label>
                                <input type="number" class="form-control" name="basic_salary" required
                                       placeholder="0.00" step="0.01" min="0.01" onchange="calculateNetSalary()">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">البدلات</label>
                                <input type="number" class="form-control" name="allowances"
                                       placeholder="0.00" step="0.01" min="0" value="0" onchange="calculateNetSalary()">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">الخصومات</label>
                                <input type="number" class="form-control" name="deductions"
                                       placeholder="0.00" step="0.01" min="0" value="0" onchange="calculateNetSalary()">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">صافي الراتب</label>
                                <input type="text" class="form-control" id="netSalaryDisplay" readonly
                                       style="background: #e9ecef; font-weight: bold; color: #28a745;">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">طريقة الدفع *</label>
                                <select class="form-select" name="payment_method" required>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="cash">نقدي</option>
                                    <option value="check">شيك</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i>إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>حفظ الراتب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let accountsTable, journalTable;
        let currency = '<?php echo $currency; ?>';
        let journalEntryRowCount = 0;

        $(document).ready(function() {
            // Initialize DataTables
            accountsTable = $('#accountsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[0, 'asc']], // Sort by account code
                columnDefs: [
                    { orderable: false, targets: [5] } // Actions column
                ]
            });

            journalTable = $('#journalTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[1, 'desc']], // Sort by date descending
                columnDefs: [
                    { orderable: false, targets: [6] } // Actions column
                ]
            });

            // Initialize Charts
            initializeCharts();

            // Add initial journal entry rows
            addJournalEntryRow();
            addJournalEntryRow();

            // Form submissions
            $('#addAccountForm').on('submit', function(e) {
                e.preventDefault();
                addAccount();
            });

            $('#addJournalEntryForm').on('submit', function(e) {
                e.preventDefault();
                addJournalEntry();
            });

            $('#addExpenseForm').on('submit', function(e) {
                e.preventDefault();
                addExpense();
            });

            $('#addPayrollForm').on('submit', function(e) {
                e.preventDefault();
                addPayroll();
            });

            // Initialize expenses and payroll tables when tabs are shown
            $('button[data-bs-target="#expenses"]').on('shown.bs.tab', function() {
                if (!$.fn.DataTable.isDataTable('#expensesTable')) {
                    loadExpenses();
                }
            });

            $('button[data-bs-target="#payroll"]').on('shown.bs.tab', function() {
                if (!$.fn.DataTable.isDataTable('#payrollTable')) {
                    loadPayroll();
                }
            });
        });

        function initializeCharts() {
            // Assets Distribution Chart
            const assetsCtx = document.getElementById('assetsChart').getContext('2d');

            // Sample data - you might want to get real data via AJAX
            new Chart(assetsCtx, {
                type: 'doughnut',
                data: {
                    labels: ['النقدية', 'المخزون', 'العملاء', 'أصول أخرى'],
                    datasets: [{
                        data: [30, 40, 20, 10],
                        backgroundColor: [
                            '#3498db',
                            '#2ecc71',
                            '#f39c12',
                            '#e74c3c'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Income vs Expense Chart
            const incomeExpenseCtx = document.getElementById('incomeExpenseChart').getContext('2d');

            new Chart(incomeExpenseCtx, {
                type: 'bar',
                data: {
                    labels: ['الإيرادات', 'المصروفات', 'صافي الربح'],
                    datasets: [{
                        data: [<?php echo $totalRevenue; ?>, <?php echo $totalExpenses; ?>, <?php echo $totalRevenue - $totalExpenses; ?>],
                        backgroundColor: [
                            '#27ae60',
                            '#e74c3c',
                            '#3498db'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ' + currency;
                                }
                            }
                        }
                    }
                }
            });
        }

        function addAccount() {
            const formData = new FormData($('#addAccountForm')[0]);
            formData.append('action', 'add_account');

            $.post('accounting.php', Object.fromEntries(formData))
                .done(function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم بنجاح',
                            text: result.message,
                            timer: 2000,
                            showConfirmButton: false
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ', result.message, 'error');
                    }
                })
                .fail(function() {
                    Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                });
        }

        function addJournalEntryRow() {
            journalEntryRowCount++;
            const rowHtml = `
                <div class="journal-entry-row" id="journalRow${journalEntryRowCount}">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <select class="form-select account-select" name="accounts[${journalEntryRowCount}][account_id]" required>
                                <option value="">اختر الحساب</option>
                                <?php foreach ($accounts as $account): ?>
                                <option value="<?php echo $account['id']; ?>">
                                    <?php echo htmlspecialchars($account['account_code'] . ' - ' . $account['account_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="number" class="form-control debit-credit-input debit-input"
                                   name="accounts[${journalEntryRowCount}][debit]"
                                   placeholder="مدين" step="0.01" min="0"
                                   onchange="updateJournalTotals()">
                        </div>
                        <div class="col-md-2">
                            <input type="number" class="form-control debit-credit-input credit-input"
                                   name="accounts[${journalEntryRowCount}][credit]"
                                   placeholder="دائن" step="0.01" min="0"
                                   onchange="updateJournalTotals()">
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control"
                                   name="accounts[${journalEntryRowCount}][description]"
                                   placeholder="وصف السطر (اختياري)">
                        </div>
                        <div class="col-md-1">
                            <button type="button" class="btn btn-outline-danger btn-sm"
                                    onclick="removeJournalEntryRow(${journalEntryRowCount})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            $('#journalEntryDetails').append(rowHtml);
        }

        function removeJournalEntryRow(rowId) {
            $(`#journalRow${rowId}`).remove();
            updateJournalTotals();
        }

        function updateJournalTotals() {
            let totalDebit = 0;
            let totalCredit = 0;

            $('.debit-input').each(function() {
                const value = parseFloat($(this).val()) || 0;
                totalDebit += value;
            });

            $('.credit-input').each(function() {
                const value = parseFloat($(this).val()) || 0;
                totalCredit += value;
            });

            $('#totalDebit').text(totalDebit.toFixed(2));
            $('#totalCredit').text(totalCredit.toFixed(2));

            const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01 && totalDebit > 0;

            if (isBalanced) {
                $('#balanceStatus').removeClass('bg-secondary bg-danger').addClass('bg-success').text('متوازن');
                $('#saveJournalEntry').prop('disabled', false);
            } else {
                $('#balanceStatus').removeClass('bg-success').addClass('bg-danger').text('غير متوازن');
                $('#saveJournalEntry').prop('disabled', true);
            }
        }

        function addJournalEntry() {
            const formData = new FormData($('#addJournalEntryForm')[0]);

            // Collect accounts data
            const accounts = [];
            $('.journal-entry-row').each(function() {
                const accountId = $(this).find('.account-select').val();
                const debit = parseFloat($(this).find('.debit-input').val()) || 0;
                const credit = parseFloat($(this).find('.credit-input').val()) || 0;
                const description = $(this).find('input[name*="[description]"]').val();

                if (accountId && (debit > 0 || credit > 0)) {
                    accounts.push({
                        account_id: accountId,
                        debit: debit,
                        credit: credit,
                        description: description
                    });
                }
            });

            formData.append('action', 'add_journal_entry');
            formData.append('accounts', JSON.stringify(accounts));

            $.post('accounting.php', Object.fromEntries(formData))
                .done(function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم بنجاح',
                            html: `${result.message}<br><strong>رقم القيد:</strong> ${result.entry_number}`,
                            timer: 3000,
                            showConfirmButton: false
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ', result.message, 'error');
                    }
                })
                .fail(function() {
                    Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                });
        }

        function viewAccountDetails(accountId) {
            // Implementation for viewing account details
            Swal.fire('معلومة', 'ميزة عرض تفاصيل الحساب قيد التطوير', 'info');
        }

        function editAccount(account) {
            // Implementation for editing account
            Swal.fire('معلومة', 'ميزة تعديل الحساب قيد التطوير', 'info');
        }

        function getAccountBalance(accountId) {
            // Implementation for getting account balance
            Swal.fire('معلومة', 'ميزة حساب الرصيد قيد التطوير', 'info');
        }

        function viewJournalEntry(entryId) {
            // Implementation for viewing journal entry details
            Swal.fire('معلومة', 'ميزة عرض تفاصيل القيد قيد التطوير', 'info');
        }

        function postJournalEntry(entryId) {
            // Implementation for posting journal entry
            Swal.fire('معلومة', 'ميزة ترحيل القيد قيد التطوير', 'info');
        }

        function deleteJournalEntry(entryId) {
            // Implementation for deleting journal entry
            Swal.fire('معلومة', 'ميزة حذف القيد قيد التطوير', 'info');
        }

        function generateTrialBalance() {
            // Implementation for generating trial balance
            Swal.fire('معلومة', 'ميزة ميزان المراجعة قيد التطوير', 'info');
        }

        function generateIncomeStatement() {
            window.open('income_statement.php', '_blank');
        }

        function generateBalanceSheet() {
            window.open('balance_sheet.php', '_blank');
        }

        function generateCashFlow() {
            window.open('cash_flow.php', '_blank');
        }

        function addExpense() {
            const formData = new FormData($('#addExpenseForm')[0]);
            formData.append('action', 'add_expense');

            $.post('accounting.php', Object.fromEntries(formData))
                .done(function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم بنجاح',
                            html: `${result.message}<br><strong>رقم المصروف:</strong> ${result.expense_number}`,
                            timer: 3000,
                            showConfirmButton: false
                        }).then(() => {
                            $('#addExpenseModal').modal('hide');
                            loadExpenses();
                        });
                    } else {
                        Swal.fire('خطأ', result.message, 'error');
                    }
                })
                .fail(function() {
                    Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                });
        }

        function addPayroll() {
            const formData = new FormData($('#addPayrollForm')[0]);
            formData.append('action', 'add_payroll');

            $.post('accounting.php', Object.fromEntries(formData))
                .done(function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم بنجاح',
                            text: result.message,
                            timer: 3000,
                            showConfirmButton: false
                        }).then(() => {
                            $('#addPayrollModal').modal('hide');
                            loadPayroll();
                        });
                    } else {
                        Swal.fire('خطأ', result.message, 'error');
                    }
                })
                .fail(function() {
                    Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                });
        }

        function loadExpenses() {
            // Load expenses data via AJAX
            $.get('get_expenses.php')
                .done(function(data) {
                    const expenses = JSON.parse(data);

                    $('#expensesTable').DataTable({
                        language: {
                            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                        },
                        data: expenses,
                        columns: [
                            { data: 'expense_number' },
                            { data: 'expense_date' },
                            {
                                data: 'category',
                                render: function(data) {
                                    const categories = {
                                        'salary': 'رواتب',
                                        'rent': 'إيجار',
                                        'utilities': 'مرافق',
                                        'supplies': 'مستلزمات',
                                        'maintenance': 'صيانة',
                                        'marketing': 'تسويق',
                                        'other': 'أخرى'
                                    };
                                    return categories[data] || data;
                                }
                            },
                            { data: 'description' },
                            {
                                data: 'amount',
                                render: function(data) {
                                    return parseFloat(data).toFixed(2) + ' ' + currency;
                                }
                            },
                            {
                                data: 'payment_method',
                                render: function(data) {
                                    const methods = {
                                        'cash': 'نقدي',
                                        'bank': 'بنكي',
                                        'check': 'شيك'
                                    };
                                    return methods[data] || data;
                                }
                            },
                            {
                                data: null,
                                render: function(data, type, row) {
                                    return `
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                    onclick="viewExpense(${row.id})" title="عرض">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteExpense(${row.id})" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    `;
                                }
                            }
                        ],
                        responsive: true,
                        pageLength: 25,
                        order: [[1, 'desc']]
                    });
                })
                .fail(function() {
                    console.error('فشل في تحميل بيانات المصروفات');
                });
        }

        function loadPayroll() {
            // Load payroll data via AJAX
            $.get('get_payroll.php')
                .done(function(data) {
                    const payroll = JSON.parse(data);

                    $('#payrollTable').DataTable({
                        language: {
                            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                        },
                        data: payroll,
                        columns: [
                            { data: 'employee_name' },
                            { data: 'employee_id' },
                            {
                                data: 'salary_month',
                                render: function(data) {
                                    return new Date(data).toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
                                }
                            },
                            {
                                data: 'basic_salary',
                                render: function(data) {
                                    return parseFloat(data).toFixed(2) + ' ' + currency;
                                }
                            },
                            {
                                data: 'allowances',
                                render: function(data) {
                                    return parseFloat(data).toFixed(2) + ' ' + currency;
                                }
                            },
                            {
                                data: 'deductions',
                                render: function(data) {
                                    return parseFloat(data).toFixed(2) + ' ' + currency;
                                }
                            },
                            {
                                data: 'net_salary',
                                render: function(data) {
                                    return '<strong>' + parseFloat(data).toFixed(2) + ' ' + currency + '</strong>';
                                }
                            },
                            { data: 'payment_date' },
                            {
                                data: null,
                                render: function(data, type, row) {
                                    return `
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                    onclick="viewPayroll(${row.id})" title="عرض">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="deletePayroll(${row.id})" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    `;
                                }
                            }
                        ],
                        responsive: true,
                        pageLength: 25,
                        order: [[7, 'desc']]
                    });
                })
                .fail(function() {
                    console.error('فشل في تحميل بيانات الرواتب');
                });
        }

        function calculateNetSalary() {
            const basicSalary = parseFloat($('input[name="basic_salary"]').val()) || 0;
            const allowances = parseFloat($('input[name="allowances"]').val()) || 0;
            const deductions = parseFloat($('input[name="deductions"]').val()) || 0;

            const netSalary = basicSalary + allowances - deductions;
            $('#netSalaryDisplay').val(netSalary.toFixed(2) + ' ' + currency);
        }

        function viewExpense(expenseId) {
            Swal.fire('معلومة', 'ميزة عرض تفاصيل المصروف قيد التطوير', 'info');
        }

        function deleteExpense(expenseId) {
            Swal.fire('معلومة', 'ميزة حذف المصروف قيد التطوير', 'info');
        }

        function viewPayroll(payrollId) {
            Swal.fire('معلومة', 'ميزة عرض تفاصيل الراتب قيد التطوير', 'info');
        }

        function deletePayroll(payrollId) {
            Swal.fire('معلومة', 'ميزة حذف الراتب قيد التطوير', 'info');
        }

        // Clear form when modal is hidden
        $('#addAccountModal').on('hidden.bs.modal', function() {
            $('#addAccountForm')[0].reset();
        });

        $('#addJournalEntryModal').on('hidden.bs.modal', function() {
            $('#addJournalEntryForm')[0].reset();
            $('#journalEntryDetails').empty();
            journalEntryRowCount = 0;
            addJournalEntryRow();
            addJournalEntryRow();
        });

        $('#addExpenseModal').on('hidden.bs.modal', function() {
            $('#addExpenseForm')[0].reset();
        });

        $('#addPayrollModal').on('hidden.bs.modal', function() {
            $('#addPayrollForm')[0].reset();
            $('#netSalaryDisplay').val('');
        });
    </script>
</body>
</html>
