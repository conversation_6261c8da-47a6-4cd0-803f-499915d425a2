<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'get_sale_details') {
            $saleId = intval($_POST['sale_id']);
            
            // جلب تفاصيل البيع
            $saleStmt = $pdo->prepare("
                SELECT s.*, 
                       c.name as customer_name,
                       c.phone as customer_phone,
                       u.name as user_name
                FROM sales s 
                LEFT JOIN customers c ON s.customer_id = c.id 
                LEFT JOIN users u ON s.user_id = u.id 
                WHERE s.id = ?
            ");
            $saleStmt->execute([$saleId]);
            $sale = $saleStmt->fetch();
            
            if (!$sale) {
                throw new Exception('البيع غير موجود');
            }
            
            // جلب عناصر البيع
            $itemsStmt = $pdo->prepare("
                SELECT si.*, p.name as product_name, p.code as product_code,
                       u.name as unit_name
                FROM sale_items si 
                LEFT JOIN products p ON si.product_id = p.id 
                LEFT JOIN units u ON p.unit_id = u.id
                WHERE si.sale_id = ?
                ORDER BY p.name
            ");
            $itemsStmt->execute([$saleId]);
            $items = $itemsStmt->fetchAll();
            
            echo json_encode([
                'success' => true,
                'sale' => $sale,
                'items' => $items
            ]);
            exit;
            
        } elseif ($_POST['action'] === 'delete_sale') {
            $saleId = intval($_POST['sale_id']);
            
            $pdo->beginTransaction();
            
            // التحقق من وجود إرجاعات مرتبطة
            $returnsCheck = $pdo->prepare("SELECT COUNT(*) FROM returns WHERE type = 'sale' AND reference_id = ?");
            $returnsCheck->execute([$saleId]);
            $returnsCount = $returnsCheck->fetchColumn();
            
            if ($returnsCount > 0) {
                throw new Exception('لا يمكن حذف هذا البيع لأنه يحتوي على إرجاعات مرتبطة');
            }
            
            // جلب عناصر البيع لإرجاع المخزون
            $itemsStmt = $pdo->prepare("SELECT product_id, quantity FROM sale_items WHERE sale_id = ?");
            $itemsStmt->execute([$saleId]);
            $items = $itemsStmt->fetchAll();
            
            // إرجاع المخزون
            $stockStmt = $pdo->prepare("UPDATE products SET quantity = quantity + ? WHERE id = ?");
            $movementStmt = $pdo->prepare("INSERT INTO stock_movements (product_id, type, reference_id, quantity, user_id) VALUES (?, 'sale_cancel', ?, ?, ?)");
            
            foreach ($items as $item) {
                $stockStmt->execute([$item['quantity'], $item['product_id']]);
                $movementStmt->execute([$item['product_id'], $saleId, $item['quantity'], $_SESSION['user_id']]);
            }
            
            // حذف عناصر البيع
            $pdo->prepare("DELETE FROM sale_items WHERE sale_id = ?")->execute([$saleId]);
            
            // حذف البيع
            $pdo->prepare("DELETE FROM sales WHERE id = ?")->execute([$saleId]);
            
            $pdo->commit();
            
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف البيع وإرجاع المخزون بنجاح'
            ]);
            exit;
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
        exit;
    }
}

// فلترة البيانات
$dateFrom = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
$dateTo = $_GET['date_to'] ?? date('Y-m-d');
$customerId = $_GET['customer_id'] ?? '';
$userId = $_GET['user_id'] ?? '';
$paymentMethod = $_GET['payment_method'] ?? '';

// بناء استعلام المبيعات
$whereConditions = ["DATE(s.sale_date) BETWEEN ? AND ?"];
$params = [$dateFrom, $dateTo];

if (!empty($customerId)) {
    $whereConditions[] = "s.customer_id = ?";
    $params[] = $customerId;
}

if (!empty($userId)) {
    $whereConditions[] = "s.user_id = ?";
    $params[] = $userId;
}

if (!empty($paymentMethod)) {
    $whereConditions[] = "s.payment_method = ?";
    $params[] = $paymentMethod;
}

$whereClause = implode(' AND ', $whereConditions);

// جلب المبيعات
try {
    $salesQuery = "
        SELECT s.*, 
               c.name as customer_name,
               c.phone as customer_phone,
               u.name as user_name,
               COUNT(si.id) as items_count
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id 
        LEFT JOIN users u ON s.user_id = u.id
        LEFT JOIN sale_items si ON s.id = si.sale_id
        WHERE $whereClause
        GROUP BY s.id
        ORDER BY s.sale_date DESC
    ";
    $salesStmt = $pdo->prepare($salesQuery);
    $salesStmt->execute($params);
    $sales = $salesStmt->fetchAll();
} catch (PDOException $e) {
    $sales = [];
}

// حساب الإحصائيات
$totalSales = count($sales);
$totalAmount = array_sum(array_column($sales, 'total'));
$totalDiscount = array_sum(array_column($sales, 'discount'));
$totalTax = array_sum(array_column($sales, 'tax'));

// إحصائيات طرق الدفع
$paymentStats = [];
foreach ($sales as $sale) {
    $method = $sale['payment_method'] ?? 'cash';
    if (!isset($paymentStats[$method])) {
        $paymentStats[$method] = ['count' => 0, 'total' => 0];
    }
    $paymentStats[$method]['count']++;
    $paymentStats[$method]['total'] += $sale['total'];
}

// جلب العملاء والمستخدمين للفلترة
try {
    $customers = $pdo->query("SELECT id, name FROM customers WHERE status = 1 ORDER BY name")->fetchAll();
    $users = $pdo->query("SELECT id, name FROM users ORDER BY name")->fetchAll();
} catch (PDOException $e) {
    $customers = [];
    $users = [];
}

// إعدادات النظام
$currency = getSetting('currency', 'ر.س');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل المبيعات - نظام نقاط البيع</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-left-color: #fff;
            transform: translateX(-5px);
        }
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .page-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .table-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }
        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 20px 20px 0 0;
        }
        .filter-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            $companyName = getSetting('company_name', 'نظام نقاط البيع');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>
        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link active" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-graph-up me-2"></i>سجل المبيعات</h2>
                    <small class="text-muted">عرض وإدارة جميع عمليات البيع والتحليلات المالية</small>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <a href="pos.php" class="btn btn-gradient">
                            <i class="bi bi-cart me-2"></i>نقطة البيع
                        </a>
                        <a href="review_sale.php" class="btn btn-outline-primary">
                            <i class="bi bi-receipt me-2"></i>مراجعة فاتورة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $totalSales; ?></h4>
                            <p class="mb-0 small">إجمالي المبيعات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-receipt"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #2ecc71);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalAmount, false); ?></h4>
                            <p class="mb-0 small">إجمالي المبلغ</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalDiscount, false); ?></h4>
                            <p class="mb-0 small">إجمالي الخصومات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-percent"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalTax, false); ?></h4>
                            <p class="mb-0 small">إجمالي الضرائب</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-calculator"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-bar-chart me-2"></i>مبيعات آخر 7 أيام</h5>
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-pie-chart me-2"></i>طرق الدفع</h5>
                    <div class="chart-container">
                        <canvas id="paymentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
