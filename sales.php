<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';
require 'check_permissions.php';
require 'apply_permissions.php';

// التحقق من صلاحيات الوصول لسجل المبيعات
applyPagePermissions($pdo, $_SESSION['user_id'], 'sales.php');

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'get_sale_details') {
            $saleId = intval($_POST['sale_id']);
            
            // جلب تفاصيل البيع
            $saleStmt = $pdo->prepare("
                SELECT s.*, 
                       c.name as customer_name,
                       c.phone as customer_phone,
                       u.name as user_name
                FROM sales s 
                LEFT JOIN customers c ON s.customer_id = c.id 
                LEFT JOIN users u ON s.user_id = u.id 
                WHERE s.id = ?
            ");
            $saleStmt->execute([$saleId]);
            $sale = $saleStmt->fetch();
            
            if (!$sale) {
                throw new Exception('البيع غير موجود');
            }
            
            // جلب عناصر البيع
            $itemsStmt = $pdo->prepare("
                SELECT si.*, p.name as product_name, p.code as product_code,
                       u.name as unit_name
                FROM sale_items si 
                LEFT JOIN products p ON si.product_id = p.id 
                LEFT JOIN units u ON p.unit_id = u.id
                WHERE si.sale_id = ?
                ORDER BY p.name
            ");
            $itemsStmt->execute([$saleId]);
            $items = $itemsStmt->fetchAll();
            
            echo json_encode([
                'success' => true,
                'sale' => $sale,
                'items' => $items
            ]);
            exit;
            
        } elseif ($_POST['action'] === 'delete_sale') {
            // التحقق من صلاحية حذف المبيعات
            if (!hasPermission($pdo, $_SESSION['user_id'], 'sales.delete') && !isAdmin($pdo, $_SESSION['user_id'])) {
                throw new Exception('ليس لديك صلاحية لحذف المبيعات');
            }

            $saleId = intval($_POST['sale_id']);

            // تسجيل عملية الحذف
            logUserAction($pdo, $_SESSION['user_id'], 'sale_delete', 'Sale ID: ' . $saleId);
            
            $pdo->beginTransaction();
            
            // التحقق من وجود إرجاعات مرتبطة
            $returnsCheck = $pdo->prepare("SELECT COUNT(*) FROM returns WHERE type = 'sale' AND reference_id = ?");
            $returnsCheck->execute([$saleId]);
            $returnsCount = $returnsCheck->fetchColumn();
            
            if ($returnsCount > 0) {
                throw new Exception('لا يمكن حذف هذا البيع لأنه يحتوي على إرجاعات مرتبطة');
            }
            
            // جلب عناصر البيع لإرجاع المخزون
            $itemsStmt = $pdo->prepare("SELECT product_id, quantity FROM sale_items WHERE sale_id = ?");
            $itemsStmt->execute([$saleId]);
            $items = $itemsStmt->fetchAll();
            
            // إرجاع المخزون
            $stockStmt = $pdo->prepare("UPDATE products SET quantity = quantity + ? WHERE id = ?");
            $movementStmt = $pdo->prepare("INSERT INTO stock_movements (product_id, type, reference_id, quantity, user_id) VALUES (?, 'sale_cancel', ?, ?, ?)");
            
            foreach ($items as $item) {
                $stockStmt->execute([$item['quantity'], $item['product_id']]);
                $movementStmt->execute([$item['product_id'], $saleId, $item['quantity'], $_SESSION['user_id']]);
            }
            
            // حذف عناصر البيع
            $pdo->prepare("DELETE FROM sale_items WHERE sale_id = ?")->execute([$saleId]);
            
            // حذف البيع
            $pdo->prepare("DELETE FROM sales WHERE id = ?")->execute([$saleId]);
            
            $pdo->commit();
            
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف البيع وإرجاع المخزون بنجاح'
            ]);
            exit;
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
        exit;
    }
}

// فلترة البيانات
$dateFrom = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
$dateTo = $_GET['date_to'] ?? date('Y-m-d');
$customerId = $_GET['customer_id'] ?? '';
$userId = $_GET['user_id'] ?? '';
$paymentMethod = $_GET['payment_method'] ?? '';

// بناء استعلام المبيعات
$whereConditions = ["DATE(s.sale_date) BETWEEN ? AND ?"];
$params = [$dateFrom, $dateTo];

if (!empty($customerId)) {
    $whereConditions[] = "s.customer_id = ?";
    $params[] = $customerId;
}

if (!empty($userId)) {
    $whereConditions[] = "s.user_id = ?";
    $params[] = $userId;
}

if (!empty($paymentMethod)) {
    $whereConditions[] = "s.payment_method = ?";
    $params[] = $paymentMethod;
}

$whereClause = implode(' AND ', $whereConditions);

// جلب المبيعات
try {
    $salesQuery = "
        SELECT s.*, 
               c.name as customer_name,
               c.phone as customer_phone,
               u.name as user_name,
               COUNT(si.id) as items_count
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id 
        LEFT JOIN users u ON s.user_id = u.id
        LEFT JOIN sale_items si ON s.id = si.sale_id
        WHERE $whereClause
        GROUP BY s.id
        ORDER BY s.sale_date DESC
    ";
    $salesStmt = $pdo->prepare($salesQuery);
    $salesStmt->execute($params);
    $sales = $salesStmt->fetchAll();
} catch (PDOException $e) {
    $sales = [];
}

// حساب الإحصائيات
$totalSales = count($sales);
$totalAmount = array_sum(array_column($sales, 'total'));
$totalDiscount = array_sum(array_column($sales, 'discount'));
$totalTax = array_sum(array_column($sales, 'tax'));

// إحصائيات طرق الدفع
$paymentStats = [];
foreach ($sales as $sale) {
    $method = $sale['payment_method'] ?? 'cash';
    if (!isset($paymentStats[$method])) {
        $paymentStats[$method] = ['count' => 0, 'total' => 0];
    }
    $paymentStats[$method]['count']++;
    $paymentStats[$method]['total'] += $sale['total'];
}

// جلب العملاء والمستخدمين للفلترة
try {
    $customers = $pdo->query("SELECT id, name FROM customers WHERE status = 1 ORDER BY name")->fetchAll();
    $users = $pdo->query("SELECT id, name FROM users ORDER BY name")->fetchAll();
} catch (PDOException $e) {
    $customers = [];
    $users = [];
}

// إعدادات النظام
$currency = getSetting('currency', 'ر.س');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل المبيعات - نظام نقاط البيع</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(255,255,255,0.05);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-left-color: #fff;
            transform: translateX(-5px);
        }
        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .page-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.8));
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .table-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }
        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 20px 20px 0 0;
        }
        .filter-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <?php applySystemSettings(); ?>
</head>
<body>
    <?php
    require 'sidebar.php';
    renderSidebar($pdo, $_SESSION['user_id'], 'sales.php');
    ?>

    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-graph-up me-2"></i>سجل المبيعات</h2>
                    <small class="text-muted">عرض وإدارة جميع عمليات البيع والتحليلات المالية</small>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <a href="pos.php" class="btn btn-gradient">
                            <i class="bi bi-cart me-2"></i>نقطة البيع
                        </a>
                        <a href="review_sale.php" class="btn btn-outline-primary">
                            <i class="bi bi-receipt me-2"></i>مراجعة فاتورة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $totalSales; ?></h4>
                            <p class="mb-0 small">إجمالي المبيعات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-receipt"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #2ecc71);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalAmount, false); ?></h4>
                            <p class="mb-0 small">إجمالي المبلغ</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalDiscount, false); ?></h4>
                            <p class="mb-0 small">إجمالي الخصومات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-percent"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalTax, false); ?></h4>
                            <p class="mb-0 small">إجمالي الضرائب</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-calculator"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-bar-chart me-2"></i>مبيعات آخر 7 أيام</h5>
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-pie-chart me-2"></i>طرق الدفع</h5>
                    <div class="chart-container">
                        <canvas id="paymentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-card">
            <form method="GET" id="filterForm">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" name="date_from" value="<?php echo $dateFrom; ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" name="date_to" value="<?php echo $dateTo; ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">العميل</label>
                        <select class="form-select" name="customer_id">
                            <option value="">جميع العملاء</option>
                            <?php foreach ($customers as $customer): ?>
                            <option value="<?php echo $customer['id']; ?>" <?php echo $customerId == $customer['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($customer['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الكاشير</label>
                        <select class="form-select" name="user_id">
                            <option value="">جميع الكاشيرين</option>
                            <?php foreach ($users as $user): ?>
                            <option value="<?php echo $user['id']; ?>" <?php echo $userId == $user['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($user['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">طريقة الدفع</label>
                        <select class="form-select" name="payment_method">
                            <option value="">جميع الطرق</option>
                            <option value="cash" <?php echo $paymentMethod === 'cash' ? 'selected' : ''; ?>>نقدي</option>
                            <option value="card" <?php echo $paymentMethod === 'card' ? 'selected' : ''; ?>>بطاقة ائتمان</option>
                            <option value="transfer" <?php echo $paymentMethod === 'transfer' ? 'selected' : ''; ?>>تحويل بنكي</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-funnel me-1"></i>تطبيق الفلتر
                        </button>
                        <a href="sales.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                        </a>
                        <button type="button" class="btn btn-success" onclick="exportSales()">
                            <i class="bi bi-download me-1"></i>تصدير Excel
                        </button>
                        <button type="button" class="btn btn-info" onclick="printReport()">
                            <i class="bi bi-printer me-1"></i>طباعة التقرير
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Sales Table -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0"><i class="bi bi-table me-2"></i>قائمة المبيعات</h5>
                <div class="d-flex gap-2">
                    <span class="badge bg-primary">عدد النتائج: <?php echo $totalSales; ?></span>
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshTable()">
                        <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                    </button>
                </div>
            </div>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="salesTable">
                        <thead>
                            <tr>
                                <th width="8%">رقم الفاتورة</th>
                                <th width="12%">التاريخ</th>
                                <th width="15%">العميل</th>
                                <th width="10%">الكاشير</th>
                                <th width="8%">عدد المنتجات</th>
                                <th width="10%">المجموع الفرعي</th>
                                <th width="8%">الخصم</th>
                                <th width="8%">الضريبة</th>
                                <th width="10%">الإجمالي</th>
                                <th width="8%">طريقة الدفع</th>
                                <th width="15%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($sales as $sale): ?>
                            <?php
                            $subtotal = $sale['total'] - $sale['tax'] + $sale['discount'];
                            ?>
                            <tr>
                                <td>
                                    <span class="badge bg-primary">#<?php echo str_pad($sale['id'], 6, '0', STR_PAD_LEFT); ?></span>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo date('Y-m-d', strtotime($sale['sale_date'])); ?></strong>
                                        <br><small class="text-muted"><?php echo date('H:i', strtotime($sale['sale_date'])); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($sale['customer_name'])): ?>
                                        <div>
                                            <strong><?php echo htmlspecialchars($sale['customer_name']); ?></strong>
                                            <?php if (!empty($sale['customer_phone'])): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($sale['customer_phone']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">عميل عادي</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo htmlspecialchars($sale['user_name'] ?? 'غير محدد'); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo $sale['items_count']; ?> منتج</span>
                                </td>
                                <td>
                                    <strong><?php echo formatCurrency($subtotal); ?></strong>
                                </td>
                                <td>
                                    <?php if ($sale['discount'] > 0): ?>
                                        <span class="text-danger">-<?php echo formatCurrency($sale['discount']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($sale['tax'] > 0): ?>
                                        <span class="text-info"><?php echo formatCurrency($sale['tax']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong class="text-success"><?php echo formatCurrency($sale['total']); ?></strong>
                                </td>
                                <td>
                                    <?php
                                    $paymentClass = 'bg-success';
                                    $paymentText = 'نقدي';

                                    switch ($sale['payment_method']) {
                                        case 'card':
                                            $paymentClass = 'bg-primary';
                                            $paymentText = 'بطاقة';
                                            break;
                                        case 'transfer':
                                            $paymentClass = 'bg-info';
                                            $paymentText = 'تحويل';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $paymentClass; ?>"><?php echo $paymentText; ?></span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="viewSaleDetails(<?php echo $sale['id']; ?>)"
                                                title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success"
                                                onclick="printSale(<?php echo $sale['id']; ?>)"
                                                title="طباعة الفاتورة">
                                            <i class="bi bi-printer"></i>
                                        </button>
                                        <a href="review_sale.php" class="btn btn-sm btn-outline-info"
                                           target="_blank" title="مراجعة الفاتورة">
                                            <i class="bi bi-receipt"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="deleteSale(<?php echo $sale['id']; ?>, '<?php echo str_pad($sale['id'], 6, '0', STR_PAD_LEFT); ?>')"
                                                title="حذف البيع">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <?php if (empty($sales)): ?>
            <div class="text-center py-5">
                <i class="bi bi-receipt display-1 text-muted"></i>
                <h5 class="text-muted mt-3">لا توجد مبيعات</h5>
                <p class="text-muted">لا توجد مبيعات في الفترة المحددة</p>
                <a href="pos.php" class="btn btn-primary">
                    <i class="bi bi-cart me-1"></i>بدء البيع
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Sale Details Modal -->
    <div class="modal fade" id="saleDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-receipt me-2"></i>تفاصيل البيع
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="saleDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>إغلاق
                    </button>
                    <button type="button" class="btn btn-primary" onclick="printCurrentSale()">
                        <i class="bi bi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Buttons -->
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let salesTable;
        let currentSaleId = null;
        let currency = '<?php echo $currency; ?>';

        $(document).ready(function() {
            // Initialize DataTable
            salesTable = $('#salesTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[1, 'desc']], // Sort by date descending
                columnDefs: [
                    { orderable: false, targets: [10] }, // Actions column
                    { searchable: false, targets: [0, 10] } // ID and Actions columns
                ],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="bi bi-file-earmark-excel me-1"></i>Excel',
                        className: 'btn btn-success btn-sm',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9] // Exclude Actions
                        }
                    }
                ]
            });

            // Initialize Charts
            initializeCharts();
        });

        function initializeCharts() {
            // Sales Chart (Last 7 days)
            const salesCtx = document.getElementById('salesChart').getContext('2d');

            // Generate last 7 days data
            const last7Days = [];
            const salesData = [];

            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                last7Days.push(date.toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' }));

                // Calculate sales for this date (simplified - you might want to get real data via AJAX)
                const dayTotal = Math.random() * 10000 + 2000; // Placeholder data
                salesData.push(dayTotal);
            }

            new Chart(salesCtx, {
                type: 'bar',
                data: {
                    labels: last7Days,
                    datasets: [{
                        label: 'المبيعات',
                        data: salesData,
                        backgroundColor: 'rgba(52, 152, 219, 0.8)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ' + currency;
                                }
                            }
                        }
                    }
                }
            });

            // Payment Methods Chart
            const paymentCtx = document.getElementById('paymentChart').getContext('2d');

            const paymentData = <?php echo json_encode($paymentStats); ?>;
            const labels = [];
            const data = [];
            const colors = ['#27ae60', '#3498db', '#f39c12'];

            let colorIndex = 0;
            for (const [method, stats] of Object.entries(paymentData)) {
                let methodName = 'نقدي';
                switch (method) {
                    case 'card': methodName = 'بطاقة ائتمان'; break;
                    case 'transfer': methodName = 'تحويل بنكي'; break;
                }
                labels.push(methodName);
                data.push(stats.total);
                colorIndex++;
            }

            new Chart(paymentCtx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: colors.slice(0, labels.length),
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed.toLocaleString() + ' ' + currency;
                                }
                            }
                        }
                    }
                }
            });
        }

        function viewSaleDetails(saleId) {
            currentSaleId = saleId;

            // Show loading
            $('#saleDetailsContent').html(`
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل تفاصيل البيع...</p>
                </div>
            `);

            $('#saleDetailsModal').modal('show');

            // Load sale details
            $.post('sales.php', {
                action: 'get_sale_details',
                sale_id: saleId
            })
            .done(function(response) {
                const result = JSON.parse(response);
                if (result.success) {
                    displaySaleDetails(result.sale, result.items);
                } else {
                    $('#saleDetailsContent').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            ${result.message}
                        </div>
                    `);
                }
            })
            .fail(function() {
                $('#saleDetailsContent').html(`
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        حدث خطأ في تحميل البيانات
                    </div>
                `);
            });
        }

        function displaySaleDetails(sale, items) {
            let itemsHtml = '';
            let subtotal = 0;

            items.forEach(item => {
                const itemTotal = item.quantity * item.unit_price;
                subtotal += itemTotal;

                itemsHtml += `
                    <tr>
                        <td>
                            <strong>${item.product_name}</strong>
                            ${item.product_code ? `<br><small class="text-muted">كود: ${item.product_code}</small>` : ''}
                        </td>
                        <td class="text-center">${item.quantity} ${item.unit_name || ''}</td>
                        <td class="text-end">${parseFloat(item.unit_price).toFixed(2)} ${currency}</td>
                        <td class="text-end"><strong>${itemTotal.toFixed(2)} ${currency}</strong></td>
                    </tr>
                `;
            });

            const content = `
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>معلومات البيع</h6>
                        <table class="table table-sm">
                            <tr><td><strong>رقم الفاتورة:</strong></td><td>#${String(sale.id).padStart(6, '0')}</td></tr>
                            <tr><td><strong>التاريخ:</strong></td><td>${new Date(sale.sale_date).toLocaleString('ar-SA')}</td></tr>
                            <tr><td><strong>الكاشير:</strong></td><td>${sale.user_name || 'غير محدد'}</td></tr>
                            <tr><td><strong>طريقة الدفع:</strong></td><td>${getPaymentMethodText(sale.payment_method)}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات العميل</h6>
                        <table class="table table-sm">
                            <tr><td><strong>الاسم:</strong></td><td>${sale.customer_name || 'عميل عادي'}</td></tr>
                            <tr><td><strong>الهاتف:</strong></td><td>${sale.customer_phone || '-'}</td></tr>
                        </table>
                        ${sale.notes ? `<div class="mt-3"><strong>ملاحظات:</strong><br>${sale.notes}</div>` : ''}
                    </div>
                </div>

                <h6>المنتجات</h6>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th class="text-center">الكمية</th>
                                <th class="text-end">السعر</th>
                                <th class="text-end">الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${itemsHtml}
                        </tbody>
                    </table>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6 ms-auto">
                        <table class="table">
                            <tr><td>المجموع الفرعي:</td><td class="text-end">${subtotal.toFixed(2)} ${currency}</td></tr>
                            <tr><td>الخصم:</td><td class="text-end text-danger">-${parseFloat(sale.discount || 0).toFixed(2)} ${currency}</td></tr>
                            <tr><td>الضريبة:</td><td class="text-end">${parseFloat(sale.tax || 0).toFixed(2)} ${currency}</td></tr>
                            <tr class="table-success"><td><strong>الإجمالي:</strong></td><td class="text-end"><strong>${parseFloat(sale.total).toFixed(2)} ${currency}</strong></td></tr>
                        </table>
                    </div>
                </div>
            `;

            $('#saleDetailsContent').html(content);
        }

        function getPaymentMethodText(method) {
            switch (method) {
                case 'card': return 'بطاقة ائتمان';
                case 'transfer': return 'تحويل بنكي';
                default: return 'نقدي';
            }
        }

        function printSale(saleId) {
            window.open(`get_receipt.php?id=${saleId}`, '_blank');
        }

        function printCurrentSale() {
            if (currentSaleId) {
                printSale(currentSaleId);
            }
        }

        function deleteSale(saleId, saleNumber) {
            Swal.fire({
                title: 'تأكيد الحذف',
                html: `
                    <div class="text-start">
                        <p>هل أنت متأكد من حذف البيع رقم <strong>#${saleNumber}</strong>؟</p>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>تحذير:</strong> سيتم إرجاع جميع المنتجات إلى المخزون
                        </div>
                    </div>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'جاري الحذف...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    $.post('sales.php', {
                        action: 'delete_sale',
                        sale_id: saleId
                    })
                    .done(function(response) {
                        const result = JSON.parse(response);
                        if (result.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'تم الحذف بنجاح',
                                text: result.message,
                                timer: 2000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('خطأ', result.message, 'error');
                        }
                    })
                    .fail(function() {
                        Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                    });
                }
            });
        }

        function refreshTable() {
            location.reload();
        }

        function exportSales() {
            salesTable.button('.buttons-excel').trigger();
        }

        function printReport() {
            window.print();
        }
    </script>
</body>
</html>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            $companyName = getSetting('company_name', 'نظام نقاط البيع');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>
        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link active" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0"><i class="bi bi-graph-up me-2"></i>سجل المبيعات</h2>
                    <small class="text-muted">عرض وإدارة جميع عمليات البيع والتحليلات المالية</small>
                </div>
                <div class="col-auto">
                    <div class="btn-group" role="group">
                        <a href="pos.php" class="btn btn-gradient">
                            <i class="bi bi-cart me-2"></i>نقطة البيع
                        </a>
                        <a href="review_sale.php" class="btn btn-outline-primary">
                            <i class="bi bi-receipt me-2"></i>مراجعة فاتورة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo $totalSales; ?></h4>
                            <p class="mb-0 small">إجمالي المبيعات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-receipt"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #2ecc71);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalAmount, false); ?></h4>
                            <p class="mb-0 small">إجمالي المبلغ</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #e67e22);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalDiscount, false); ?></h4>
                            <p class="mb-0 small">إجمالي الخصومات</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-percent"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="background: linear-gradient(135deg, var(--info-color), #3498db);">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?php echo formatCurrency($totalTax, false); ?></h4>
                            <p class="mb-0 small">إجمالي الضرائب</p>
                        </div>
                        <div class="fs-2 opacity-75">
                            <i class="bi bi-calculator"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-bar-chart me-2"></i>مبيعات آخر 7 أيام</h5>
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="content-card">
                    <h5 class="mb-3"><i class="bi bi-pie-chart me-2"></i>طرق الدفع</h5>
                    <div class="chart-container">
                        <canvas id="paymentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
