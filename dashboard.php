<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';
require 'check_permissions.php';
require 'apply_permissions.php';

// Quick stats queries
$total_sales_today = $pdo->query("SELECT IFNULL(SUM(total),0) FROM sales WHERE DATE(sale_date) = CURDATE()")->fetchColumn();
$product_count = $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn();
$customer_count = $pdo->query("SELECT COUNT(*) FROM customers")->fetchColumn();
$supplier_count = $pdo->query("SELECT COUNT(*) FROM suppliers")->fetchColumn();

// Low stock products (using system setting)
$lowStockLimit = getLowStockAlert();
$low_stock_count = $pdo->query("SELECT COUNT(*) FROM products WHERE stock < $lowStockLimit")->fetchColumn();

// Sales data for last 7 days
$sales_7_days = $pdo->query("
    SELECT DATE(sale_date) as date, IFNULL(SUM(total),0) as total
    FROM sales
    WHERE sale_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    GROUP BY DATE(sale_date)
    ORDER BY date
")->fetchAll();

// Top 5 selling products
$top_products = $pdo->query("
    SELECT p.name, SUM(si.quantity) as total_sold
    FROM sale_items si
    JOIN products p ON si.product_id = p.id
    JOIN sales s ON si.sale_id = s.id
    WHERE s.sale_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    GROUP BY p.id, p.name
    ORDER BY total_sold DESC
    LIMIT 5
")->fetchAll();

// Monthly financial summary
$monthly_sales = $pdo->query("SELECT IFNULL(SUM(total),0) FROM sales WHERE MONTH(sale_date) = MONTH(CURDATE()) AND YEAR(sale_date) = YEAR(CURDATE())")->fetchColumn();
$monthly_purchases = $pdo->query("SELECT IFNULL(SUM(total),0) FROM purchases WHERE MONTH(purchase_date) = MONTH(CURDATE()) AND YEAR(purchase_date) = YEAR(CURDATE())")->fetchColumn();
$monthly_expenses = $pdo->query("SELECT IFNULL(SUM(amount),0) FROM expenses WHERE MONTH(expense_date) = MONTH(CURDATE()) AND YEAR(expense_date) = YEAR(CURDATE())")->fetchColumn();
$monthly_deposits = $pdo->query("SELECT IFNULL(SUM(amount),0) FROM deposits WHERE MONTH(deposit_date) = MONTH(CURDATE()) AND YEAR(deposit_date) = YEAR(CURDATE())")->fetchColumn();

// Calculate net profit
$net_profit = $monthly_sales + $monthly_deposits - $monthly_purchases - $monthly_expenses;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام نقاط البيع</title>

    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">

    <?php renderCommonCSS(); ?>

    <style>
        .stats-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .stats-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            margin-bottom: 15px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }

        .chart-container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .quick-actions {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }

        .action-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            padding: 15px 25px;
            border-radius: 15px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }

        .welcome-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .alert-card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid var(--warning-color);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <?php
    require 'sidebar.php';
    renderSidebar($pdo, $_SESSION['user_id'], 'dashboard.php');
    ?>
    <!-- Main Content -->
    <div class="main-content fade-in">
        <!-- Welcome Card -->
        <div class="welcome-card">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-2">
                        <i class="bi bi-house-door me-2"></i>
                        مرحباً بك، <?php echo htmlspecialchars($_SESSION['username']); ?>
                    </h2>
                    <p class="mb-0 opacity-75">نظرة عامة شاملة على أداء نظام نقاط البيع</p>
                </div>
                <div class="col-auto">
                    <div class="text-end">
                        <div class="badge bg-light text-dark fs-6 mb-2">
                            <i class="bi bi-calendar-date me-1"></i>
                            <?php echo date('Y-m-d'); ?>
                        </div>
                        <br>
                        <div class="badge bg-light text-dark fs-6">
                            <i class="bi bi-clock me-1"></i>
                            <span id="current-time"><?php echo date('H:i:s'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Alert -->
        <?php if ($low_stock_count > 0): ?>
        <div class="alert-card">
            <div class="d-flex align-items-center">
                <i class="bi bi-exclamation-triangle text-warning fs-3 me-3"></i>
                <div>
                    <h6 class="mb-1 text-warning">تنبيه: مخزون منخفض</h6>
                    <p class="mb-0">يوجد <?php echo $low_stock_count; ?> منتج بمخزون منخفض يحتاج إعادة تموين</p>
                </div>
                <div class="ms-auto">
                    <a href="inventory.php" class="btn btn-warning btn-sm">
                        <i class="bi bi-eye me-1"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Stats Cards -->
        <div class="row g-4 mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="text-center">
                        <div class="stats-icon mx-auto" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <h6 class="text-muted mb-2">مبيعات اليوم</h6>
                        <h3 class="mb-0 text-success"><?php echo formatCurrency($total_sales_today); ?></h3>
                        <small class="text-muted">
                            <i class="bi bi-calendar-day me-1"></i>
                            <?php echo date('d/m/Y'); ?>
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="text-center">
                        <div class="stats-icon mx-auto" style="background: linear-gradient(135deg, #3498db, #5dade2);">
                            <i class="bi bi-box"></i>
                        </div>
                        <h6 class="text-muted mb-2">إجمالي المنتجات</h6>
                        <h3 class="mb-0 text-primary"><?php echo number_format($product_count); ?></h3>
                        <small class="text-muted">
                            <i class="bi bi-boxes me-1"></i>
                            منتج متاح
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="text-center">
                        <div class="stats-icon mx-auto" style="background: linear-gradient(135deg, #17a2b8, #5bc0de);">
                            <i class="bi bi-people"></i>
                        </div>
                        <h6 class="text-muted mb-2">قاعدة العملاء</h6>
                        <h3 class="mb-0 text-info"><?php echo number_format($customer_count); ?></h3>
                        <small class="text-muted">
                            <i class="bi bi-person-check me-1"></i>
                            عميل مسجل
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="text-center">
                        <div class="stats-icon mx-auto" style="background: linear-gradient(135deg, #f39c12, #f8c471);">
                            <i class="bi bi-building"></i>
                        </div>
                        <h6 class="text-muted mb-2">شبكة الموردين</h6>
                        <h3 class="mb-0 text-warning"><?php echo number_format($supplier_count); ?></h3>
                        <small class="text-muted">
                            <i class="bi bi-truck me-1"></i>
                            مورد نشط
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Summary Cards -->
        <div class="row g-4 mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="text-center">
                        <div class="stats-icon mx-auto" style="background: linear-gradient(135deg, #27ae60, #58d68d);">
                            <i class="bi bi-graph-up-arrow"></i>
                        </div>
                        <h6 class="text-muted mb-2">مبيعات الشهر</h6>
                        <h3 class="mb-0 text-success"><?php echo formatCurrency($monthly_sales); ?></h3>
                        <small class="text-muted">
                            <i class="bi bi-calendar-month me-1"></i>
                            <?php echo date('F Y'); ?>
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="text-center">
                        <div class="stats-icon mx-auto" style="background: linear-gradient(135deg, #e74c3c, #ec7063);">
                            <i class="bi bi-graph-down-arrow"></i>
                        </div>
                        <h6 class="text-muted mb-2">مشتريات الشهر</h6>
                        <h3 class="mb-0 text-danger"><?php echo formatCurrency($monthly_purchases); ?></h3>
                        <small class="text-muted">
                            <i class="bi bi-cart-dash me-1"></i>
                            إجمالي المشتريات
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="text-center">
                        <div class="stats-icon mx-auto" style="background: linear-gradient(135deg, <?php echo $net_profit >= 0 ? '#27ae60, #58d68d' : '#e74c3c, #ec7063'; ?>);">
                            <i class="bi bi-calculator"></i>
                        </div>
                        <h6 class="text-muted mb-2">صافي الربح</h6>
                        <h3 class="mb-0 text-<?php echo $net_profit >= 0 ? 'success' : 'danger'; ?>">
                            <?php echo formatCurrency($net_profit); ?>
                        </h3>
                        <small class="text-muted">
                            <i class="bi bi-<?php echo $net_profit >= 0 ? 'arrow-up' : 'arrow-down'; ?> me-1"></i>
                            <?php echo $net_profit >= 0 ? 'ربح' : 'خسارة'; ?> الشهر
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="text-center">
                        <div class="stats-icon mx-auto" style="background: linear-gradient(135deg, <?php echo $low_stock_count > 0 ? '#e74c3c, #ec7063' : '#27ae60, #58d68d'; ?>);">
                            <i class="bi bi-<?php echo $low_stock_count > 0 ? 'exclamation-triangle' : 'check-circle'; ?>"></i>
                        </div>
                        <h6 class="text-muted mb-2">حالة المخزون</h6>
                        <h3 class="mb-0 text-<?php echo $low_stock_count > 0 ? 'danger' : 'success'; ?>">
                            <?php echo number_format($low_stock_count); ?>
                        </h3>
                        <small class="text-muted">
                            <i class="bi bi-boxes me-1"></i>
                            <?php echo $low_stock_count > 0 ? 'منتج بمخزون منخفض' : 'المخزون جيد'; ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions mb-4">
            <h5 class="mb-3">
                <i class="bi bi-lightning text-primary me-2"></i>
                الإجراءات السريعة
            </h5>
            <div class="row g-3">
                <?php if (hasPermission($pdo, $_SESSION['user_id'], 'pos.access') || isAdmin($pdo, $_SESSION['user_id'])): ?>
                <div class="col-md-3">
                    <a href="pos.php" class="action-btn w-100 text-center">
                        <i class="bi bi-cart-plus d-block fs-4 mb-2"></i>
                        نقطة البيع
                    </a>
                </div>
                <?php endif; ?>

                <?php if (hasPermission($pdo, $_SESSION['user_id'], 'products.create') || isAdmin($pdo, $_SESSION['user_id'])): ?>
                <div class="col-md-3">
                    <a href="products.php" class="action-btn w-100 text-center">
                        <i class="bi bi-plus-circle d-block fs-4 mb-2"></i>
                        إضافة منتج
                    </a>
                </div>
                <?php endif; ?>

                <?php if (hasPermission($pdo, $_SESSION['user_id'], 'customers.create') || isAdmin($pdo, $_SESSION['user_id'])): ?>
                <div class="col-md-3">
                    <a href="customers.php" class="action-btn w-100 text-center">
                        <i class="bi bi-person-plus d-block fs-4 mb-2"></i>
                        إضافة عميل
                    </a>
                </div>
                <?php endif; ?>

                <?php if (hasPermission($pdo, $_SESSION['user_id'], 'reports.view') || isAdmin($pdo, $_SESSION['user_id'])): ?>
                <div class="col-md-3">
                    <a href="reports.php" class="action-btn w-100 text-center">
                        <i class="bi bi-bar-chart d-block fs-4 mb-2"></i>
                        التقارير
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row g-4">
            <!-- Sales Chart -->
            <div class="col-lg-8">
                <div class="chart-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up text-primary me-2"></i>
                            مبيعات آخر 7 أيام
                        </h5>
                        <span class="badge bg-primary">
                            إجمالي: <?php echo formatCurrency(array_sum($sales)); ?>
                        </span>
                    </div>
                    <canvas id="salesChart" height="100"></canvas>
                </div>
            </div>

            <!-- Top Products Chart -->
            <div class="col-lg-4">
                <div class="chart-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="bi bi-pie-chart text-success me-2"></i>
                            أفضل 5 منتجات مبيعاً
                        </h5>
                        <span class="badge bg-success">
                            آخر 30 يوم
                        </span>
                    </div>
                    <?php if (!empty($top_products)): ?>
                    <canvas id="topProductsChart"></canvas>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox text-muted fs-1"></i>
                        <p class="text-muted mt-2">لا توجد بيانات مبيعات</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تحديث الساعة كل ثانية
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }

        // تحديث الساعة كل ثانية
        setInterval(updateTime, 1000);
        updateTime(); // تحديث فوري عند تحميل الصفحة
        // Sales Chart Data
        const salesData = {
            labels: [
                <?php
                $dates = [];
                $sales = [];

                // Fill missing dates with 0 sales
                for ($i = 6; $i >= 0; $i--) {
                    $date = date('Y-m-d', strtotime("-$i days"));
                    $dates[] = "'" . date('m/d', strtotime($date)) . "'";

                    $found = false;
                    foreach ($sales_7_days as $sale) {
                        if ($sale['date'] == $date) {
                            $sales[] = $sale['total'];
                            $found = true;
                            break;
                        }
                    }
                    if (!$found) {
                        $sales[] = 0;
                    }
                }
                echo implode(',', $dates);
                ?>
            ],
            datasets: [{
                label: 'المبيعات (ر.س)',
                data: [<?php echo implode(',', $sales); ?>],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        };

        // Top Products Chart Data
        const topProductsData = {
            labels: [
                <?php
                $productNames = [];
                $productSales = [];
                foreach ($top_products as $product) {
                    $productNames[] = "'" . $product['name'] . "'";
                    $productSales[] = $product['total_sold'];
                }
                echo implode(',', $productNames);
                ?>
            ],
            datasets: [{
                data: [<?php echo implode(',', $productSales); ?>],
                backgroundColor: [
                    '#3498db',
                    '#2ecc71',
                    '#f39c12',
                    '#e74c3c',
                    '#9b59b6'
                ],
                borderWidth: 0
            }]
        };

        // Initialize Sales Chart
        const salesCtx = document.getElementById('salesChart').getContext('2d');
        new Chart(salesCtx, {
            type: 'line',
            data: salesData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#3498db',
                        borderWidth: 1,
                        cornerRadius: 10,
                        displayColors: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(52, 152, 219, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#6c757d',
                            font: {
                                family: 'Segoe UI'
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6c757d',
                            font: {
                                family: 'Segoe UI'
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 6,
                        hoverRadius: 8,
                        backgroundColor: '#3498db',
                        borderColor: '#fff',
                        borderWidth: 2
                    }
                }
            }
        });

        // Initialize Top Products Chart (only if data exists)
        <?php if (!empty($top_products)): ?>
        const topProductsCtx = document.getElementById('topProductsChart').getContext('2d');
        new Chart(topProductsCtx, {
            type: 'doughnut',
            data: topProductsData,
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                family: 'Segoe UI',
                                size: 12
                            },
                            color: '#6c757d'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#3498db',
                        borderWidth: 1,
                        cornerRadius: 10,
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + ' قطعة';
                            }
                        }
                    }
                },
                cutout: '60%',
                elements: {
                    arc: {
                        borderWidth: 2,
                        borderColor: '#fff'
                    }
                }
            }
        });
        <?php endif; ?>

        // Auto refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>

    <?php renderCommonJS($pdo, $_SESSION['user_id']); ?>
</body>
</html>