<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}
require 'db.php';
require 'get_settings.php';
require 'check_permissions.php';
require 'apply_permissions.php';

// Quick stats queries
$total_sales_today = $pdo->query("SELECT IFNULL(SUM(total),0) FROM sales WHERE DATE(sale_date) = CURDATE()")->fetchColumn();
$product_count = $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn();
$customer_count = $pdo->query("SELECT COUNT(*) FROM customers")->fetchColumn();
$supplier_count = $pdo->query("SELECT COUNT(*) FROM suppliers")->fetchColumn();

// Low stock products (using system setting)
$lowStockLimit = getLowStockAlert();
$low_stock_count = $pdo->query("SELECT COUNT(*) FROM products WHERE stock < $lowStockLimit")->fetchColumn();

// Sales data for last 7 days
$sales_7_days = $pdo->query("
    SELECT DATE(sale_date) as date, IFNULL(SUM(total),0) as total
    FROM sales
    WHERE sale_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    GROUP BY DATE(sale_date)
    ORDER BY date
")->fetchAll();

// Top 5 selling products
$top_products = $pdo->query("
    SELECT p.name, SUM(si.quantity) as total_sold
    FROM sale_items si
    JOIN products p ON si.product_id = p.id
    JOIN sales s ON si.sale_id = s.id
    WHERE s.sale_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    GROUP BY p.id, p.name
    ORDER BY total_sold DESC
    LIMIT 5
")->fetchAll();

// Monthly financial summary
$monthly_sales = $pdo->query("SELECT IFNULL(SUM(total),0) FROM sales WHERE MONTH(sale_date) = MONTH(CURDATE()) AND YEAR(sale_date) = YEAR(CURDATE())")->fetchColumn();
$monthly_purchases = $pdo->query("SELECT IFNULL(SUM(total),0) FROM purchases WHERE MONTH(purchase_date) = MONTH(CURDATE()) AND YEAR(purchase_date) = YEAR(CURDATE())")->fetchColumn();
$monthly_expenses = $pdo->query("SELECT IFNULL(SUM(amount),0) FROM expenses WHERE MONTH(expense_date) = MONTH(CURDATE()) AND YEAR(expense_date) = YEAR(CURDATE())")->fetchColumn();
$monthly_deposits = $pdo->query("SELECT IFNULL(SUM(amount),0) FROM deposits WHERE MONTH(deposit_date) = MONTH(CURDATE()) AND YEAR(deposit_date) = YEAR(CURDATE())")->fetchColumn();

// Calculate net profit
$net_profit = $monthly_sales + $monthly_deposits - $monthly_purchases - $monthly_expenses;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام نقاط البيع</title>

    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f6f9;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            border-left-color: #fff;
        }

        .main-content {
            margin-right: 280px;
            padding: 20px;
            min-height: 100vh;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 15px;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="logo">
            <?php
            $companyLogo = getSetting('company_logo');
            $companyName = getSetting('company_name', 'نظام نقاط البيع');
            if (!empty($companyLogo) && file_exists($companyLogo)):
            ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="max-width: 60px; max-height: 60px; border-radius: 10px;" class="mb-2">
                <h5 class="mb-0"><?php echo htmlspecialchars($companyName); ?></h5>
            <?php else: ?>
                <h4 class="mb-0"><i class="bi bi-shop"></i> <?php echo htmlspecialchars($companyName); ?></h4>
            <?php endif; ?>
            <small class="text-light">مرحباً، <?php echo $_SESSION['username']; ?></small>
        </div>

        <nav class="nav flex-column mt-3">
            <a class="nav-link active" href="dashboard.php">
                <i class="bi bi-house-door me-2"></i> لوحة التحكم
            </a>
            <a class="nav-link" href="products.php">
                <i class="bi bi-box me-2"></i> إدارة المنتجات
            </a>
            <a class="nav-link" href="categories.php">
                <i class="bi bi-folder me-2"></i> إدارة الفئات
            </a>
            <a class="nav-link" href="units.php">
                <i class="bi bi-rulers me-2"></i> إدارة الوحدات
            </a>
            <a class="nav-link" href="pos.php">
                <i class="bi bi-cart me-2"></i> نقطة البيع
            </a>
            <a class="nav-link" href="sales.php">
                <i class="bi bi-graph-up me-2"></i> سجل المبيعات
            </a>
            <a class="nav-link" href="purchases.php">
                <i class="bi bi-clipboard-data me-2"></i> سجل المشتريات
            </a>
            <a class="nav-link" href="inventory.php">
                <i class="bi bi-boxes me-2"></i> إدارة المخزون
            </a>
            <a class="nav-link" href="customers.php">
                <i class="bi bi-people me-2"></i> إدارة العملاء
            </a>
            <a class="nav-link" href="suppliers.php">
                <i class="bi bi-building me-2"></i> إدارة الموردين
            </a>
            <a class="nav-link" href="accounting.php">
                <i class="bi bi-currency-dollar me-2"></i> النظام المحاسبي
            </a>
            <a class="nav-link" href="reports.php">
                <i class="bi bi-bar-chart me-2"></i> التقارير والإحصائيات
            </a>
            <a class="nav-link" href="settings.php">
                <i class="bi bi-gear me-2"></i> إعدادات النظام
            </a>
            <hr class="text-light">
            <a class="nav-link text-warning" href="logout.php">
                <i class="bi bi-box-arrow-right me-2"></i> تسجيل الخروج
            </a>
        </nav>
    </div>
    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="mb-0">لوحة التحكم</h2>
                    <small class="text-muted">نظرة عامة على أداء النظام</small>
                </div>
                <div class="col-auto">
                    <span class="badge bg-primary fs-6">
                        <i class="bi bi-calendar-date me-1"></i>
                        <?php echo date('Y-m-d'); ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Quick Stats Cards -->
        <div class="row g-4 mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">مبيعات اليوم</h6>
                            <h4 class="mb-0"><?php echo formatCurrency($total_sales_today); ?></h4>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary">
                            <i class="bi bi-box"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">المنتجات</h6>
                            <h4 class="mb-0"><?php echo number_format($product_count); ?></h4>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">العملاء</h6>
                            <h4 class="mb-0"><?php echo number_format($customer_count); ?></h4>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning">
                            <i class="bi bi-building"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">الموردين</h6>
                            <h4 class="mb-0"><?php echo number_format($supplier_count); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Summary Cards -->
        <div class="row g-4 mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success">
                            <i class="bi bi-graph-up-arrow"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">مبيعات الشهر</h6>
                            <h4 class="mb-0"><?php echo formatCurrency($monthly_sales); ?></h4>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-danger">
                            <i class="bi bi-graph-down-arrow"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">مشتريات الشهر</h6>
                            <h4 class="mb-0"><?php echo formatCurrency($monthly_purchases); ?></h4>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-<?php echo $net_profit >= 0 ? 'success' : 'danger'; ?>">
                            <i class="bi bi-calculator"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">صافي الربح</h6>
                            <h4 class="mb-0 text-<?php echo $net_profit >= 0 ? 'success' : 'danger'; ?>">
                                <?php echo number_format($net_profit, 2); ?> ر.س
                            </h4>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-<?php echo $low_stock_count > 0 ? 'danger' : 'success'; ?>">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">مخزون منخفض</h6>
                            <h4 class="mb-0 text-<?php echo $low_stock_count > 0 ? 'danger' : 'success'; ?>">
                                <?php echo number_format($low_stock_count); ?> منتج
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row g-4">
            <!-- Sales Chart -->
            <div class="col-lg-8">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-graph-up text-primary me-2"></i>
                        مبيعات آخر 7 أيام
                    </h5>
                    <canvas id="salesChart" height="100"></canvas>
                </div>
            </div>

            <!-- Top Products Chart -->
            <div class="col-lg-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="bi bi-pie-chart text-success me-2"></i>
                        أفضل 5 منتجات مبيعاً
                    </h5>
                    <canvas id="topProductsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Sales Chart Data
        const salesData = {
            labels: [
                <?php
                $dates = [];
                $sales = [];

                // Fill missing dates with 0 sales
                for ($i = 6; $i >= 0; $i--) {
                    $date = date('Y-m-d', strtotime("-$i days"));
                    $dates[] = "'" . date('m/d', strtotime($date)) . "'";

                    $found = false;
                    foreach ($sales_7_days as $sale) {
                        if ($sale['date'] == $date) {
                            $sales[] = $sale['total'];
                            $found = true;
                            break;
                        }
                    }
                    if (!$found) {
                        $sales[] = 0;
                    }
                }
                echo implode(',', $dates);
                ?>
            ],
            datasets: [{
                label: 'المبيعات (ر.س)',
                data: [<?php echo implode(',', $sales); ?>],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        };

        // Top Products Chart Data
        const topProductsData = {
            labels: [
                <?php
                $productNames = [];
                $productSales = [];
                foreach ($top_products as $product) {
                    $productNames[] = "'" . $product['name'] . "'";
                    $productSales[] = $product['total_sold'];
                }
                echo implode(',', $productNames);
                ?>
            ],
            datasets: [{
                data: [<?php echo implode(',', $productSales); ?>],
                backgroundColor: [
                    '#3498db',
                    '#2ecc71',
                    '#f39c12',
                    '#e74c3c',
                    '#9b59b6'
                ],
                borderWidth: 0
            }]
        };

        // Initialize Sales Chart
        const salesCtx = document.getElementById('salesChart').getContext('2d');
        new Chart(salesCtx, {
            type: 'line',
            data: salesData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // Initialize Top Products Chart
        const topProductsCtx = document.getElementById('topProductsChart').getContext('2d');
        new Chart(topProductsCtx, {
            type: 'doughnut',
            data: topProductsData,
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });

        // Auto refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>