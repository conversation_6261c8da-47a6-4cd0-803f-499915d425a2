<?php
// ملف الحماية العام - يجب تضمينه في جميع الصفحات المحمية
require_once 'db.php';
require_once 'session_security.php';
require_once 'check_permissions.php';

// التحقق من تسجيل الدخول
requireLogin();

// إنشاء الجداول المطلوبة إذا لم تكن موجودة
createUserSessionsTable($pdo);
createUserLogsTable($pdo);

// تنظيف الجلسات المنتهية الصلاحية
cleanExpiredSessions($pdo);

// التحقق من صحة الجلسة
if (!validateSession()) {
    secureLogout($pdo);
    header('Location: login');
    exit;
}

// تحديث نشاط الجلسة
updateSessionActivity();

// دالة للتحقق من الصلاحية وإعادة التوجيه
function checkPagePermission($permission, $redirectUrl = 'dashboard') {
    global $pdo;
    
    if (!isset($_SESSION['user_id'])) {
        header("Location: login");
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    
    // إذا كان مدير، يمكنه الوصول لكل شيء
    if (isAdmin($pdo, $userId)) {
        return true;
    }
    
    // التحقق من الصلاحية المطلوبة
    if (!hasPermission($pdo, $userId, $permission)) {
        header("Location: $redirectUrl?error=no_permission");
        exit;
    }
    
    return true;
}

// دالة لتسجيل الوصول للصفحة
function logPageAccess($page) {
    global $pdo;
    
    if (isset($_SESSION['user_id'])) {
        logUserAction($pdo, $_SESSION['user_id'], 'page_access', "Accessed page: $page");
    }
}

// دالة للحماية من CSRF
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// دالة للتحقق من CSRF Token
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// دالة لتنظيف المدخلات
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// دالة للتحقق من صحة البريد الإلكتروني
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// دالة للتحقق من قوة كلمة المرور
function validatePassword($password) {
    // على الأقل 8 أحرف، حرف كبير، حرف صغير، رقم
    return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/', $password);
}

// دالة لحماية من SQL Injection
function sanitizeForSQL($input) {
    return addslashes(trim($input));
}

// دالة للتحقق من نوع الملف المرفوع
function validateFileType($filename, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif']) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowedTypes);
}

// دالة للتحقق من حجم الملف
function validateFileSize($fileSize, $maxSize = 5242880) { // 5MB default
    return $fileSize <= $maxSize;
}

// دالة لإنشاء اسم ملف آمن
function generateSafeFilename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $safeName = preg_replace('/[^a-zA-Z0-9_-]/', '_', pathinfo($originalName, PATHINFO_FILENAME));
    return $safeName . '_' . time() . '.' . $extension;
}

// دالة للحماية من XSS
function escapeOutput($output) {
    return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
}

// دالة للتحقق من IP المسموح
function checkAllowedIP($allowedIPs = []) {
    if (empty($allowedIPs)) {
        return true; // إذا لم يتم تحديد IPs مسموحة، السماح للجميع
    }
    
    $userIP = $_SERVER['REMOTE_ADDR'];
    return in_array($userIP, $allowedIPs);
}

// دالة للحماية من Directory Traversal
function validatePath($path) {
    $realPath = realpath($path);
    $basePath = realpath(__DIR__);
    
    return $realPath !== false && strpos($realPath, $basePath) === 0;
}

// دالة لتشفير البيانات الحساسة
function encryptData($data, $key = null) {
    if ($key === null) {
        $key = 'your-secret-key-here'; // يجب تغييرها في الإنتاج
    }
    
    $iv = random_bytes(16);
    $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
    return base64_encode($iv . $encrypted);
}

// دالة لفك تشفير البيانات
function decryptData($encryptedData, $key = null) {
    if ($key === null) {
        $key = 'your-secret-key-here'; // يجب أن تكون نفس المفتاح المستخدم في التشفير
    }
    
    $data = base64_decode($encryptedData);
    $iv = substr($data, 0, 16);
    $encrypted = substr($data, 16);
    
    return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
}

// دالة للتحقق من قوة الاتصال بقاعدة البيانات
function checkDatabaseConnection() {
    global $pdo;
    
    try {
        $pdo->query('SELECT 1');
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// دالة لتسجيل الأخطاء الأمنية
function logSecurityEvent($event, $details = null) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO security_logs (event_type, details, ip_address, user_agent, user_id, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $userId = $_SESSION['user_id'] ?? null;
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $stmt->execute([$event, $details, $ipAddress, $userAgent, $userId]);
    } catch (PDOException $e) {
        // فشل في تسجيل الحدث الأمني
        error_log("Failed to log security event: " . $e->getMessage());
    }
}

// دالة لإنشاء جدول سجل الأحداث الأمنية
function createSecurityLogsTable($pdo) {
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS security_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_type VARCHAR(100) NOT NULL,
            details TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            user_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_event_type (event_type),
            INDEX idx_ip_address (ip_address),
            INDEX idx_user_id (user_id),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )");
    } catch (PDOException $e) {
        // الجدول موجود مسبقاً أو خطأ في الإنشاء
    }
}

// إنشاء جدول سجل الأحداث الأمنية
createSecurityLogsTable($pdo);

// تسجيل الوصول للصفحة
$currentPage = basename($_SERVER['PHP_SELF']);
logPageAccess($currentPage);

// إعداد headers الأمان
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: SAMEORIGIN');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// منع التخزين المؤقت للصفحات الحساسة
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
?>
