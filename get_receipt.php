<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    exit('غير مصرح');
}

require 'db.php';
require 'get_settings.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    exit('معرف غير صحيح');
}

$saleId = intval($_GET['id']);

try {
    // جلب تفاصيل البيع
    $saleStmt = $pdo->prepare("
        SELECT s.*, 
               c.name as customer_name,
               c.phone as customer_phone,
               u.name as user_name
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id 
        LEFT JOIN users u ON s.user_id = u.id 
        WHERE s.id = ?
    ");
    $saleStmt->execute([$saleId]);
    $sale = $saleStmt->fetch();

    if (!$sale) {
        http_response_code(404);
        exit('البيع غير موجود');
    }

    // جلب عناصر البيع
    $itemsStmt = $pdo->prepare("
        SELECT si.*, p.name as product_name, p.code as product_code,
               u.name as unit_name
        FROM sale_items si 
        LEFT JOIN products p ON si.product_id = p.id 
        LEFT JOIN units u ON p.unit_id = u.id
        WHERE si.sale_id = ?
        ORDER BY p.name
    ");
    $itemsStmt->execute([$saleId]);
    $items = $itemsStmt->fetchAll();

    // إعدادات الشركة
    $companyName = getSetting('company_name', 'نظام نقاط البيع');
    $companyPhone = getSetting('company_phone', '');
    $companyAddress = getSetting('company_address', '');
    $taxNumber = getSetting('tax_number', '');
    $currency = getSetting('currency', 'ر.س');

} catch (PDOException $e) {
    http_response_code(500);
    exit('خطأ في قاعدة البيانات');
}
?>

<div class="receipt-container" style="max-width: 400px; margin: 0 auto; font-family: Arial, sans-serif;">
    <!-- Header -->
    <div class="text-center mb-3">
        <h4 class="mb-1"><?php echo htmlspecialchars($companyName); ?></h4>
        <?php if (!empty($companyPhone)): ?>
        <p class="mb-1 small">هاتف: <?php echo htmlspecialchars($companyPhone); ?></p>
        <?php endif; ?>
        <?php if (!empty($companyAddress)): ?>
        <p class="mb-1 small"><?php echo htmlspecialchars($companyAddress); ?></p>
        <?php endif; ?>
        <?php if (!empty($taxNumber)): ?>
        <p class="mb-1 small">الرقم الضريبي: <?php echo htmlspecialchars($taxNumber); ?></p>
        <?php endif; ?>
        <hr>
        <h5>فاتورة بيع</h5>
    </div>

    <!-- Sale Info -->
    <div class="row mb-3">
        <div class="col-6">
            <strong>رقم الفاتورة:</strong><br>
            <span class="text-muted">#<?php echo str_pad($sale['id'], 6, '0', STR_PAD_LEFT); ?></span>
        </div>
        <div class="col-6 text-end">
            <strong>التاريخ:</strong><br>
            <span class="text-muted"><?php echo date('Y-m-d H:i', strtotime($sale['sale_date'])); ?></span>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-6">
            <strong>الكاشير:</strong><br>
            <span class="text-muted"><?php echo htmlspecialchars($sale['user_name'] ?? 'غير محدد'); ?></span>
        </div>
        <div class="col-6 text-end">
            <strong>طريقة الدفع:</strong><br>
            <span class="text-muted">
                <?php
                switch ($sale['payment_method']) {
                    case 'cash': echo 'نقدي'; break;
                    case 'card': echo 'بطاقة ائتمان'; break;
                    case 'transfer': echo 'تحويل بنكي'; break;
                    default: echo 'نقدي';
                }
                ?>
            </span>
        </div>
    </div>

    <?php if (!empty($sale['customer_name'])): ?>
    <div class="mb-3">
        <strong>العميل:</strong> <?php echo htmlspecialchars($sale['customer_name']); ?>
        <?php if (!empty($sale['customer_phone'])): ?>
        <br><small class="text-muted">هاتف: <?php echo htmlspecialchars($sale['customer_phone']); ?></small>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <hr>

    <!-- Items -->
    <div class="mb-3">
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th class="text-center">الكمية</th>
                    <th class="text-end">السعر</th>
                    <th class="text-end">الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                <?php 
                $subtotal = 0;
                foreach ($items as $item): 
                    $itemTotal = $item['quantity'] * $item['unit_price'];
                    $subtotal += $itemTotal;
                ?>
                <tr>
                    <td>
                        <div class="fw-bold"><?php echo htmlspecialchars($item['product_name']); ?></div>
                        <?php if (!empty($item['product_code'])): ?>
                        <small class="text-muted">كود: <?php echo htmlspecialchars($item['product_code']); ?></small>
                        <?php endif; ?>
                    </td>
                    <td class="text-center">
                        <?php echo $item['quantity']; ?>
                        <?php if (!empty($item['unit_name'])): ?>
                        <small class="text-muted"><?php echo htmlspecialchars($item['unit_name']); ?></small>
                        <?php endif; ?>
                    </td>
                    <td class="text-end"><?php echo number_format($item['unit_price'], 2); ?></td>
                    <td class="text-end"><?php echo number_format($itemTotal, 2); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <hr>

    <!-- Totals -->
    <div class="mb-3">
        <div class="row">
            <div class="col-6"><strong>المجموع الفرعي:</strong></div>
            <div class="col-6 text-end"><?php echo number_format($subtotal, 2); ?> <?php echo $currency; ?></div>
        </div>
        
        <?php if ($sale['discount'] > 0): ?>
        <div class="row">
            <div class="col-6">الخصم:</div>
            <div class="col-6 text-end text-danger">-<?php echo number_format($sale['discount'], 2); ?> <?php echo $currency; ?></div>
        </div>
        <?php endif; ?>
        
        <?php if ($sale['tax'] > 0): ?>
        <div class="row">
            <div class="col-6">الضريبة:</div>
            <div class="col-6 text-end"><?php echo number_format($sale['tax'], 2); ?> <?php echo $currency; ?></div>
        </div>
        <?php endif; ?>
        
        <hr>
        
        <div class="row">
            <div class="col-6"><strong>الإجمالي النهائي:</strong></div>
            <div class="col-6 text-end"><strong><?php echo number_format($sale['total'], 2); ?> <?php echo $currency; ?></strong></div>
        </div>
    </div>

    <?php if (!empty($sale['notes'])): ?>
    <hr>
    <div class="mb-3">
        <strong>ملاحظات:</strong><br>
        <span class="text-muted"><?php echo htmlspecialchars($sale['notes']); ?></span>
    </div>
    <?php endif; ?>

    <hr>

    <!-- Footer -->
    <div class="text-center">
        <p class="mb-1 small text-muted">شكراً لتسوقكم معنا</p>
        <p class="mb-1 small text-muted">نتمنى لكم يوماً سعيداً</p>
        <div class="mt-3">
            <small class="text-muted">
                تم الطباعة في: <?php echo date('Y-m-d H:i:s'); ?>
            </small>
        </div>
    </div>
</div>

<style>
@media print {
    body { margin: 0; }
    .receipt-container { 
        max-width: none; 
        width: 80mm; 
        font-size: 12px; 
    }
    .table { font-size: 11px; }
    .btn { display: none; }
}
</style>
