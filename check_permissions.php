<?php
// ملف للتحقق من صلاحيات المستخدمين
function hasPermission($pdo, $userId, $permission) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM user_roles ur
            JOIN role_permissions rp ON ur.role_id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            WHERE ur.user_id = ? AND p.name = ?
        ");
        $stmt->execute([$userId, $permission]);
        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function getUserRole($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("
            SELECT r.name, r.display_name 
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = ?
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        return null;
    }
}

function getUserPermissions($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("
            SELECT DISTINCT p.name, p.display_name, p.category
            FROM user_roles ur
            JOIN role_permissions rp ON ur.role_id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            WHERE ur.user_id = ?
            ORDER BY p.category, p.name
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

function isAdmin($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = ? AND r.name = 'admin'
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function checkPermissionOrRedirect($pdo, $userId, $permission, $redirectUrl = 'dashboard.php') {
    if (!hasPermission($pdo, $userId, $permission) && !isAdmin($pdo, $userId)) {
        header("Location: $redirectUrl?error=no_permission");
        exit;
    }
}

function canAccess($pdo, $userId, $permissions) {
    // إذا كان مدير، يمكنه الوصول لكل شيء
    if (isAdmin($pdo, $userId)) {
        return true;
    }
    
    // التحقق من وجود أي من الصلاحيات المطلوبة
    foreach ($permissions as $permission) {
        if (hasPermission($pdo, $userId, $permission)) {
            return true;
        }
    }
    
    return false;
}

// دالة لعرض رسالة عدم وجود صلاحية
function showNoPermissionMessage($message = 'ليس لديك صلاحية للوصول إلى هذه الصفحة') {
    echo '<div class="alert alert-danger text-center">';
    echo '<i class="bi bi-shield-exclamation fs-1 text-danger mb-3"></i>';
    echo '<h4>غير مصرح</h4>';
    echo '<p>' . htmlspecialchars($message) . '</p>';
    echo '<a href="dashboard.php" class="btn btn-primary">العودة للوحة التحكم</a>';
    echo '</div>';
}

// دالة للتحقق من الصلاحيات في JavaScript
function generatePermissionsJS($pdo, $userId) {
    $permissions = getUserPermissions($pdo, $userId);
    $permissionsList = array_column($permissions, 'name');
    $isAdminUser = isAdmin($pdo, $userId);
    
    echo '<script>';
    echo 'window.userPermissions = ' . json_encode($permissionsList) . ';';
    echo 'window.isAdmin = ' . ($isAdminUser ? 'true' : 'false') . ';';
    echo 'function hasPermission(permission) {';
    echo '    return window.isAdmin || window.userPermissions.includes(permission);';
    echo '}';
    echo '</script>';
}

// دالة لإخفاء/إظهار العناصر حسب الصلاحيات
function showIfHasPermission($pdo, $userId, $permission, $content, $hideClass = 'd-none') {
    if (hasPermission($pdo, $userId, $permission) || isAdmin($pdo, $userId)) {
        echo $content;
    } else {
        echo '<div class="' . $hideClass . '">' . $content . '</div>';
    }
}

// دالة لتطبيق الصلاحيات على الأزرار
function permissionButton($pdo, $userId, $permission, $buttonHtml, $disabledText = 'غير مصرح') {
    if (hasPermission($pdo, $userId, $permission) || isAdmin($pdo, $userId)) {
        return $buttonHtml;
    } else {
        return '<button class="btn btn-secondary" disabled title="' . $disabledText . '">' . $disabledText . '</button>';
    }
}

// دالة للتحقق من صلاحيات متعددة
function hasAnyPermission($pdo, $userId, $permissions) {
    if (isAdmin($pdo, $userId)) {
        return true;
    }
    
    foreach ($permissions as $permission) {
        if (hasPermission($pdo, $userId, $permission)) {
            return true;
        }
    }
    
    return false;
}

// دالة للتحقق من جميع الصلاحيات
function hasAllPermissions($pdo, $userId, $permissions) {
    if (isAdmin($pdo, $userId)) {
        return true;
    }
    
    foreach ($permissions as $permission) {
        if (!hasPermission($pdo, $userId, $permission)) {
            return false;
        }
    }
    
    return true;
}

// دالة لتسجيل العمليات
function logUserAction($pdo, $userId, $action, $details = null) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO user_logs (user_id, action, details, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $stmt->execute([$userId, $action, $details, $ipAddress, $userAgent]);
    } catch (PDOException $e) {
        // فشل في تسجيل العملية - يمكن تجاهله
    }
}

// إنشاء جدول سجل العمليات إذا لم يكن موجود
function createUserLogsTable($pdo) {
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(100) NOT NULL,
            details TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )");
    } catch (PDOException $e) {
        // الجدول موجود مسبقاً أو خطأ في الإنشاء
    }
}

// دالة للحصول على قائمة الصفحات المسموحة للمستخدم
function getAllowedPages($pdo, $userId) {
    $pages = [];
    
    // الصفحات العامة المتاحة للجميع
    $pages[] = ['url' => 'dashboard.php', 'name' => 'لوحة التحكم', 'icon' => 'bi-house-door'];
    
    // صفحات حسب الصلاحيات
    if (hasPermission($pdo, $userId, 'products.view') || isAdmin($pdo, $userId)) {
        $pages[] = ['url' => 'products.php', 'name' => 'إدارة المنتجات', 'icon' => 'bi-box'];
        $pages[] = ['url' => 'categories.php', 'name' => 'إدارة الفئات', 'icon' => 'bi-folder'];
        $pages[] = ['url' => 'units.php', 'name' => 'إدارة الوحدات', 'icon' => 'bi-rulers'];
    }
    
    if (hasPermission($pdo, $userId, 'pos.access') || isAdmin($pdo, $userId)) {
        $pages[] = ['url' => 'pos.php', 'name' => 'نقطة البيع', 'icon' => 'bi-cart'];
    }
    
    if (hasPermission($pdo, $userId, 'sales.view') || isAdmin($pdo, $userId)) {
        $pages[] = ['url' => 'sales.php', 'name' => 'سجل المبيعات', 'icon' => 'bi-graph-up'];
    }
    
    if (hasPermission($pdo, $userId, 'inventory.view') || isAdmin($pdo, $userId)) {
        $pages[] = ['url' => 'purchases.php', 'name' => 'سجل المشتريات', 'icon' => 'bi-clipboard-data'];
        $pages[] = ['url' => 'inventory.php', 'name' => 'إدارة المخزون', 'icon' => 'bi-boxes'];
    }
    
    if (hasPermission($pdo, $userId, 'customers.view') || isAdmin($pdo, $userId)) {
        $pages[] = ['url' => 'customers.php', 'name' => 'إدارة العملاء', 'icon' => 'bi-people'];
    }
    
    if (hasPermission($pdo, $userId, 'suppliers.view') || isAdmin($pdo, $userId)) {
        $pages[] = ['url' => 'suppliers.php', 'name' => 'إدارة الموردين', 'icon' => 'bi-building'];
    }
    
    if (hasPermission($pdo, $userId, 'accounting.view') || isAdmin($pdo, $userId)) {
        $pages[] = ['url' => 'accounting.php', 'name' => 'النظام المحاسبي', 'icon' => 'bi-currency-dollar'];
    }
    
    if (hasPermission($pdo, $userId, 'reports.view') || isAdmin($pdo, $userId)) {
        $pages[] = ['url' => 'reports.php', 'name' => 'التقارير والإحصائيات', 'icon' => 'bi-bar-chart'];
    }
    
    if (hasPermission($pdo, $userId, 'settings.view') || isAdmin($pdo, $userId)) {
        $pages[] = ['url' => 'settings.php', 'name' => 'إعدادات النظام', 'icon' => 'bi-gear'];
    }
    
    return $pages;
}
?>
