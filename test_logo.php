<?php
require 'db.php';
require 'get_settings.php';

// إنشاء شعار تجريبي بسيط باستخدام SVG
$logoSvg = '
<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2c3e50;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="50" cy="50" r="45" fill="url(#grad1)" stroke="#fff" stroke-width="3"/>
  <text x="50" y="35" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white" text-anchor="middle">POS</text>
  <text x="50" y="55" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">نقاط</text>
  <text x="50" y="70" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">البيع</text>
</svg>';

// حفظ الشعار كملف SVG
$logoPath = 'uploads/default_logo.svg';
file_put_contents($logoPath, $logoSvg);

// تحديث قاعدة البيانات
try {
    $stmt = $pdo->prepare("UPDATE system_settings SET setting_value = ? WHERE setting_key = 'company_logo'");
    $stmt->execute([$logoPath]);
    echo "✅ تم إنشاء شعار تجريبي بنجاح!<br>";
    echo "مسار الشعار: " . $logoPath . "<br>";
    echo '<img src="' . $logoPath . '" alt="شعار تجريبي" style="max-width: 100px;"><br>';
    echo '<a href="dashboard.php">عرض لوحة التحكم</a> | ';
    echo '<a href="login.php">صفحة تسجيل الدخول</a> | ';
    echo '<a href="settings.php">الإعدادات</a>';
} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الشعار</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            padding: 50px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>🎨 اختبار الشعار</h2>
        <p>تم إنشاء شعار تجريبي للنظام. يمكنك الآن رؤيته في:</p>
        <ul>
            <li>لوحة التحكم (الشريط الجانبي)</li>
            <li>صفحة تسجيل الدخول</li>
            <li>صفحة الإعدادات</li>
        </ul>
        
        <div class="alert alert-info">
            <strong>ملاحظة:</strong> يمكنك رفع شعار مخصص من صفحة الإعدادات في قسم "معلومات الشركة".
        </div>
        
        <h4>معاينة الشعار:</h4>
        <div class="text-center p-3 bg-light rounded">
            <?php if (file_exists($logoPath)): ?>
                <img src="<?php echo $logoPath; ?>" alt="شعار النظام" style="max-width: 150px;">
            <?php else: ?>
                <p class="text-muted">لم يتم العثور على الشعار</p>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
