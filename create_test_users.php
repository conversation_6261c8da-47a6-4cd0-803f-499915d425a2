<?php
// إنشاء مستخدمين تجريبيين لاختبار الصلاحيات
require 'db.php';
require 'check_permissions.php';

try {
    // إنشاء مستخدم كاشير
    $cashierPassword = password_hash('cashier123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, password, email, phone, created_by) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['cashier', $cashierPassword, '<EMAIL>', '0501234567', 1]);
    $cashierId = $pdo->lastInsertId();
    
    if ($cashierId) {
        // ربط الكاشير بدور الكاشير
        $cashierRoleId = $pdo->query("SELECT id FROM roles WHERE name = 'cashier'")->fetchColumn();
        if ($cashierRoleId) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by) VALUES (?, ?, ?)");
            $stmt->execute([$cashierId, $cashierRoleId, 1]);
        }
    }
    
    // إنشاء مستخدم مدير مخزون
    $inventoryPassword = password_hash('inventory123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, password, email, phone, created_by) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['inventory_manager', $inventoryPassword, '<EMAIL>', '0507654321', 1]);
    $inventoryId = $pdo->lastInsertId();
    
    if ($inventoryId) {
        // ربط مدير المخزون بدوره
        $inventoryRoleId = $pdo->query("SELECT id FROM roles WHERE name = 'inventory_manager'")->fetchColumn();
        if ($inventoryRoleId) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by) VALUES (?, ?, ?)");
            $stmt->execute([$inventoryId, $inventoryRoleId, 1]);
        }
    }
    
    // إنشاء مستخدم محاسب
    $accountantPassword = password_hash('accountant123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, password, email, phone, created_by) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['accountant', $accountantPassword, '<EMAIL>', '**********', 1]);
    $accountantId = $pdo->lastInsertId();
    
    if ($accountantId) {
        // ربط المحاسب بدوره
        $accountantRoleId = $pdo->query("SELECT id FROM roles WHERE name = 'accountant'")->fetchColumn();
        if ($accountantRoleId) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by) VALUES (?, ?, ?)");
            $stmt->execute([$accountantId, $accountantRoleId, 1]);
        }
    }
    
    // إنشاء مستخدم عادي
    $userPassword = password_hash('user123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, password, email, phone, created_by) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['normal_user', $userPassword, '<EMAIL>', '**********', 1]);
    $normalUserId = $pdo->lastInsertId();
    
    if ($normalUserId) {
        // ربط المستخدم العادي بدوره
        $userRoleId = $pdo->query("SELECT id FROM roles WHERE name = 'user'")->fetchColumn();
        if ($userRoleId) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by) VALUES (?, ?, ?)");
            $stmt->execute([$normalUserId, $userRoleId, 1]);
        }
    }
    
    // تحديث صلاحيات الأدوار
    
    // صلاحيات الكاشير
    $cashierPermissions = [
        'pos.access', 'pos.discount', 'sales.view', 'customers.view', 'customers.create', 'products.view'
    ];
    
    if ($cashierRoleId) {
        // حذف الصلاحيات الحالية
        $pdo->prepare("DELETE FROM role_permissions WHERE role_id = ?")->execute([$cashierRoleId]);
        
        // إضافة الصلاحيات الجديدة
        foreach ($cashierPermissions as $permissionName) {
            $permissionId = $pdo->prepare("SELECT id FROM permissions WHERE name = ?")->execute([$permissionName]);
            $permissionId = $pdo->query("SELECT id FROM permissions WHERE name = '$permissionName'")->fetchColumn();
            if ($permissionId) {
                $pdo->prepare("INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES (?, ?)")->execute([$cashierRoleId, $permissionId]);
            }
        }
    }
    
    // صلاحيات مدير المخزون
    $inventoryPermissions = [
        'products.view', 'products.create', 'products.edit', 'inventory.view', 'inventory.adjust', 
        'purchases.view', 'purchases.create', 'suppliers.view', 'suppliers.create'
    ];
    
    if ($inventoryRoleId) {
        // حذف الصلاحيات الحالية
        $pdo->prepare("DELETE FROM role_permissions WHERE role_id = ?")->execute([$inventoryRoleId]);
        
        // إضافة الصلاحيات الجديدة
        foreach ($inventoryPermissions as $permissionName) {
            $permissionId = $pdo->query("SELECT id FROM permissions WHERE name = '$permissionName'")->fetchColumn();
            if ($permissionId) {
                $pdo->prepare("INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES (?, ?)")->execute([$inventoryRoleId, $permissionId]);
            }
        }
    }
    
    // صلاحيات المحاسب
    $accountantPermissions = [
        'reports.view', 'reports.export', 'accounting.view', 'accounting.entries', 'sales.view'
    ];
    
    if ($accountantRoleId) {
        // حذف الصلاحيات الحالية
        $pdo->prepare("DELETE FROM role_permissions WHERE role_id = ?")->execute([$accountantRoleId]);
        
        // إضافة الصلاحيات الجديدة
        foreach ($accountantPermissions as $permissionName) {
            $permissionId = $pdo->query("SELECT id FROM permissions WHERE name = '$permissionName'")->fetchColumn();
            if ($permissionId) {
                $pdo->prepare("INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES (?, ?)")->execute([$accountantRoleId, $permissionId]);
            }
        }
    }
    
    // صلاحيات المستخدم العادي (عرض فقط)
    $userPermissions = [
        'products.view', 'sales.view', 'customers.view', 'reports.view'
    ];
    
    if ($userRoleId) {
        // حذف الصلاحيات الحالية
        $pdo->prepare("DELETE FROM role_permissions WHERE role_id = ?")->execute([$userRoleId]);
        
        // إضافة الصلاحيات الجديدة
        foreach ($userPermissions as $permissionName) {
            $permissionId = $pdo->query("SELECT id FROM permissions WHERE name = '$permissionName'")->fetchColumn();
            if ($permissionId) {
                $pdo->prepare("INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES (?, ?)")->execute([$userRoleId, $permissionId]);
            }
        }
    }
    
    echo "تم إنشاء المستخدمين التجريبيين بنجاح!\n\n";
    echo "المستخدمين المتاحين:\n";
    echo "1. admin / admin123 - مدير النظام (جميع الصلاحيات)\n";
    echo "2. cashier / cashier123 - كاشير (نقطة البيع والمبيعات)\n";
    echo "3. inventory_manager / inventory123 - مدير المخزون (المنتجات والمخزون)\n";
    echo "4. accountant / accountant123 - محاسب (التقارير والمحاسبة)\n";
    echo "5. normal_user / user123 - مستخدم عادي (عرض فقط)\n\n";
    echo "يمكنك الآن اختبار النظام بتسجيل الدخول بأي من هذه الحسابات!\n";
    
} catch (PDOException $e) {
    echo "خطأ في إنشاء المستخدمين: " . $e->getMessage() . "\n";
}
?>
